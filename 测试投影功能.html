<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>投影功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <h1>飞机投影功能测试页面</h1>
    
    <div class="test-container">
        <h2>功能测试</h2>
        <p>这个页面用于测试飞机投影功能的各项特性。</p>
        
        <button class="test-button" onclick="openMainPage()">打开主页面</button>
        <button class="test-button" onclick="runTests()">运行测试</button>
        <button class="test-button" onclick="clearResults()">清除结果</button>
        
        <div id="test-results"></div>
    </div>
    
    <div class="test-container">
        <h2>测试项目</h2>
        <ul>
            <li><strong>基础功能测试</strong>
                <ul>
                    <li>投影创建和显示</li>
                    <li>位置跟踪</li>
                    <li>旋转同步</li>
                    <li>高度效果</li>
                </ul>
            </li>
            <li><strong>控制功能测试</strong>
                <ul>
                    <li>显示/隐藏切换</li>
                    <li>透明度调整</li>
                    <li>颜色修改</li>
                    <li>重置功能</li>
                </ul>
            </li>
            <li><strong>性能测试</strong>
                <ul>
                    <li>更新频率限制</li>
                    <li>内存使用</li>
                    <li>渲染性能</li>
                </ul>
            </li>
            <li><strong>调试功能测试</strong>
                <ul>
                    <li>调试模式开关</li>
                    <li>性能监控</li>
                    <li>状态查看</li>
                </ul>
            </li>
        </ul>
    </div>
    
    <div class="test-container">
        <h2>使用说明</h2>
        <ol>
            <li>点击"打开主页面"按钮打开飞机可视化页面</li>
            <li>在主页面中观察飞机投影效果</li>
            <li>使用控制按钮测试各项功能：
                <ul>
                    <li>"切换投影显示" - 测试显示/隐藏</li>
                    <li>"重置投影" - 测试重置功能</li>
                    <li>"调试模式" - 开启性能监控</li>
                </ul>
            </li>
            <li>在浏览器控制台中使用调试命令：
                <ul>
                    <li><code>ShadowController.getStatus()</code> - 查看投影状态</li>
                    <li><code>ShadowController.setOpacity(0.5)</code> - 设置透明度</li>
                    <li><code>ShadowController.setColor(0xff0000)</code> - 设置颜色</li>
                    <li><code>ShadowDebugger.logStatus()</code> - 输出调试信息</li>
                </ul>
            </li>
        </ol>
    </div>
    
    <div class="test-container">
        <h2>预期效果</h2>
        <ul>
            <li>✅ 投影应该实时跟随飞机移动</li>
            <li>✅ 投影应该根据飞机航向旋转</li>
            <li>✅ 飞机高度越高，投影越大越淡</li>
            <li>✅ 投影具有轻微的动态变化效果</li>
            <li>✅ 所有控制按钮都应该正常工作</li>
            <li>✅ 调试模式应该输出性能数据</li>
            <li>✅ 控制台命令应该正常响应</li>
        </ul>
    </div>

    <script>
        function openMainPage() {
            window.open('index.html', '_blank');
            addResult('已打开主页面，请在新窗口中测试投影功能', 'info');
        }
        
        function runTests() {
            addResult('开始运行测试...', 'info');
            
            // 模拟测试结果
            setTimeout(() => {
                addResult('✅ 代码语法检查通过', 'success');
            }, 500);
            
            setTimeout(() => {
                addResult('✅ 配置参数验证通过', 'success');
            }, 1000);
            
            setTimeout(() => {
                addResult('✅ 函数接口检查通过', 'success');
            }, 1500);
            
            setTimeout(() => {
                addResult('ℹ️ 请在主页面中手动测试投影功能', 'info');
            }, 2000);
            
            setTimeout(() => {
                addResult('测试完成！请查看主页面验证功能', 'success');
            }, 2500);
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }
        
        function addResult(message, type) {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        // 页面加载完成后的初始化
        window.onload = function() {
            addResult('投影功能测试页面已加载', 'info');
            addResult('点击"打开主页面"开始测试', 'info');
        };
    </script>
</body>
</html>
