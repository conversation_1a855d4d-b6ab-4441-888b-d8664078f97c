<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>无人机图标测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .icon-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .icon-preview img {
            max-width: 100px;
            max-height: 100px;
            display: block;
            margin: 0 auto 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>无人机图标测试页面</h1>
    
    <div class="test-container">
        <h2>图标预览</h2>
        <p>检查无人机图标是否正确加载：</p>
        
        <div class="icon-preview">
            <img id="drone1-png" src="./img/drone1.png" alt="Drone 1 PNG" onerror="showError(this, 'drone1.png')">
            <div>drone1.png</div>
        </div>
        
        <div class="icon-preview">
            <img id="drone1-svg" src="./img/drone1.svg" alt="Drone 1 SVG" onerror="showError(this, 'drone1.svg')">
            <div>drone1.svg</div>
        </div>
        
        <div class="icon-preview">
            <img id="drone2-png" src="./img/drone2.png" alt="Drone 2 PNG" onerror="showError(this, 'drone2.png')">
            <div>drone2.png</div>
        </div>
        
        <div class="icon-preview">
            <img id="drone2-svg" src="./img/drone2.svg" alt="Drone 2 SVG" onerror="showError(this, 'drone2.svg')">
            <div>drone2.svg</div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>加载状态</h2>
        <div id="status-container">
            <div class="status info">正在检查图标文件...</div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>投影效果预览</h2>
        <p>不同尺寸的投影效果（模拟近大远小）：</p>
        
        <div style="text-align: center; background: #333; padding: 20px; border-radius: 4px;">
            <div style="color: white; margin-bottom: 10px;">近处（大）</div>
            <img src="./img/drone1.png" style="width: 120px; opacity: 0.8;" alt="近处效果">
            
            <div style="color: white; margin: 20px 0 10px;">中等距离</div>
            <img src="./img/drone1.png" style="width: 80px; opacity: 0.6;" alt="中等距离效果">
            
            <div style="color: white; margin: 20px 0 10px;">远处（小）</div>
            <img src="./img/drone1.png" style="width: 40px; opacity: 0.4;" alt="远处效果">
        </div>
    </div>
    
    <div class="test-container">
        <h2>使用说明</h2>
        <ol>
            <li>确认上方的图标都能正常显示</li>
            <li>如果有图标显示为破损图片，说明文件不存在或路径错误</li>
            <li>投影效果预览展示了近大远小的视觉效果</li>
            <li>确认无误后，可以打开主页面测试投影功能</li>
        </ol>
        
        <button onclick="openMainPage()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
            打开主页面测试
        </button>
    </div>

    <script>
        let loadedCount = 0;
        let totalCount = 4;
        let errors = [];

        function showError(img, filename) {
            errors.push(filename);
            img.style.border = '2px solid red';
            img.alt = '加载失败: ' + filename;
            img.title = '文件不存在或无法加载';
            updateStatus();
        }

        function showSuccess(img, filename) {
            loadedCount++;
            img.style.border = '2px solid green';
            updateStatus();
        }

        function updateStatus() {
            const container = document.getElementById('status-container');
            container.innerHTML = '';

            if (errors.length > 0) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'status error';
                errorDiv.textContent = `❌ 以下图标加载失败: ${errors.join(', ')}`;
                container.appendChild(errorDiv);
            }

            if (loadedCount > 0) {
                const successDiv = document.createElement('div');
                successDiv.className = 'status success';
                successDiv.textContent = `✅ 已成功加载 ${loadedCount} 个图标`;
                container.appendChild(successDiv);
            }

            if (loadedCount + errors.length === totalCount) {
                const completeDiv = document.createElement('div');
                completeDiv.className = 'status info';
                if (errors.length === 0) {
                    completeDiv.textContent = '🎉 所有图标加载完成，可以正常使用投影功能！';
                } else {
                    completeDiv.textContent = '⚠️ 部分图标加载失败，建议检查文件是否存在';
                }
                container.appendChild(completeDiv);
            }
        }

        function openMainPage() {
            window.open('index.html', '_blank');
        }

        // 为所有图片添加加载成功事件
        window.onload = function() {
            const images = document.querySelectorAll('.icon-preview img');
            images.forEach(img => {
                if (img.complete && img.naturalHeight !== 0) {
                    showSuccess(img, img.src.split('/').pop());
                } else {
                    img.onload = function() {
                        showSuccess(this, this.src.split('/').pop());
                    };
                }
            });

            // 延迟检查，确保所有图片都有机会加载
            setTimeout(() => {
                if (loadedCount + errors.length < totalCount) {
                    const remaining = totalCount - loadedCount - errors.length;
                    const statusDiv = document.createElement('div');
                    statusDiv.className = 'status info';
                    statusDiv.textContent = `⏳ 还有 ${remaining} 个图标正在加载中...`;
                    document.getElementById('status-container').appendChild(statusDiv);
                }
            }, 2000);
        };
    </script>
</body>
</html>
