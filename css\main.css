html,
body,
#container {
    width: 100%;
    height: 100%;
    z-index: 0;
}
/* 控制菜单优化 */
#menu {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 280px;
    background: linear-gradient(135deg, rgba(20, 20, 20, 0.95), rgba(40, 40, 40, 0.9));
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
}

#menu button {
    width: 100%;
    padding: 15px 20px;
    margin: 8px 0;
    background: linear-gradient(135deg, #00d4ff, #0099cc);
    border: none;
    border-radius: 10px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

#menu button:hover {
    background: linear-gradient(135deg, #00b8e6, #0088bb);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

#menu hr {
    border: none;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    margin: 15px 0;
}
#menu:hover{
    background-color: #09071199;
    color: #fff;
    transition: 0.5s;
}



/*
* jQuery Flight Indicators plugin
* By Sébastien Matton (<EMAIL>)
* Published under GPLv3 License.
*
* https://github.com/sebmatton/jQuery-Flight-Indicators
*/
/* Global block of an indicator*/
div.instrument {
    width: 120px;
    height: 120px;
    position: relative;
    display: inline-block;
    overflow: hidden;
    z-index: 999;
    transition: transform 0.3s ease;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

div.instrument:hover {
    transform: scale(1.05);
}
  /* The box containing any element of an indicator */
  div.instrument .box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index:999;
  }

  /* Default transformations */
  div.instrument.attitude div.roll {
    transform: rotate(0deg);
  }
  div.instrument.attitude div.roll div.pitch {
    top: 0%;
  }
  div.instrument.heading div.yaw {
    transform: rotate(0deg);
  }
  div.instrument.vario div.vario {
    transform: rotate(0deg);
  }
  div.instrument.speed div.airspeed {
    transform: rotate(90deg);
  }
  div.instrument.altimeter div.pressure {
    transform: rotate(40deg);
  }
  div.instrument.altimeter div.needle {
    transform: rotate(90deg);
  }
  div.instrument.altimeter div.needleSmall {
    transform: rotate(90deg);
  }
  .contain {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: linear-gradient(135deg, rgba(0, 20, 40, 0.9), rgba(0, 40, 80, 0.8));
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  .examples {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    align-items: center;
  }
