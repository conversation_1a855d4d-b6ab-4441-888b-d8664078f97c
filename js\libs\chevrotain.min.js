/*! chevrotain - v9.0.1 */
/*! For license information please see chevrotain.min.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("chevrotain",[],e):"object"==typeof exports?exports.chevrotain=e():t.chevrotain=e()}("undefined"!=typeof self?self:this,(function(){return(()=>{var t={844:function(t,e){var n,r;"undefined"!=typeof self&&self,void 0===(r="function"==typeof(n=function(){function t(){}t.prototype.saveState=function(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}},t.prototype.restoreState=function(t){this.idx=t.idx,this.input=t.input,this.groupIdx=t.groupIdx},t.prototype.pattern=function(t){this.idx=0,this.input=t,this.groupIdx=0,this.consumeChar("/");var e=this.disjunction();this.consumeChar("/");for(var n={type:"Flags",loc:{begin:this.idx,end:t.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};this.isRegExpFlag();)switch(this.popChar()){case"g":s(n,"global");break;case"i":s(n,"ignoreCase");break;case"m":s(n,"multiLine");break;case"u":s(n,"unicode");break;case"y":s(n,"sticky")}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:n,value:e,loc:this.loc(0)}},t.prototype.disjunction=function(){var t=[],e=this.idx;for(t.push(this.alternative());"|"===this.peekChar();)this.consumeChar("|"),t.push(this.alternative());return{type:"Disjunction",value:t,loc:this.loc(e)}},t.prototype.alternative=function(){for(var t=[],e=this.idx;this.isTerm();)t.push(this.term());return{type:"Alternative",value:t,loc:this.loc(e)}},t.prototype.term=function(){return this.isAssertion()?this.assertion():this.atom()},t.prototype.assertion=function(){var t=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(t)};case"$":return{type:"EndAnchor",loc:this.loc(t)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(t)};case"B":return{type:"NonWordBoundary",loc:this.loc(t)}}throw Error("Invalid Assertion Escape");case"(":var e;switch(this.consumeChar("?"),this.popChar()){case"=":e="Lookahead";break;case"!":e="NegativeLookahead"}c(e);var n=this.disjunction();return this.consumeChar(")"),{type:e,value:n,loc:this.loc(t)}}!function(){throw Error("Internal Error - Should never get here!")}()},t.prototype.quantifier=function(t){var e,n=this.idx;switch(this.popChar()){case"*":e={atLeast:0,atMost:1/0};break;case"+":e={atLeast:1,atMost:1/0};break;case"?":e={atLeast:0,atMost:1};break;case"{":var r=this.integerIncludingZero();switch(this.popChar()){case"}":e={atLeast:r,atMost:r};break;case",":e=this.isDigit()?{atLeast:r,atMost:this.integerIncludingZero()}:{atLeast:r,atMost:1/0},this.consumeChar("}")}if(!0===t&&void 0===e)return;c(e)}if(!0!==t||void 0!==e)return c(e),"?"===this.peekChar(0)?(this.consumeChar("?"),e.greedy=!1):e.greedy=!0,e.type="Quantifier",e.loc=this.loc(n),e},t.prototype.atom=function(){var t,e=this.idx;switch(this.peekChar()){case".":t=this.dotAll();break;case"\\":t=this.atomEscape();break;case"[":t=this.characterClass();break;case"(":t=this.group()}return void 0===t&&this.isPatternCharacter()&&(t=this.patternCharacter()),c(t),t.loc=this.loc(e),this.isQuantifier()&&(t.quantifier=this.quantifier()),t},t.prototype.dotAll=function(){return this.consumeChar("."),{type:"Set",complement:!0,value:[o("\n"),o("\r"),o("\u2028"),o("\u2029")]}},t.prototype.atomEscape=function(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}},t.prototype.decimalEscapeAtom=function(){return{type:"GroupBackReference",value:this.positiveInteger()}},t.prototype.characterClassEscape=function(){var t,e=!1;switch(this.popChar()){case"d":t=u;break;case"D":t=u,e=!0;break;case"s":t=p;break;case"S":t=p,e=!0;break;case"w":t=l;break;case"W":t=l,e=!0}return c(t),{type:"Set",value:t,complement:e}},t.prototype.controlEscapeAtom=function(){var t;switch(this.popChar()){case"f":t=o("\f");break;case"n":t=o("\n");break;case"r":t=o("\r");break;case"t":t=o("\t");break;case"v":t=o("\v")}return c(t),{type:"Character",value:t}},t.prototype.controlLetterEscapeAtom=function(){this.consumeChar("c");var t=this.popChar();if(!1===/[a-zA-Z]/.test(t))throw Error("Invalid ");return{type:"Character",value:t.toUpperCase().charCodeAt(0)-64}},t.prototype.nulCharacterAtom=function(){return this.consumeChar("0"),{type:"Character",value:o("\0")}},t.prototype.hexEscapeSequenceAtom=function(){return this.consumeChar("x"),this.parseHexDigits(2)},t.prototype.regExpUnicodeEscapeSequenceAtom=function(){return this.consumeChar("u"),this.parseHexDigits(4)},t.prototype.identityEscapeAtom=function(){return{type:"Character",value:o(this.popChar())}},t.prototype.classPatternCharacterAtom=function(){switch(this.peekChar()){case"\n":case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:return{type:"Character",value:o(this.popChar())}}},t.prototype.characterClass=function(){var t=[],e=!1;for(this.consumeChar("["),"^"===this.peekChar(0)&&(this.consumeChar("^"),e=!0);this.isClassAtom();){var n=this.classAtom();if("Character"===n.type&&this.isRangeDash()){this.consumeChar("-");var r=this.classAtom();if("Character"===r.type){if(r.value<n.value)throw Error("Range out of order in character class");t.push({from:n.value,to:r.value})}else a(n.value,t),t.push(o("-")),a(r.value,t)}else a(n.value,t)}return this.consumeChar("]"),{type:"Set",complement:e,value:t}},t.prototype.classAtom=function(){switch(this.peekChar()){case"]":case"\n":case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}},t.prototype.classEscape=function(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:o("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}},t.prototype.group=function(){var t=!0;switch(this.consumeChar("("),this.peekChar(0)){case"?":this.consumeChar("?"),this.consumeChar(":"),t=!1;break;default:this.groupIdx++}var e=this.disjunction();this.consumeChar(")");var n={type:"Group",capturing:t,value:e};return t&&(n.idx=this.groupIdx),n},t.prototype.positiveInteger=function(){var t=this.popChar();if(!1===i.test(t))throw Error("Expecting a positive integer");for(;r.test(this.peekChar(0));)t+=this.popChar();return parseInt(t,10)},t.prototype.integerIncludingZero=function(){var t=this.popChar();if(!1===r.test(t))throw Error("Expecting an integer");for(;r.test(this.peekChar(0));)t+=this.popChar();return parseInt(t,10)},t.prototype.patternCharacter=function(){var t=this.popChar();switch(t){case"\n":case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:o(t)}}},t.prototype.isRegExpFlag=function(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}},t.prototype.isRangeDash=function(){return"-"===this.peekChar()&&this.isClassAtom(1)},t.prototype.isDigit=function(){return r.test(this.peekChar(0))},t.prototype.isClassAtom=function(t){switch(void 0===t&&(t=0),this.peekChar(t)){case"]":case"\n":case"\r":case"\u2028":case"\u2029":return!1;default:return!0}},t.prototype.isTerm=function(){return this.isAtom()||this.isAssertion()},t.prototype.isAtom=function(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}},t.prototype.isAssertion=function(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return"?"===this.peekChar(1)&&("="===this.peekChar(2)||"!"===this.peekChar(2));default:return!1}},t.prototype.isQuantifier=function(){var t=this.saveState();try{return void 0!==this.quantifier(!0)}catch(t){return!1}finally{this.restoreState(t)}},t.prototype.isPatternCharacter=function(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case"\n":case"\r":case"\u2028":case"\u2029":return!1;default:return!0}},t.prototype.parseHexDigits=function(t){for(var e="",r=0;r<t;r++){var i=this.popChar();if(!1===n.test(i))throw Error("Expecting a HexDecimal digits");e+=i}return{type:"Character",value:parseInt(e,16)}},t.prototype.peekChar=function(t){return void 0===t&&(t=0),this.input[this.idx+t]},t.prototype.popChar=function(){var t=this.peekChar(0);return this.consumeChar(),t},t.prototype.consumeChar=function(t){if(void 0!==t&&this.input[this.idx]!==t)throw Error("Expected: '"+t+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++},t.prototype.loc=function(t){return{begin:t,end:this.idx}};var e,n=/[0-9a-fA-F]/,r=/[0-9]/,i=/[1-9]/;function o(t){return t.charCodeAt(0)}function a(t,e){void 0!==t.length?t.forEach((function(t){e.push(t)})):e.push(t)}function s(t,e){if(!0===t[e])throw"duplicate flag "+e;t[e]=!0}function c(t){if(void 0===t)throw Error("Internal Error - Should never get here!")}var u=[];for(e=o("0");e<=o("9");e++)u.push(e);var l=[o("_")].concat(u);for(e=o("a");e<=o("z");e++)l.push(e);for(e=o("A");e<=o("Z");e++)l.push(e);var p=[o(" "),o("\f"),o("\n"),o("\r"),o("\t"),o("\v"),o("\t"),o(" "),o(" "),o(" "),o(" "),o(" "),o(" "),o(" "),o(" "),o(" "),o(" "),o(" "),o(" "),o(" "),o("\u2028"),o("\u2029"),o(" "),o(" "),o("　"),o("\ufeff")];function f(){}return f.prototype.visitChildren=function(t){for(var e in t){var n=t[e];t.hasOwnProperty(e)&&(void 0!==n.type?this.visit(n):Array.isArray(n)&&n.forEach((function(t){this.visit(t)}),this))}},f.prototype.visit=function(t){switch(t.type){case"Pattern":this.visitPattern(t);break;case"Flags":this.visitFlags(t);break;case"Disjunction":this.visitDisjunction(t);break;case"Alternative":this.visitAlternative(t);break;case"StartAnchor":this.visitStartAnchor(t);break;case"EndAnchor":this.visitEndAnchor(t);break;case"WordBoundary":this.visitWordBoundary(t);break;case"NonWordBoundary":this.visitNonWordBoundary(t);break;case"Lookahead":this.visitLookahead(t);break;case"NegativeLookahead":this.visitNegativeLookahead(t);break;case"Character":this.visitCharacter(t);break;case"Set":this.visitSet(t);break;case"Group":this.visitGroup(t);break;case"GroupBackReference":this.visitGroupBackReference(t);break;case"Quantifier":this.visitQuantifier(t)}this.visitChildren(t)},f.prototype.visitPattern=function(t){},f.prototype.visitFlags=function(t){},f.prototype.visitDisjunction=function(t){},f.prototype.visitAlternative=function(t){},f.prototype.visitStartAnchor=function(t){},f.prototype.visitEndAnchor=function(t){},f.prototype.visitWordBoundary=function(t){},f.prototype.visitNonWordBoundary=function(t){},f.prototype.visitLookahead=function(t){},f.prototype.visitNegativeLookahead=function(t){},f.prototype.visitCharacter=function(t){},f.prototype.visitSet=function(t){},f.prototype.visitGroup=function(t){},f.prototype.visitGroupBackReference=function(t){},f.prototype.visitQuantifier=function(t){},{RegExpParser:t,BaseRegExpVisitor:f,VERSION:"0.5.0"}})?n.apply(e,[]):n)||(t.exports=r)},781:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createSyntaxDiagramsCode=void 0;var r=n(979);e.createSyntaxDiagramsCode=function(t,e){var n=void 0===e?{}:e,i=n.resourceBase,o=void 0===i?"https://unpkg.com/chevrotain@"+r.VERSION+"/diagrams/":i,a=n.css;return"\n\x3c!-- This is a generated file --\x3e\n<!DOCTYPE html>\n<meta charset=\"utf-8\">\n<style>\n  body {\n    background-color: hsl(30, 20%, 95%)\n  }\n</style>\n\n\n<link rel='stylesheet' href='"+(void 0===a?"https://unpkg.com/chevrotain@"+r.VERSION+"/diagrams/diagrams.css":a)+"'>\n\n<script src='"+o+"vendor/railroad-diagrams.js'><\/script>\n<script src='"+o+"src/diagrams_builder.js'><\/script>\n<script src='"+o+"src/diagrams_behavior.js'><\/script>\n<script src='"+o+'src/main.js\'><\/script>\n\n<div id="diagrams" align="center"></div>    \n\n<script>\n    window.serializedGrammar = '+JSON.stringify(t,null,"  ")+';\n<\/script>\n\n<script>\n    var diagramsDiv = document.getElementById("diagrams");\n    main.drawDiagramsFromSerializedGrammar(serializedGrammar, diagramsDiv);\n<\/script>\n'}},105:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.defineNameProp=e.functionName=e.classNameFromInstance=void 0;var r=n(146);e.classNameFromInstance=function(t){return o(t.constructor)};var i="name";function o(t){return t.name||"anonymous"}e.functionName=o,e.defineNameProp=function(t,e){var n=Object.getOwnPropertyDescriptor(t,i);return!(!r.isUndefined(n)&&!n.configurable||(Object.defineProperty(t,i,{enumerable:!1,configurable:!0,writable:!1,value:e}),0))}},710:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.IN=void 0,e.IN="_~IN~_"},485:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.addNoneTerminalToCst=e.addTerminalToCst=e.setNodeLocationFull=e.setNodeLocationOnlyOffset=void 0,e.setNodeLocationOnlyOffset=function(t,e){!0===isNaN(t.startOffset)?(t.startOffset=e.startOffset,t.endOffset=e.endOffset):t.endOffset<e.endOffset==1&&(t.endOffset=e.endOffset)},e.setNodeLocationFull=function(t,e){!0===isNaN(t.startOffset)?(t.startOffset=e.startOffset,t.startColumn=e.startColumn,t.startLine=e.startLine,t.endOffset=e.endOffset,t.endColumn=e.endColumn,t.endLine=e.endLine):t.endOffset<e.endOffset==1&&(t.endOffset=e.endOffset,t.endColumn=e.endColumn,t.endLine=e.endLine)},e.addTerminalToCst=function(t,e,n){void 0===t.children[n]?t.children[n]=[e]:t.children[n].push(e)},e.addNoneTerminalToCst=function(t,e,n){void 0===t.children[e]?t.children[e]=[n]:t.children[e].push(n)}},169:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.validateRedundantMethods=e.validateMissingCstMethods=e.validateVisitor=e.CstVisitorDefinitionError=e.createBaseVisitorConstructorWithDefaults=e.createBaseSemanticVisitorConstructor=e.defaultVisit=void 0;var r,i=n(146),o=n(105);function a(t,e){for(var n=i.keys(t),r=n.length,o=0;o<r;o++)for(var a=t[n[o]],s=a.length,c=0;c<s;c++){var u=a[c];void 0===u.tokenTypeIdx&&this[u.name](u.children,e)}}function s(t,e){var n=c(t,e),r=l(t,e);return n.concat(r)}function c(t,e){var n=i.map(e,(function(e){if(!i.isFunction(t[e]))return{msg:"Missing visitor method: <"+e+"> on "+o.functionName(t.constructor)+" CST Visitor.",type:r.MISSING_METHOD,methodName:e}}));return i.compact(n)}e.defaultVisit=a,e.createBaseSemanticVisitorConstructor=function(t,e){var n=function(){};return o.defineNameProp(n,t+"BaseSemantics"),(n.prototype={visit:function(t,e){if(i.isArray(t)&&(t=t[0]),!i.isUndefined(t))return this[t.name](t.children,e)},validateVisitor:function(){var t=s(this,e);if(!i.isEmpty(t)){var n=i.map(t,(function(t){return t.msg}));throw Error("Errors Detected in CST Visitor <"+o.functionName(this.constructor)+">:\n\t"+n.join("\n\n").replace(/\n/g,"\n\t"))}}}).constructor=n,n._RULE_NAMES=e,n},e.createBaseVisitorConstructorWithDefaults=function(t,e,n){var r=function(){};o.defineNameProp(r,t+"BaseSemanticsWithDefaults");var s=Object.create(n.prototype);return i.forEach(e,(function(t){s[t]=a})),(r.prototype=s).constructor=r,r},function(t){t[t.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",t[t.MISSING_METHOD=1]="MISSING_METHOD"}(r=e.CstVisitorDefinitionError||(e.CstVisitorDefinitionError={})),e.validateVisitor=s,e.validateMissingCstMethods=c;var u=["constructor","visit","validateVisitor"];function l(t,e){var n=[];for(var a in t)!i.isFunction(t[a])||i.contains(u,a)||i.contains(e,a)||n.push({msg:"Redundant visitor method: <"+a+"> on "+o.functionName(t.constructor)+" CST Visitor\nThere is no Grammar Rule corresponding to this method's name.\n",type:r.REDUNDANT_METHOD,methodName:a});return n}e.validateRedundantMethods=l},7:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.defaultGrammarValidatorErrorProvider=e.defaultGrammarResolverErrorProvider=e.defaultParserErrorProvider=void 0;var r=n(736),i=n(146),o=n(146),a=n(946),s=n(651);e.defaultParserErrorProvider={buildMismatchTokenMessage:function(t){var e=t.expected,n=t.actual;return t.previous,t.ruleName,"Expecting "+(r.hasTokenLabel(e)?"--\x3e "+r.tokenLabel(e)+" <--":"token of type --\x3e "+e.name+" <--")+" but found --\x3e '"+n.image+"' <--"},buildNotAllInputParsedMessage:function(t){var e=t.firstRedundant;return t.ruleName,"Redundant input, expecting EOF but found: "+e.image},buildNoViableAltMessage:function(t){var e=t.expectedPathsPerAlt,n=t.actual,i=(t.previous,t.customUserDescription),a=(t.ruleName,"Expecting: "),s="\nbut found: '"+o.first(n).image+"'";if(i)return a+i+s;var c=o.reduce(e,(function(t,e){return t.concat(e)}),[]),u=o.map(c,(function(t){return"["+o.map(t,(function(t){return r.tokenLabel(t)})).join(", ")+"]"}));return a+"one of these possible Token sequences:\n"+o.map(u,(function(t,e){return"  "+(e+1)+". "+t})).join("\n")+s},buildEarlyExitMessage:function(t){var e=t.expectedIterationPaths,n=t.actual,i=t.customUserDescription,a=(t.ruleName,"Expecting: "),s="\nbut found: '"+o.first(n).image+"'";return i?a+i+s:a+"expecting at least one iteration which starts with one of these possible Token sequences::\n  <"+o.map(e,(function(t){return"["+o.map(t,(function(t){return r.tokenLabel(t)})).join(",")+"]"})).join(" ,")+">"+s}},Object.freeze(e.defaultParserErrorProvider),e.defaultGrammarResolverErrorProvider={buildRuleNotFoundError:function(t,e){return"Invalid grammar, reference to a rule which is not defined: ->"+e.nonTerminalName+"<-\ninside top level rule: ->"+t.name+"<-"}},e.defaultGrammarValidatorErrorProvider={buildDuplicateFoundError:function(t,e){var n,r=t.name,i=o.first(e),c=i.idx,u=s.getProductionDslName(i),l=(n=i)instanceof a.Terminal?n.terminalType.name:n instanceof a.NonTerminal?n.nonTerminalName:"",p="->"+u+(c>0?c:"")+"<- "+(l?"with argument: ->"+l+"<-":"")+"\n                  appears more than once ("+e.length+" times) in the top level rule: ->"+r+"<-.                  \n                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES \n                  ";return(p=p.replace(/[ \t]+/g," ")).replace(/\s\s+/g,"\n")},buildNamespaceConflictError:function(t){return"Namespace conflict found in grammar.\nThe grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <"+t.name+">.\nTo resolve this make sure each Terminal and Non-Terminal names are unique\nThis is easy to accomplish by using the convention that Terminal names start with an uppercase letter\nand Non-Terminal names start with a lower case letter."},buildAlternationPrefixAmbiguityError:function(t){var e=o.map(t.prefixPath,(function(t){return r.tokenLabel(t)})).join(", "),n=0===t.alternation.idx?"":t.alternation.idx;return"Ambiguous alternatives: <"+t.ambiguityIndices.join(" ,")+"> due to common lookahead prefix\nin <OR"+n+"> inside <"+t.topLevelRule.name+"> Rule,\n<"+e+"> may appears as a prefix path in all these alternatives.\nSee: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX\nFor Further details."},buildAlternationAmbiguityError:function(t){var e=o.map(t.prefixPath,(function(t){return r.tokenLabel(t)})).join(", "),n=0===t.alternation.idx?"":t.alternation.idx;return"Ambiguous Alternatives Detected: <"+t.ambiguityIndices.join(" ,")+"> in <OR"+n+"> inside <"+t.topLevelRule.name+"> Rule,\n<"+e+"> may appears as a prefix path in all these alternatives.\n"+"See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES\nFor Further details."},buildEmptyRepetitionError:function(t){var e=s.getProductionDslName(t.repetition);return 0!==t.repetition.idx&&(e+=t.repetition.idx),"The repetition <"+e+"> within Rule <"+t.topLevelRule.name+"> can never consume any tokens.\nThis could lead to an infinite loop."},buildTokenNameError:function(t){return"deprecated"},buildEmptyAlternationError:function(t){return"Ambiguous empty alternative: <"+(t.emptyChoiceIdx+1)+"> in <OR"+t.alternation.idx+"> inside <"+t.topLevelRule.name+"> Rule.\nOnly the last alternative may be an empty alternative."},buildTooManyAlternativesError:function(t){return"An Alternation cannot have more than 256 alternatives:\n<OR"+t.alternation.idx+"> inside <"+t.topLevelRule.name+"> Rule.\n has "+(t.alternation.definition.length+1)+" alternatives."},buildLeftRecursionError:function(t){var e=t.topLevelRule.name;return"Left Recursion found in grammar.\nrule: <"+e+"> can be invoked from itself (directly or indirectly)\nwithout consuming any Tokens. The grammar path that causes this is: \n "+e+" --\x3e "+i.map(t.leftRecursionPath,(function(t){return t.name})).concat([e]).join(" --\x3e ")+"\n To fix this refactor your grammar to remove the left recursion.\nsee: https://en.wikipedia.org/wiki/LL_parser#Left_Factoring."},buildInvalidRuleNameError:function(t){return"deprecated"},buildDuplicateRuleNameError:function(t){return"Duplicate definition, rule: ->"+(t.topLevelRule instanceof a.Rule?t.topLevelRule.name:t.topLevelRule)+"<- is already defined in the grammar: ->"+t.grammarName+"<-"}}},643:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0}),e.EarlyExitException=e.NotAllInputParsedException=e.NoViableAltException=e.MismatchedTokenException=e.isRecognitionException=void 0;var o=n(146),a="MismatchedTokenException",s="NoViableAltException",c="EarlyExitException",u="NotAllInputParsedException",l=[a,s,c,u];Object.freeze(l),e.isRecognitionException=function(t){return o.contains(l,t.name)};var p=function(t){function e(e,n){var r=this.constructor,i=t.call(this,e)||this;return i.token=n,i.resyncedTokens=[],Object.setPrototypeOf(i,r.prototype),Error.captureStackTrace&&Error.captureStackTrace(i,i.constructor),i}return i(e,t),e}(Error),f=function(t){function e(e,n,r){var i=t.call(this,e,n)||this;return i.previousToken=r,i.name=a,i}return i(e,t),e}(p);e.MismatchedTokenException=f;var h=function(t){function e(e,n,r){var i=t.call(this,e,n)||this;return i.previousToken=r,i.name=s,i}return i(e,t),e}(p);e.NoViableAltException=h;var d=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.name=u,r}return i(e,t),e}(p);e.NotAllInputParsedException=d;var E=function(t){function e(e,n,r){var i=t.call(this,e,n)||this;return i.previousToken=r,i.name=c,i}return i(e,t),e}(p);e.EarlyExitException=E},870:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0}),e.checkPrefixAlternativesAmbiguities=e.validateSomeNonEmptyLookaheadPath=e.validateTooManyAlts=e.RepetionCollector=e.validateAmbiguousAlternationAlternatives=e.validateEmptyOrAlternative=e.getFirstNoneTerminal=e.validateNoLeftRecursion=e.validateRuleIsOverridden=e.validateRuleDoesNotAlreadyExist=e.OccurrenceValidationCollector=e.identifyProductionForDuplicates=e.validateGrammar=void 0;var o=n(146),a=n(146),s=n(941),c=n(651),u=n(677),l=n(985),p=n(946),f=n(841);function h(t){return c.getProductionDslName(t)+"_#_"+t.idx+"_#_"+d(t)}function d(t){return t instanceof p.Terminal?t.terminalType.name:t instanceof p.NonTerminal?t.nonTerminalName:""}e.validateGrammar=function(t,e,n,r,i){var u=o.map(t,(function(t){return function(t,e){var n=new E;t.accept(n);var r=n.allProductions,i=o.groupBy(r,h),a=o.pick(i,(function(t){return t.length>1}));return o.map(o.values(a),(function(n){var r=o.first(n),i=e.buildDuplicateFoundError(t,n),a=c.getProductionDslName(r),u={message:i,type:s.ParserDefinitionErrorType.DUPLICATE_PRODUCTIONS,ruleName:t.name,dslName:a,occurrence:r.idx},l=d(r);return l&&(u.parameter=l),u}))}(t,r)})),l=o.map(t,(function(t){return y(t,t,r)})),p=[],f=[],T=[];a.every(l,a.isEmpty)&&(p=a.map(t,(function(t){return R(t,r)})),f=a.map(t,(function(t){return O(t,e,r)})),T=_(t,e,r));var v=function(t,e,n){var r=[],i=a.map(e,(function(t){return t.name}));return a.forEach(t,(function(t){var e=t.name;if(a.contains(i,e)){var o=n.buildNamespaceConflictError(t);r.push({message:o,type:s.ParserDefinitionErrorType.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:e})}})),r}(t,n,r),g=a.map(t,(function(t){return A(t,r)})),N=a.map(t,(function(e){return m(e,t,i,r)}));return o.flatten(u.concat(T,l,p,f,v,g,N))},e.identifyProductionForDuplicates=h;var E=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.allProductions=[],e}return i(e,t),e.prototype.visitNonTerminal=function(t){this.allProductions.push(t)},e.prototype.visitOption=function(t){this.allProductions.push(t)},e.prototype.visitRepetitionWithSeparator=function(t){this.allProductions.push(t)},e.prototype.visitRepetitionMandatory=function(t){this.allProductions.push(t)},e.prototype.visitRepetitionMandatoryWithSeparator=function(t){this.allProductions.push(t)},e.prototype.visitRepetition=function(t){this.allProductions.push(t)},e.prototype.visitAlternation=function(t){this.allProductions.push(t)},e.prototype.visitTerminal=function(t){this.allProductions.push(t)},e}(f.GAstVisitor);function m(t,e,n,r){var i=[];if(a.reduce(e,(function(e,n){return n.name===t.name?e+1:e}),0)>1){var o=r.buildDuplicateRuleNameError({topLevelRule:t,grammarName:n});i.push({message:o,type:s.ParserDefinitionErrorType.DUPLICATE_RULE_NAME,ruleName:t.name})}return i}function y(t,e,n,r){void 0===r&&(r=[]);var i=[],a=T(e.definition);if(o.isEmpty(a))return[];var c=t.name;o.contains(a,t)&&i.push({message:n.buildLeftRecursionError({topLevelRule:t,leftRecursionPath:r}),type:s.ParserDefinitionErrorType.LEFT_RECURSION,ruleName:c});var u=o.difference(a,r.concat([t])),l=o.map(u,(function(e){var i=o.cloneArr(r);return i.push(e),y(t,e,n,i)}));return i.concat(o.flatten(l))}function T(t){var e=[];if(o.isEmpty(t))return e;var n=o.first(t);if(n instanceof p.NonTerminal)e.push(n.referencedRule);else if(n instanceof p.Alternative||n instanceof p.Option||n instanceof p.RepetitionMandatory||n instanceof p.RepetitionMandatoryWithSeparator||n instanceof p.RepetitionWithSeparator||n instanceof p.Repetition)e=e.concat(T(n.definition));else if(n instanceof p.Alternation)e=o.flatten(o.map(n.definition,(function(t){return T(t.definition)})));else if(!(n instanceof p.Terminal))throw Error("non exhaustive match");var r=c.isOptionalProd(n),i=t.length>1;if(r&&i){var a=o.drop(t);return e.concat(T(a))}return e}e.OccurrenceValidationCollector=E,e.validateRuleDoesNotAlreadyExist=m,e.validateRuleIsOverridden=function(t,e,n){var r,i=[];return o.contains(e,t)||(r="Invalid rule override, rule: ->"+t+"<- cannot be overridden in the grammar: ->"+n+"<-as it is not defined in any of the super grammars ",i.push({message:r,type:s.ParserDefinitionErrorType.INVALID_RULE_OVERRIDE,ruleName:t})),i},e.validateNoLeftRecursion=y,e.getFirstNoneTerminal=T;var v=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.alternations=[],e}return i(e,t),e.prototype.visitAlternation=function(t){this.alternations.push(t)},e}(f.GAstVisitor);function R(t,e){var n=new v;t.accept(n);var r=n.alternations;return o.reduce(r,(function(n,r){var i=o.dropRight(r.definition),a=o.map(i,(function(n,i){var a=l.nextPossibleTokensAfter([n],[],null,1);return o.isEmpty(a)?{message:e.buildEmptyAlternationError({topLevelRule:t,alternation:r,emptyChoiceIdx:i}),type:s.ParserDefinitionErrorType.NONE_LAST_EMPTY_ALT,ruleName:t.name,occurrence:r.idx,alternative:i+1}:null}));return n.concat(o.compact(a))}),[])}function O(t,e,n){var r=new v;t.accept(r);var i=r.alternations;return i=a.reject(i,(function(t){return!0===t.ignoreAmbiguities})),o.reduce(i,(function(r,i){var c=i.idx,l=i.maxLookahead||e,p=u.getLookaheadPathsForOr(c,t,l,i),f=function(t,e,n,r){var i=[],c=a.reduce(t,(function(n,r,o){return!0===e.definition[o].ignoreAmbiguities||a.forEach(r,(function(r){var s=[o];a.forEach(t,(function(t,n){o!==n&&u.containsPath(t,r)&&!0!==e.definition[n].ignoreAmbiguities&&s.push(n)})),s.length>1&&!u.containsPath(i,r)&&(i.push(r),n.push({alts:s,path:r}))})),n}),[]);return o.map(c,(function(t){var i=a.map(t.alts,(function(t){return t+1}));return{message:r.buildAlternationAmbiguityError({topLevelRule:n,alternation:e,ambiguityIndices:i,prefixPath:t.path}),type:s.ParserDefinitionErrorType.AMBIGUOUS_ALTS,ruleName:n.name,occurrence:e.idx,alternatives:[t.alts]}}))}(p,i,t,n),h=N(p,i,t,n);return r.concat(f,h)}),[])}e.validateEmptyOrAlternative=R,e.validateAmbiguousAlternationAlternatives=O;var g=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.allProductions=[],e}return i(e,t),e.prototype.visitRepetitionWithSeparator=function(t){this.allProductions.push(t)},e.prototype.visitRepetitionMandatory=function(t){this.allProductions.push(t)},e.prototype.visitRepetitionMandatoryWithSeparator=function(t){this.allProductions.push(t)},e.prototype.visitRepetition=function(t){this.allProductions.push(t)},e}(f.GAstVisitor);function A(t,e){var n=new v;t.accept(n);var r=n.alternations;return o.reduce(r,(function(n,r){return r.definition.length>255&&n.push({message:e.buildTooManyAlternativesError({topLevelRule:t,alternation:r}),type:s.ParserDefinitionErrorType.TOO_MANY_ALTS,ruleName:t.name,occurrence:r.idx}),n}),[])}function _(t,e,n){var r=[];return a.forEach(t,(function(t){var i=new g;t.accept(i);var o=i.allProductions;a.forEach(o,(function(i){var o=u.getProdType(i),c=i.maxLookahead||e,l=i.idx,p=u.getLookaheadPathsForOptionalProd(l,t,o,c)[0];if(a.isEmpty(a.flatten(p))){var f=n.buildEmptyRepetitionError({topLevelRule:t,repetition:i});r.push({message:f,type:s.ParserDefinitionErrorType.NO_NON_EMPTY_LOOKAHEAD,ruleName:t.name})}}))})),r}function N(t,e,n,r){var i=[],o=a.reduce(t,(function(t,e,n){var r=a.map(e,(function(t){return{idx:n,path:t}}));return t.concat(r)}),[]);return a.forEach(o,(function(t){if(!0!==e.definition[t.idx].ignoreAmbiguities){var c=t.idx,l=t.path,p=a.findAll(o,(function(t){return!0!==e.definition[t.idx].ignoreAmbiguities&&t.idx<c&&u.isStrictPrefixOfPath(t.path,l)})),f=a.map(p,(function(t){var i=[t.idx+1,c+1],o=0===e.idx?"":e.idx;return{message:r.buildAlternationPrefixAmbiguityError({topLevelRule:n,alternation:e,ambiguityIndices:i,prefixPath:t.path}),type:s.ParserDefinitionErrorType.AMBIGUOUS_PREFIX_ALTS,ruleName:n.name,occurrence:o,alternatives:i}}));i=i.concat(f)}})),i}e.RepetionCollector=g,e.validateTooManyAlts=A,e.validateSomeNonEmptyLookaheadPath=_,e.checkPrefixAlternativesAmbiguities=N},52:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.firstForTerminal=e.firstForBranching=e.firstForSequence=e.first=void 0;var r=n(146),i=n(946),o=n(651);function a(t){if(t instanceof i.NonTerminal)return a(t.referencedRule);if(t instanceof i.Terminal)return u(t);if(o.isSequenceProd(t))return s(t);if(o.isBranchingProd(t))return c(t);throw Error("non exhaustive match")}function s(t){for(var e,n=[],i=t.definition,s=0,c=i.length>s,u=!0;c&&u;)e=i[s],u=o.isOptionalProd(e),n=n.concat(a(e)),s+=1,c=i.length>s;return r.uniq(n)}function c(t){var e=r.map(t.definition,(function(t){return a(t)}));return r.uniq(r.flatten(e))}function u(t){return[t.terminalType]}e.first=a,e.firstForSequence=s,e.firstForBranching=c,e.firstForTerminal=u},917:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0}),e.buildInProdFollowPrefix=e.buildBetweenProdsFollowPrefix=e.computeAllProdsFollows=e.ResyncFollowsWalker=void 0;var o=n(567),a=n(52),s=n(146),c=n(710),u=n(946),l=function(t){function e(e){var n=t.call(this)||this;return n.topProd=e,n.follows={},n}return i(e,t),e.prototype.startWalking=function(){return this.walk(this.topProd),this.follows},e.prototype.walkTerminal=function(t,e,n){},e.prototype.walkProdRef=function(t,e,n){var r=p(t.referencedRule,t.idx)+this.topProd.name,i=e.concat(n),o=new u.Alternative({definition:i}),s=a.first(o);this.follows[r]=s},e}(o.RestWalker);function p(t,e){return t.name+e+c.IN}e.ResyncFollowsWalker=l,e.computeAllProdsFollows=function(t){var e={};return s.forEach(t,(function(t){var n=new l(t).startWalking();s.assign(e,n)})),e},e.buildBetweenProdsFollowPrefix=p,e.buildInProdFollowPrefix=function(t){return t.terminalType.name+t.idx+c.IN}},651:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0}),e.collectMethods=e.DslMethodsCollectorVisitor=e.getProductionDslName=e.isBranchingProd=e.isOptionalProd=e.isSequenceProd=void 0;var o=n(146),a=n(946),s=n(841);e.isSequenceProd=function(t){return t instanceof a.Alternative||t instanceof a.Option||t instanceof a.Repetition||t instanceof a.RepetitionMandatory||t instanceof a.RepetitionMandatoryWithSeparator||t instanceof a.RepetitionWithSeparator||t instanceof a.Terminal||t instanceof a.Rule},e.isOptionalProd=function t(e,n){return void 0===n&&(n=[]),!!(e instanceof a.Option||e instanceof a.Repetition||e instanceof a.RepetitionWithSeparator)||(e instanceof a.Alternation?o.some(e.definition,(function(e){return t(e,n)})):!(e instanceof a.NonTerminal&&o.contains(n,e))&&e instanceof a.AbstractProduction&&(e instanceof a.NonTerminal&&n.push(e),o.every(e.definition,(function(e){return t(e,n)}))))},e.isBranchingProd=function(t){return t instanceof a.Alternation},e.getProductionDslName=function(t){if(t instanceof a.NonTerminal)return"SUBRULE";if(t instanceof a.Option)return"OPTION";if(t instanceof a.Alternation)return"OR";if(t instanceof a.RepetitionMandatory)return"AT_LEAST_ONE";if(t instanceof a.RepetitionMandatoryWithSeparator)return"AT_LEAST_ONE_SEP";if(t instanceof a.RepetitionWithSeparator)return"MANY_SEP";if(t instanceof a.Repetition)return"MANY";if(t instanceof a.Terminal)return"CONSUME";throw Error("non exhaustive match")};var c=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.separator="-",e.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]},e}return i(e,t),e.prototype.reset=function(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}},e.prototype.visitTerminal=function(t){var e=t.terminalType.name+this.separator+"Terminal";o.has(this.dslMethods,e)||(this.dslMethods[e]=[]),this.dslMethods[e].push(t)},e.prototype.visitNonTerminal=function(t){var e=t.nonTerminalName+this.separator+"Terminal";o.has(this.dslMethods,e)||(this.dslMethods[e]=[]),this.dslMethods[e].push(t)},e.prototype.visitOption=function(t){this.dslMethods.option.push(t)},e.prototype.visitRepetitionWithSeparator=function(t){this.dslMethods.repetitionWithSeparator.push(t)},e.prototype.visitRepetitionMandatory=function(t){this.dslMethods.repetitionMandatory.push(t)},e.prototype.visitRepetitionMandatoryWithSeparator=function(t){this.dslMethods.repetitionMandatoryWithSeparator.push(t)},e.prototype.visitRepetition=function(t){this.dslMethods.repetition.push(t)},e.prototype.visitAlternation=function(t){this.dslMethods.alternation.push(t)},e}(s.GAstVisitor);e.DslMethodsCollectorVisitor=c;var u=new c;e.collectMethods=function(t){u.reset(),t.accept(u);var e=u.dslMethods;return u.reset(),e}},946:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0}),e.serializeProduction=e.serializeGrammar=e.Terminal=e.Alternation=e.RepetitionWithSeparator=e.Repetition=e.RepetitionMandatoryWithSeparator=e.RepetitionMandatory=e.Option=e.Alternative=e.Rule=e.NonTerminal=e.AbstractProduction=void 0;var o=n(146),a=n(736),s=function(){function t(t){this._definition=t}return Object.defineProperty(t.prototype,"definition",{get:function(){return this._definition},set:function(t){this._definition=t},enumerable:!1,configurable:!0}),t.prototype.accept=function(t){t.visit(this),o.forEach(this.definition,(function(e){e.accept(t)}))},t}();e.AbstractProduction=s;var c=function(t){function e(e){var n=t.call(this,[])||this;return n.idx=1,o.assign(n,o.pick(e,(function(t){return void 0!==t}))),n}return i(e,t),Object.defineProperty(e.prototype,"definition",{get:function(){return void 0!==this.referencedRule?this.referencedRule.definition:[]},set:function(t){},enumerable:!1,configurable:!0}),e.prototype.accept=function(t){t.visit(this)},e}(s);e.NonTerminal=c;var u=function(t){function e(e){var n=t.call(this,e.definition)||this;return n.orgText="",o.assign(n,o.pick(e,(function(t){return void 0!==t}))),n}return i(e,t),e}(s);e.Rule=u;var l=function(t){function e(e){var n=t.call(this,e.definition)||this;return n.ignoreAmbiguities=!1,o.assign(n,o.pick(e,(function(t){return void 0!==t}))),n}return i(e,t),e}(s);e.Alternative=l;var p=function(t){function e(e){var n=t.call(this,e.definition)||this;return n.idx=1,o.assign(n,o.pick(e,(function(t){return void 0!==t}))),n}return i(e,t),e}(s);e.Option=p;var f=function(t){function e(e){var n=t.call(this,e.definition)||this;return n.idx=1,o.assign(n,o.pick(e,(function(t){return void 0!==t}))),n}return i(e,t),e}(s);e.RepetitionMandatory=f;var h=function(t){function e(e){var n=t.call(this,e.definition)||this;return n.idx=1,o.assign(n,o.pick(e,(function(t){return void 0!==t}))),n}return i(e,t),e}(s);e.RepetitionMandatoryWithSeparator=h;var d=function(t){function e(e){var n=t.call(this,e.definition)||this;return n.idx=1,o.assign(n,o.pick(e,(function(t){return void 0!==t}))),n}return i(e,t),e}(s);e.Repetition=d;var E=function(t){function e(e){var n=t.call(this,e.definition)||this;return n.idx=1,o.assign(n,o.pick(e,(function(t){return void 0!==t}))),n}return i(e,t),e}(s);e.RepetitionWithSeparator=E;var m=function(t){function e(e){var n=t.call(this,e.definition)||this;return n.idx=1,n.ignoreAmbiguities=!1,n.hasPredicates=!1,o.assign(n,o.pick(e,(function(t){return void 0!==t}))),n}return i(e,t),Object.defineProperty(e.prototype,"definition",{get:function(){return this._definition},set:function(t){this._definition=t},enumerable:!1,configurable:!0}),e}(s);e.Alternation=m;var y=function(){function t(t){this.idx=1,o.assign(this,o.pick(t,(function(t){return void 0!==t})))}return t.prototype.accept=function(t){t.visit(this)},t}();function T(t){function e(t){return o.map(t,T)}if(t instanceof c)return{type:"NonTerminal",name:t.nonTerminalName,idx:t.idx};if(t instanceof l)return{type:"Alternative",definition:e(t.definition)};if(t instanceof p)return{type:"Option",idx:t.idx,definition:e(t.definition)};if(t instanceof f)return{type:"RepetitionMandatory",idx:t.idx,definition:e(t.definition)};if(t instanceof h)return{type:"RepetitionMandatoryWithSeparator",idx:t.idx,separator:T(new y({terminalType:t.separator})),definition:e(t.definition)};if(t instanceof E)return{type:"RepetitionWithSeparator",idx:t.idx,separator:T(new y({terminalType:t.separator})),definition:e(t.definition)};if(t instanceof d)return{type:"Repetition",idx:t.idx,definition:e(t.definition)};if(t instanceof m)return{type:"Alternation",idx:t.idx,definition:e(t.definition)};if(t instanceof y){var n={type:"Terminal",name:t.terminalType.name,label:a.tokenLabel(t.terminalType),idx:t.idx},r=t.terminalType.PATTERN;return t.terminalType.PATTERN&&(n.pattern=o.isRegExp(r)?r.source:r),n}if(t instanceof u)return{type:"Rule",name:t.name,orgText:t.orgText,definition:e(t.definition)};throw Error("non exhaustive match")}e.Terminal=y,e.serializeGrammar=function(t){return o.map(t,T)},e.serializeProduction=T},665:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.validateGrammar=e.resolveGrammar=void 0;var r=n(146),i=n(304),o=n(870),a=n(7);e.resolveGrammar=function(t){t=r.defaults(t,{errMsgProvider:a.defaultGrammarResolverErrorProvider});var e={};return r.forEach(t.rules,(function(t){e[t.name]=t})),i.resolveGrammar(e,t.errMsgProvider)},e.validateGrammar=function(t){return t=r.defaults(t,{errMsgProvider:a.defaultGrammarValidatorErrorProvider}),o.validateGrammar(t.rules,t.maxLookahead,t.tokenTypes,t.errMsgProvider,t.grammarName)}},841:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.GAstVisitor=void 0;var r=n(946),i=function(){function t(){}return t.prototype.visit=function(t){var e=t;switch(e.constructor){case r.NonTerminal:return this.visitNonTerminal(e);case r.Alternative:return this.visitAlternative(e);case r.Option:return this.visitOption(e);case r.RepetitionMandatory:return this.visitRepetitionMandatory(e);case r.RepetitionMandatoryWithSeparator:return this.visitRepetitionMandatoryWithSeparator(e);case r.RepetitionWithSeparator:return this.visitRepetitionWithSeparator(e);case r.Repetition:return this.visitRepetition(e);case r.Alternation:return this.visitAlternation(e);case r.Terminal:return this.visitTerminal(e);case r.Rule:return this.visitRule(e);default:throw Error("non exhaustive match")}},t.prototype.visitNonTerminal=function(t){},t.prototype.visitAlternative=function(t){},t.prototype.visitOption=function(t){},t.prototype.visitRepetition=function(t){},t.prototype.visitRepetitionMandatory=function(t){},t.prototype.visitRepetitionMandatoryWithSeparator=function(t){},t.prototype.visitRepetitionWithSeparator=function(t){},t.prototype.visitAlternation=function(t){},t.prototype.visitTerminal=function(t){},t.prototype.visitRule=function(t){},t}();e.GAstVisitor=i},985:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0}),e.nextPossibleTokensAfter=e.possiblePathsFrom=e.NextTerminalAfterAtLeastOneSepWalker=e.NextTerminalAfterAtLeastOneWalker=e.NextTerminalAfterManySepWalker=e.NextTerminalAfterManyWalker=e.AbstractNextTerminalAfterProductionWalker=e.NextAfterTokenWalker=e.AbstractNextPossibleTokensWalker=void 0;var o=n(567),a=n(146),s=n(52),c=n(946),u=function(t){function e(e,n){var r=t.call(this)||this;return r.topProd=e,r.path=n,r.possibleTokTypes=[],r.nextProductionName="",r.nextProductionOccurrence=0,r.found=!1,r.isAtEndOfPath=!1,r}return i(e,t),e.prototype.startWalking=function(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=a.cloneArr(this.path.ruleStack).reverse(),this.occurrenceStack=a.cloneArr(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes},e.prototype.walk=function(e,n){void 0===n&&(n=[]),this.found||t.prototype.walk.call(this,e,n)},e.prototype.walkProdRef=function(t,e,n){if(t.referencedRule.name===this.nextProductionName&&t.idx===this.nextProductionOccurrence){var r=e.concat(n);this.updateExpectedNext(),this.walk(t.referencedRule,r)}},e.prototype.updateExpectedNext=function(){a.isEmpty(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())},e}(o.RestWalker);e.AbstractNextPossibleTokensWalker=u;var l=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.path=n,r.nextTerminalName="",r.nextTerminalOccurrence=0,r.nextTerminalName=r.path.lastTok.name,r.nextTerminalOccurrence=r.path.lastTokOccurrence,r}return i(e,t),e.prototype.walkTerminal=function(t,e,n){if(this.isAtEndOfPath&&t.terminalType.name===this.nextTerminalName&&t.idx===this.nextTerminalOccurrence&&!this.found){var r=e.concat(n),i=new c.Alternative({definition:r});this.possibleTokTypes=s.first(i),this.found=!0}},e}(u);e.NextAfterTokenWalker=l;var p=function(t){function e(e,n){var r=t.call(this)||this;return r.topRule=e,r.occurrence=n,r.result={token:void 0,occurrence:void 0,isEndOfRule:void 0},r}return i(e,t),e.prototype.startWalking=function(){return this.walk(this.topRule),this.result},e}(o.RestWalker);e.AbstractNextTerminalAfterProductionWalker=p;var f=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.walkMany=function(e,n,r){if(e.idx===this.occurrence){var i=a.first(n.concat(r));this.result.isEndOfRule=void 0===i,i instanceof c.Terminal&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else t.prototype.walkMany.call(this,e,n,r)},e}(p);e.NextTerminalAfterManyWalker=f;var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.walkManySep=function(e,n,r){if(e.idx===this.occurrence){var i=a.first(n.concat(r));this.result.isEndOfRule=void 0===i,i instanceof c.Terminal&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else t.prototype.walkManySep.call(this,e,n,r)},e}(p);e.NextTerminalAfterManySepWalker=h;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.walkAtLeastOne=function(e,n,r){if(e.idx===this.occurrence){var i=a.first(n.concat(r));this.result.isEndOfRule=void 0===i,i instanceof c.Terminal&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else t.prototype.walkAtLeastOne.call(this,e,n,r)},e}(p);e.NextTerminalAfterAtLeastOneWalker=d;var E=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.walkAtLeastOneSep=function(e,n,r){if(e.idx===this.occurrence){var i=a.first(n.concat(r));this.result.isEndOfRule=void 0===i,i instanceof c.Terminal&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else t.prototype.walkAtLeastOneSep.call(this,e,n,r)},e}(p);function m(t,e,n,r){var i=a.cloneArr(n);i.push(t.name);var o=a.cloneArr(r);return o.push(1),{idx:e,def:t.definition,ruleStack:i,occurrenceStack:o}}e.NextTerminalAfterAtLeastOneSepWalker=E,e.possiblePathsFrom=function t(e,n,r){void 0===r&&(r=[]),r=a.cloneArr(r);var i=[],o=0;function s(s){var c=t(s.concat(a.drop(e,o+1)),n,r);return i.concat(c)}for(;r.length<n&&o<e.length;){var u=e[o];if(u instanceof c.Alternative)return s(u.definition);if(u instanceof c.NonTerminal)return s(u.definition);if(u instanceof c.Option)i=s(u.definition);else{if(u instanceof c.RepetitionMandatory)return s(l=u.definition.concat([new c.Repetition({definition:u.definition})]));if(u instanceof c.RepetitionMandatoryWithSeparator)return s(l=[new c.Alternative({definition:u.definition}),new c.Repetition({definition:[new c.Terminal({terminalType:u.separator})].concat(u.definition)})]);if(u instanceof c.RepetitionWithSeparator){var l=u.definition.concat([new c.Repetition({definition:[new c.Terminal({terminalType:u.separator})].concat(u.definition)})]);i=s(l)}else if(u instanceof c.Repetition)l=u.definition.concat([new c.Repetition({definition:u.definition})]),i=s(l);else{if(u instanceof c.Alternation)return a.forEach(u.definition,(function(t){!1===a.isEmpty(t.definition)&&(i=s(t.definition))})),i;if(!(u instanceof c.Terminal))throw Error("non exhaustive match");r.push(u.terminalType)}}o++}return i.push({partialPath:r,suffixDef:a.drop(e,o)}),i},e.nextPossibleTokensAfter=function(t,e,n,r){var i="EXIT_NONE_TERMINAL",o=[i],s="EXIT_ALTERNATIVE",u=!1,l=e.length,p=l-r-1,f=[],h=[];for(h.push({idx:-1,def:t,ruleStack:[],occurrenceStack:[]});!a.isEmpty(h);){var d=h.pop();if(d!==s){var E=d.def,y=d.idx,T=d.ruleStack,v=d.occurrenceStack;if(!a.isEmpty(E)){var R=E[0];if(R===i){var O={idx:y,def:a.drop(E),ruleStack:a.dropRight(T),occurrenceStack:a.dropRight(v)};h.push(O)}else if(R instanceof c.Terminal)if(y<l-1){var g=y+1;n(e[g],R.terminalType)&&(O={idx:g,def:a.drop(E),ruleStack:T,occurrenceStack:v},h.push(O))}else{if(y!==l-1)throw Error("non exhaustive match");f.push({nextTokenType:R.terminalType,nextTokenOccurrence:R.idx,ruleStack:T,occurrenceStack:v}),u=!0}else if(R instanceof c.NonTerminal){var A=a.cloneArr(T);A.push(R.nonTerminalName);var _=a.cloneArr(v);_.push(R.idx),O={idx:y,def:R.definition.concat(o,a.drop(E)),ruleStack:A,occurrenceStack:_},h.push(O)}else if(R instanceof c.Option){var N={idx:y,def:a.drop(E),ruleStack:T,occurrenceStack:v};h.push(N),h.push(s);var I={idx:y,def:R.definition.concat(a.drop(E)),ruleStack:T,occurrenceStack:v};h.push(I)}else if(R instanceof c.RepetitionMandatory){var P=new c.Repetition({definition:R.definition,idx:R.idx});O={idx:y,def:R.definition.concat([P],a.drop(E)),ruleStack:T,occurrenceStack:v},h.push(O)}else if(R instanceof c.RepetitionMandatoryWithSeparator){var S=new c.Terminal({terminalType:R.separator});P=new c.Repetition({definition:[S].concat(R.definition),idx:R.idx}),O={idx:y,def:R.definition.concat([P],a.drop(E)),ruleStack:T,occurrenceStack:v},h.push(O)}else if(R instanceof c.RepetitionWithSeparator){N={idx:y,def:a.drop(E),ruleStack:T,occurrenceStack:v},h.push(N),h.push(s),S=new c.Terminal({terminalType:R.separator});var k=new c.Repetition({definition:[S].concat(R.definition),idx:R.idx});I={idx:y,def:R.definition.concat([k],a.drop(E)),ruleStack:T,occurrenceStack:v},h.push(I)}else if(R instanceof c.Repetition)N={idx:y,def:a.drop(E),ruleStack:T,occurrenceStack:v},h.push(N),h.push(s),k=new c.Repetition({definition:R.definition,idx:R.idx}),I={idx:y,def:R.definition.concat([k],a.drop(E)),ruleStack:T,occurrenceStack:v},h.push(I);else if(R instanceof c.Alternation)for(var L=R.definition.length-1;L>=0;L--){var C={idx:y,def:R.definition[L].definition.concat(a.drop(E)),ruleStack:T,occurrenceStack:v};h.push(C),h.push(s)}else if(R instanceof c.Alternative)h.push({idx:y,def:R.definition.concat(a.drop(E)),ruleStack:T,occurrenceStack:v});else{if(!(R instanceof c.Rule))throw Error("non exhaustive match");h.push(m(R,y,T,v))}}}else u&&a.last(h).idx<=p&&h.pop()}return f}},209:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getKeyForAutomaticLookahead=e.AT_LEAST_ONE_SEP_IDX=e.MANY_SEP_IDX=e.AT_LEAST_ONE_IDX=e.MANY_IDX=e.OPTION_IDX=e.OR_IDX=e.BITS_FOR_ALT_IDX=e.BITS_FOR_RULE_IDX=e.BITS_FOR_OCCURRENCE_IDX=e.BITS_FOR_METHOD_TYPE=void 0,e.BITS_FOR_METHOD_TYPE=4,e.BITS_FOR_OCCURRENCE_IDX=8,e.BITS_FOR_RULE_IDX=12,e.BITS_FOR_ALT_IDX=8,e.OR_IDX=1<<e.BITS_FOR_OCCURRENCE_IDX,e.OPTION_IDX=2<<e.BITS_FOR_OCCURRENCE_IDX,e.MANY_IDX=3<<e.BITS_FOR_OCCURRENCE_IDX,e.AT_LEAST_ONE_IDX=4<<e.BITS_FOR_OCCURRENCE_IDX,e.MANY_SEP_IDX=5<<e.BITS_FOR_OCCURRENCE_IDX,e.AT_LEAST_ONE_SEP_IDX=6<<e.BITS_FOR_OCCURRENCE_IDX,e.getKeyForAutomaticLookahead=function(t,e,n){return n|e|t},e.BITS_FOR_ALT_IDX},677:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0}),e.areTokenCategoriesNotUsed=e.isStrictPrefixOfPath=e.containsPath=e.getLookaheadPathsForOptionalProd=e.getLookaheadPathsForOr=e.lookAheadSequenceFromAlternatives=e.buildSingleAlternativeLookaheadFunction=e.buildAlternativesLookAheadFunc=e.buildLookaheadFuncForOptionalProd=e.buildLookaheadFuncForOr=e.getProdType=e.PROD_TYPE=void 0;var o,a=n(146),s=n(985),c=n(567),u=n(201),l=n(946),p=n(841);!function(t){t[t.OPTION=0]="OPTION",t[t.REPETITION=1]="REPETITION",t[t.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",t[t.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",t[t.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",t[t.ALTERNATION=5]="ALTERNATION"}(o=e.PROD_TYPE||(e.PROD_TYPE={})),e.getProdType=function(t){if(t instanceof l.Option)return o.OPTION;if(t instanceof l.Repetition)return o.REPETITION;if(t instanceof l.RepetitionMandatory)return o.REPETITION_MANDATORY;if(t instanceof l.RepetitionMandatoryWithSeparator)return o.REPETITION_MANDATORY_WITH_SEPARATOR;if(t instanceof l.RepetitionWithSeparator)return o.REPETITION_WITH_SEPARATOR;if(t instanceof l.Alternation)return o.ALTERNATION;throw Error("non exhaustive match")},e.buildLookaheadFuncForOr=function(t,e,n,r,i,o){var a=T(t,e,n);return o(a,r,O(a)?u.tokenStructuredMatcherNoCategories:u.tokenStructuredMatcher,i)},e.buildLookaheadFuncForOptionalProd=function(t,e,n,r,i,o){var a=v(t,e,i,n),s=O(a)?u.tokenStructuredMatcherNoCategories:u.tokenStructuredMatcher;return o(a[0],s,r)},e.buildAlternativesLookAheadFunc=function(t,e,n,r){var i=t.length,o=a.every(t,(function(t){return a.every(t,(function(t){return 1===t.length}))}));if(e)return function(e){for(var r=a.map(e,(function(t){return t.GATE})),o=0;o<i;o++){var s=t[o],c=s.length,u=r[o];if(void 0===u||!1!==u.call(this))t:for(var l=0;l<c;l++){for(var p=s[l],f=p.length,h=0;h<f;h++){var d=this.LA(h+1);if(!1===n(d,p[h]))continue t}return o}}};if(o&&!r){var s=a.map(t,(function(t){return a.flatten(t)})),c=a.reduce(s,(function(t,e,n){return a.forEach(e,(function(e){a.has(t,e.tokenTypeIdx)||(t[e.tokenTypeIdx]=n),a.forEach(e.categoryMatches,(function(e){a.has(t,e)||(t[e]=n)}))})),t}),[]);return function(){var t=this.LA(1);return c[t.tokenTypeIdx]}}return function(){for(var e=0;e<i;e++){var r=t[e],o=r.length;t:for(var a=0;a<o;a++){for(var s=r[a],c=s.length,u=0;u<c;u++){var l=this.LA(u+1);if(!1===n(l,s[u]))continue t}return e}}}},e.buildSingleAlternativeLookaheadFunction=function(t,e,n){var r=a.every(t,(function(t){return 1===t.length})),i=t.length;if(r&&!n){var o=a.flatten(t);if(1===o.length&&a.isEmpty(o[0].categoryMatches)){var s=o[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===s}}var c=a.reduce(o,(function(t,e,n){return t[e.tokenTypeIdx]=!0,a.forEach(e.categoryMatches,(function(e){t[e]=!0})),t}),[]);return function(){var t=this.LA(1);return!0===c[t.tokenTypeIdx]}}return function(){t:for(var n=0;n<i;n++){for(var r=t[n],o=r.length,a=0;a<o;a++){var s=this.LA(a+1);if(!1===e(s,r[a]))continue t}return!0}return!1}};var f=function(t){function e(e,n,r){var i=t.call(this)||this;return i.topProd=e,i.targetOccurrence=n,i.targetProdType=r,i}return i(e,t),e.prototype.startWalking=function(){return this.walk(this.topProd),this.restDef},e.prototype.checkIsTarget=function(t,e,n,r){return t.idx===this.targetOccurrence&&this.targetProdType===e&&(this.restDef=n.concat(r),!0)},e.prototype.walkOption=function(e,n,r){this.checkIsTarget(e,o.OPTION,n,r)||t.prototype.walkOption.call(this,e,n,r)},e.prototype.walkAtLeastOne=function(e,n,r){this.checkIsTarget(e,o.REPETITION_MANDATORY,n,r)||t.prototype.walkOption.call(this,e,n,r)},e.prototype.walkAtLeastOneSep=function(e,n,r){this.checkIsTarget(e,o.REPETITION_MANDATORY_WITH_SEPARATOR,n,r)||t.prototype.walkOption.call(this,e,n,r)},e.prototype.walkMany=function(e,n,r){this.checkIsTarget(e,o.REPETITION,n,r)||t.prototype.walkOption.call(this,e,n,r)},e.prototype.walkManySep=function(e,n,r){this.checkIsTarget(e,o.REPETITION_WITH_SEPARATOR,n,r)||t.prototype.walkOption.call(this,e,n,r)},e}(c.RestWalker),h=function(t){function e(e,n,r){var i=t.call(this)||this;return i.targetOccurrence=e,i.targetProdType=n,i.targetRef=r,i.result=[],i}return i(e,t),e.prototype.checkIsTarget=function(t,e){t.idx!==this.targetOccurrence||this.targetProdType!==e||void 0!==this.targetRef&&t!==this.targetRef||(this.result=t.definition)},e.prototype.visitOption=function(t){this.checkIsTarget(t,o.OPTION)},e.prototype.visitRepetition=function(t){this.checkIsTarget(t,o.REPETITION)},e.prototype.visitRepetitionMandatory=function(t){this.checkIsTarget(t,o.REPETITION_MANDATORY)},e.prototype.visitRepetitionMandatoryWithSeparator=function(t){this.checkIsTarget(t,o.REPETITION_MANDATORY_WITH_SEPARATOR)},e.prototype.visitRepetitionWithSeparator=function(t){this.checkIsTarget(t,o.REPETITION_WITH_SEPARATOR)},e.prototype.visitAlternation=function(t){this.checkIsTarget(t,o.ALTERNATION)},e}(p.GAstVisitor);function d(t){for(var e=new Array(t),n=0;n<t;n++)e[n]=[];return e}function E(t){for(var e=[""],n=0;n<t.length;n++){for(var r=t[n],i=[],o=0;o<e.length;o++){var a=e[o];i.push(a+"_"+r.tokenTypeIdx);for(var s=0;s<r.categoryMatches.length;s++){var c="_"+r.categoryMatches[s];i.push(a+c)}}e=i}return e}function m(t,e,n){for(var r=0;r<t.length;r++)if(r!==n)for(var i=t[r],o=0;o<e.length;o++)if(!0===i[e[o]])return!1;return!0}function y(t,e){for(var n=a.map(t,(function(t){return s.possiblePathsFrom([t],1)})),r=d(n.length),i=a.map(n,(function(t){var e={};return a.forEach(t,(function(t){var n=E(t.partialPath);a.forEach(n,(function(t){e[t]=!0}))})),e})),o=n,c=1;c<=e;c++){var u=o;o=d(u.length);for(var l=function(t){for(var n=u[t],l=0;l<n.length;l++){var p=n[l].partialPath,f=n[l].suffixDef,h=E(p);if(m(i,h,t)||a.isEmpty(f)||p.length===e){var d=r[t];if(!1===R(d,p)){d.push(p);for(var y=0;y<h.length;y++){var T=h[y];i[t][T]=!0}}}else{var v=s.possiblePathsFrom(f,c+1,p);o[t]=o[t].concat(v),a.forEach(v,(function(e){var n=E(e.partialPath);a.forEach(n,(function(e){i[t][e]=!0}))}))}}},p=0;p<u.length;p++)l(p)}return r}function T(t,e,n,r){var i=new h(t,o.ALTERNATION,r);return e.accept(i),y(i.result,n)}function v(t,e,n,r){var i=new h(t,n);e.accept(i);var o=i.result,a=new f(e,t,n).startWalking();return y([new l.Alternative({definition:o}),new l.Alternative({definition:a})],r)}function R(t,e){t:for(var n=0;n<t.length;n++){var r=t[n];if(r.length===e.length){for(var i=0;i<r.length;i++){var o=e[i],a=r[i];if(!1==(o===a||void 0!==a.categoryMatchesMap[o.tokenTypeIdx]))continue t}return!0}}return!1}function O(t){return a.every(t,(function(t){return a.every(t,(function(t){return a.every(t,(function(t){return a.isEmpty(t.categoryMatches)}))}))}))}e.lookAheadSequenceFromAlternatives=y,e.getLookaheadPathsForOr=T,e.getLookaheadPathsForOptionalProd=v,e.containsPath=R,e.isStrictPrefixOfPath=function(t,e){return t.length<e.length&&a.every(t,(function(t,n){var r=e[n];return t===r||r.categoryMatchesMap[t.tokenTypeIdx]}))},e.areTokenCategoriesNotUsed=O},304:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0}),e.GastRefResolverVisitor=e.resolveGrammar=void 0;var o=n(941),a=n(146),s=n(841);e.resolveGrammar=function(t,e){var n=new c(t,e);return n.resolveRefs(),n.errors};var c=function(t){function e(e,n){var r=t.call(this)||this;return r.nameToTopRule=e,r.errMsgProvider=n,r.errors=[],r}return i(e,t),e.prototype.resolveRefs=function(){var t=this;a.forEach(a.values(this.nameToTopRule),(function(e){t.currTopLevel=e,e.accept(t)}))},e.prototype.visitNonTerminal=function(t){var e=this.nameToTopRule[t.nonTerminalName];if(e)t.referencedRule=e;else{var n=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,t);this.errors.push({message:n,type:o.ParserDefinitionErrorType.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:t.nonTerminalName})}},e}(s.GAstVisitor);e.GastRefResolverVisitor=c},567:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.RestWalker=void 0;var r=n(146),i=n(946),o=function(){function t(){}return t.prototype.walk=function(t,e){var n=this;void 0===e&&(e=[]),r.forEach(t.definition,(function(o,a){var s=r.drop(t.definition,a+1);if(o instanceof i.NonTerminal)n.walkProdRef(o,s,e);else if(o instanceof i.Terminal)n.walkTerminal(o,s,e);else if(o instanceof i.Alternative)n.walkFlat(o,s,e);else if(o instanceof i.Option)n.walkOption(o,s,e);else if(o instanceof i.RepetitionMandatory)n.walkAtLeastOne(o,s,e);else if(o instanceof i.RepetitionMandatoryWithSeparator)n.walkAtLeastOneSep(o,s,e);else if(o instanceof i.RepetitionWithSeparator)n.walkManySep(o,s,e);else if(o instanceof i.Repetition)n.walkMany(o,s,e);else{if(!(o instanceof i.Alternation))throw Error("non exhaustive match");n.walkOr(o,s,e)}}))},t.prototype.walkTerminal=function(t,e,n){},t.prototype.walkProdRef=function(t,e,n){},t.prototype.walkFlat=function(t,e,n){var r=e.concat(n);this.walk(t,r)},t.prototype.walkOption=function(t,e,n){var r=e.concat(n);this.walk(t,r)},t.prototype.walkAtLeastOne=function(t,e,n){var r=[new i.Option({definition:t.definition})].concat(e,n);this.walk(t,r)},t.prototype.walkAtLeastOneSep=function(t,e,n){var r=a(t,e,n);this.walk(t,r)},t.prototype.walkMany=function(t,e,n){var r=[new i.Option({definition:t.definition})].concat(e,n);this.walk(t,r)},t.prototype.walkManySep=function(t,e,n){var r=a(t,e,n);this.walk(t,r)},t.prototype.walkOr=function(t,e,n){var o=this,a=e.concat(n);r.forEach(t.definition,(function(t){var e=new i.Alternative({definition:[t]});o.walk(e,a)}))},t}();function a(t,e,n){return[new i.Option({definition:[new i.Terminal({terminalType:t.separator})].concat(t.definition)})].concat(e,n)}e.RestWalker=o},941:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0}),e.EmbeddedActionsParser=e.CstParser=e.Parser=e.EMPTY_ALT=e.ParserDefinitionErrorType=e.DEFAULT_RULE_CONFIG=e.DEFAULT_PARSER_CONFIG=e.END_OF_FILE=void 0;var o,a=n(146),s=n(917),c=n(736),u=n(7),l=n(665),p=n(992),f=n(161),h=n(225),d=n(598),E=n(503),m=n(273),y=n(464),T=n(625),v=n(688),R=n(906),O=n(803);e.END_OF_FILE=c.createTokenInstance(c.EOF,"",NaN,NaN,NaN,NaN,NaN,NaN),Object.freeze(e.END_OF_FILE),e.DEFAULT_PARSER_CONFIG=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:u.defaultParserErrorProvider,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),e.DEFAULT_RULE_CONFIG=Object.freeze({recoveryValueFunc:function(){},resyncEnabled:!0}),(o=e.ParserDefinitionErrorType||(e.ParserDefinitionErrorType={}))[o.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",o[o.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",o[o.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",o[o.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",o[o.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",o[o.LEFT_RECURSION=5]="LEFT_RECURSION",o[o.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",o[o.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",o[o.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",o[o.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",o[o.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",o[o.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",o[o.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",e.EMPTY_ALT=function(t){return void 0===t&&(t=void 0),function(){return t}};var g=function(){function t(t,n){this.definitionErrors=[],this.selfAnalysisDone=!1;var r=this;if(r.initErrorHandler(n),r.initLexerAdapter(),r.initLooksAhead(n),r.initRecognizerEngine(t,n),r.initRecoverable(n),r.initTreeBuilder(n),r.initContentAssist(),r.initGastRecorder(n),r.initPerformanceTracer(n),a.has(n,"ignoredIssues"))throw new Error("The <ignoredIssues> IParserConfig property has been deprecated.\n\tPlease use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.\n\tSee: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES\n\tFor further details.");this.skipValidations=a.has(n,"skipValidations")?n.skipValidations:e.DEFAULT_PARSER_CONFIG.skipValidations}return t.performSelfAnalysis=function(t){throw Error("The **static** `performSelfAnalysis` method has been deprecated.\t\nUse the **instance** method with the same name instead.")},t.prototype.performSelfAnalysis=function(){var e=this;this.TRACE_INIT("performSelfAnalysis",(function(){var n;e.selfAnalysisDone=!0;var r=e.className;e.TRACE_INIT("toFastProps",(function(){a.toFastProperties(e)})),e.TRACE_INIT("Grammar Recording",(function(){try{e.enableRecording(),a.forEach(e.definedRulesNames,(function(t){var n=e[t].originalGrammarAction,r=void 0;e.TRACE_INIT(t+" Rule",(function(){r=e.topLevelRuleRecord(t,n)})),e.gastProductionsCache[t]=r}))}finally{e.disableRecording()}}));var i=[];if(e.TRACE_INIT("Grammar Resolving",(function(){i=l.resolveGrammar({rules:a.values(e.gastProductionsCache)}),e.definitionErrors=e.definitionErrors.concat(i)})),e.TRACE_INIT("Grammar Validations",(function(){if(a.isEmpty(i)&&!1===e.skipValidations){var t=l.validateGrammar({rules:a.values(e.gastProductionsCache),maxLookahead:e.maxLookahead,tokenTypes:a.values(e.tokensMap),errMsgProvider:u.defaultGrammarValidatorErrorProvider,grammarName:r});e.definitionErrors=e.definitionErrors.concat(t)}})),a.isEmpty(e.definitionErrors)&&(e.recoveryEnabled&&e.TRACE_INIT("computeAllProdsFollows",(function(){var t=s.computeAllProdsFollows(a.values(e.gastProductionsCache));e.resyncFollows=t})),e.TRACE_INIT("ComputeLookaheadFunctions",(function(){e.preComputeLookaheadFunctions(a.values(e.gastProductionsCache))}))),!t.DEFER_DEFINITION_ERRORS_HANDLING&&!a.isEmpty(e.definitionErrors))throw n=a.map(e.definitionErrors,(function(t){return t.message})),new Error("Parser Definition Errors detected:\n "+n.join("\n-------------------------------\n"))}))},t.DEFER_DEFINITION_ERRORS_HANDLING=!1,t}();e.Parser=g,O.applyMixins(g,[p.Recoverable,f.LooksAhead,h.TreeBuilder,d.LexerAdapter,m.RecognizerEngine,E.RecognizerApi,y.ErrorHandler,T.ContentAssist,v.GastRecorder,R.PerformanceTracer]);var A=function(t){function n(n,r){void 0===r&&(r=e.DEFAULT_PARSER_CONFIG);var i=a.cloneObj(r);return i.outputCst=!0,t.call(this,n,i)||this}return i(n,t),n}(g);e.CstParser=A;var _=function(t){function n(n,r){void 0===r&&(r=e.DEFAULT_PARSER_CONFIG);var i=a.cloneObj(r);return i.outputCst=!1,t.call(this,n,i)||this}return i(n,t),n}(g);e.EmbeddedActionsParser=_},625:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ContentAssist=void 0;var r=n(985),i=n(146),o=function(){function t(){}return t.prototype.initContentAssist=function(){},t.prototype.computeContentAssist=function(t,e){var n=this.gastProductionsCache[t];if(i.isUndefined(n))throw Error("Rule ->"+t+"<- does not exist in this grammar.");return r.nextPossibleTokensAfter([n],e,this.tokenMatcher,this.maxLookahead)},t.prototype.getNextPossibleTokenTypes=function(t){var e=i.first(t.ruleStack),n=this.getGAstProductions()[e];return new r.NextAfterTokenWalker(n,t).startWalking()},t}();e.ContentAssist=o},464:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ErrorHandler=void 0;var r=n(643),i=n(146),o=n(677),a=n(941),s=function(){function t(){}return t.prototype.initErrorHandler=function(t){this._errors=[],this.errorMessageProvider=i.has(t,"errorMessageProvider")?t.errorMessageProvider:a.DEFAULT_PARSER_CONFIG.errorMessageProvider},t.prototype.SAVE_ERROR=function(t){if(r.isRecognitionException(t))return t.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:i.cloneArr(this.RULE_OCCURRENCE_STACK)},this._errors.push(t),t;throw Error("Trying to save an Error which is not a RecognitionException")},Object.defineProperty(t.prototype,"errors",{get:function(){return i.cloneArr(this._errors)},set:function(t){this._errors=t},enumerable:!1,configurable:!0}),t.prototype.raiseEarlyExitException=function(t,e,n){for(var i=this.getCurrRuleFullName(),a=this.getGAstProductions()[i],s=o.getLookaheadPathsForOptionalProd(t,a,e,this.maxLookahead)[0],c=[],u=1;u<=this.maxLookahead;u++)c.push(this.LA(u));var l=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:s,actual:c,previous:this.LA(0),customUserDescription:n,ruleName:i});throw this.SAVE_ERROR(new r.EarlyExitException(l,this.LA(1),this.LA(0)))},t.prototype.raiseNoAltException=function(t,e){for(var n=this.getCurrRuleFullName(),i=this.getGAstProductions()[n],a=o.getLookaheadPathsForOr(t,i,this.maxLookahead),s=[],c=1;c<=this.maxLookahead;c++)s.push(this.LA(c));var u=this.LA(0),l=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:a,actual:s,previous:u,customUserDescription:e,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new r.NoViableAltException(l,this.LA(1),u))},t}();e.ErrorHandler=s},688:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.GastRecorder=void 0;var r=n(146),i=n(946),o=n(27),a=n(201),s=n(736),c=n(941),u=n(209),l={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(l);var p=Math.pow(2,u.BITS_FOR_OCCURRENCE_IDX)-1,f=s.createToken({name:"RECORDING_PHASE_TOKEN",pattern:o.Lexer.NA});a.augmentTokenTypes([f]);var h=s.createTokenInstance(f,"This IToken indicates the Parser is in Recording Phase\n\tSee: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details",-1,-1,-1,-1,-1,-1);Object.freeze(h);var d={name:"This CSTNode indicates the Parser is in Recording Phase\n\tSee: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details",children:{}},E=function(){function t(){}return t.prototype.initGastRecorder=function(t){this.recordingProdStack=[],this.RECORDING_PHASE=!1},t.prototype.enableRecording=function(){var t=this;this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",(function(){for(var e=function(e){var n=e>0?e:"";t["CONSUME"+n]=function(t,n){return this.consumeInternalRecord(t,e,n)},t["SUBRULE"+n]=function(t,n){return this.subruleInternalRecord(t,e,n)},t["OPTION"+n]=function(t){return this.optionInternalRecord(t,e)},t["OR"+n]=function(t){return this.orInternalRecord(t,e)},t["MANY"+n]=function(t){this.manyInternalRecord(e,t)},t["MANY_SEP"+n]=function(t){this.manySepFirstInternalRecord(e,t)},t["AT_LEAST_ONE"+n]=function(t){this.atLeastOneInternalRecord(e,t)},t["AT_LEAST_ONE_SEP"+n]=function(t){this.atLeastOneSepFirstInternalRecord(e,t)}},n=0;n<10;n++)e(n);t.consume=function(t,e,n){return this.consumeInternalRecord(e,t,n)},t.subrule=function(t,e,n){return this.subruleInternalRecord(e,t,n)},t.option=function(t,e){return this.optionInternalRecord(e,t)},t.or=function(t,e){return this.orInternalRecord(e,t)},t.many=function(t,e){this.manyInternalRecord(t,e)},t.atLeastOne=function(t,e){this.atLeastOneInternalRecord(t,e)},t.ACTION=t.ACTION_RECORD,t.BACKTRACK=t.BACKTRACK_RECORD,t.LA=t.LA_RECORD}))},t.prototype.disableRecording=function(){var t=this;this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",(function(){for(var e=0;e<10;e++){var n=e>0?e:"";delete t["CONSUME"+n],delete t["SUBRULE"+n],delete t["OPTION"+n],delete t["OR"+n],delete t["MANY"+n],delete t["MANY_SEP"+n],delete t["AT_LEAST_ONE"+n],delete t["AT_LEAST_ONE_SEP"+n]}delete t.consume,delete t.subrule,delete t.option,delete t.or,delete t.many,delete t.atLeastOne,delete t.ACTION,delete t.BACKTRACK,delete t.LA}))},t.prototype.ACTION_RECORD=function(t){},t.prototype.BACKTRACK_RECORD=function(t,e){return function(){return!0}},t.prototype.LA_RECORD=function(t){return c.END_OF_FILE},t.prototype.topLevelRuleRecord=function(t,e){try{var n=new i.Rule({definition:[],name:t});return n.name=t,this.recordingProdStack.push(n),e.call(this),this.recordingProdStack.pop(),n}catch(t){if(!0!==t.KNOWN_RECORDER_ERROR)try{t.message=t.message+'\n\t This error was thrown during the "grammar recording phase" For more info see:\n\thttps://chevrotain.io/docs/guide/internals.html#grammar-recording'}catch(e){throw t}throw t}},t.prototype.optionInternalRecord=function(t,e){return m.call(this,i.Option,t,e)},t.prototype.atLeastOneInternalRecord=function(t,e){m.call(this,i.RepetitionMandatory,e,t)},t.prototype.atLeastOneSepFirstInternalRecord=function(t,e){m.call(this,i.RepetitionMandatoryWithSeparator,e,t,!0)},t.prototype.manyInternalRecord=function(t,e){m.call(this,i.Repetition,e,t)},t.prototype.manySepFirstInternalRecord=function(t,e){m.call(this,i.RepetitionWithSeparator,e,t,!0)},t.prototype.orInternalRecord=function(t,e){return y.call(this,t,e)},t.prototype.subruleInternalRecord=function(t,e,n){if(v(e),!t||!1===r.has(t,"ruleName")){var o=new Error("<SUBRULE"+T(e)+"> argument is invalid expecting a Parser method reference but got: <"+JSON.stringify(t)+">\n inside top level rule: <"+this.recordingProdStack[0].name+">");throw o.KNOWN_RECORDER_ERROR=!0,o}var a=r.peek(this.recordingProdStack),s=t.ruleName,c=new i.NonTerminal({idx:e,nonTerminalName:s,referencedRule:void 0});return a.definition.push(c),this.outputCst?d:l},t.prototype.consumeInternalRecord=function(t,e,n){if(v(e),!a.hasShortKeyProperty(t)){var o=new Error("<CONSUME"+T(e)+"> argument is invalid expecting a TokenType reference but got: <"+JSON.stringify(t)+">\n inside top level rule: <"+this.recordingProdStack[0].name+">");throw o.KNOWN_RECORDER_ERROR=!0,o}var s=r.peek(this.recordingProdStack),c=new i.Terminal({idx:e,terminalType:t});return s.definition.push(c),h},t}();function m(t,e,n,i){void 0===i&&(i=!1),v(n);var o=r.peek(this.recordingProdStack),a=r.isFunction(e)?e:e.DEF,s=new t({definition:[],idx:n});return i&&(s.separator=e.SEP),r.has(e,"MAX_LOOKAHEAD")&&(s.maxLookahead=e.MAX_LOOKAHEAD),this.recordingProdStack.push(s),a.call(this),o.definition.push(s),this.recordingProdStack.pop(),l}function y(t,e){var n=this;v(e);var o=r.peek(this.recordingProdStack),a=!1===r.isArray(t),s=!1===a?t:t.DEF,c=new i.Alternation({definition:[],idx:e,ignoreAmbiguities:a&&!0===t.IGNORE_AMBIGUITIES});r.has(t,"MAX_LOOKAHEAD")&&(c.maxLookahead=t.MAX_LOOKAHEAD);var u=r.some(s,(function(t){return r.isFunction(t.GATE)}));return c.hasPredicates=u,o.definition.push(c),r.forEach(s,(function(t){var e=new i.Alternative({definition:[]});c.definition.push(e),r.has(t,"IGNORE_AMBIGUITIES")?e.ignoreAmbiguities=t.IGNORE_AMBIGUITIES:r.has(t,"GATE")&&(e.ignoreAmbiguities=!0),n.recordingProdStack.push(e),t.ALT.call(n),n.recordingProdStack.pop()})),l}function T(t){return 0===t?"":""+t}function v(t){if(t<0||t>p){var e=new Error("Invalid DSL Method idx value: <"+t+">\n\tIdx value must be a none negative value smaller than "+(p+1));throw e.KNOWN_RECORDER_ERROR=!0,e}}e.GastRecorder=E},598:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.LexerAdapter=void 0;var r=n(941),i=function(){function t(){}return t.prototype.initLexerAdapter=function(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1},Object.defineProperty(t.prototype,"input",{get:function(){return this.tokVector},set:function(t){if(!0!==this.selfAnalysisDone)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=t,this.tokVectorLength=t.length},enumerable:!1,configurable:!0}),t.prototype.SKIP_TOKEN=function(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):r.END_OF_FILE},t.prototype.LA=function(t){var e=this.currIdx+t;return e<0||this.tokVectorLength<=e?r.END_OF_FILE:this.tokVector[e]},t.prototype.consumeToken=function(){this.currIdx++},t.prototype.exportLexerState=function(){return this.currIdx},t.prototype.importLexerState=function(t){this.currIdx=t},t.prototype.resetLexerState=function(){this.currIdx=-1},t.prototype.moveToTerminatedState=function(){this.currIdx=this.tokVector.length-1},t.prototype.getLexerPosition=function(){return this.exportLexerState()},t}();e.LexerAdapter=i},161:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.LooksAhead=void 0;var r=n(677),i=n(146),o=n(941),a=n(209),s=n(651),c=function(){function t(){}return t.prototype.initLooksAhead=function(t){this.dynamicTokensEnabled=i.has(t,"dynamicTokensEnabled")?t.dynamicTokensEnabled:o.DEFAULT_PARSER_CONFIG.dynamicTokensEnabled,this.maxLookahead=i.has(t,"maxLookahead")?t.maxLookahead:o.DEFAULT_PARSER_CONFIG.maxLookahead,this.lookAheadFuncsCache=i.isES2015MapSupported()?new Map:[],i.isES2015MapSupported()?(this.getLaFuncFromCache=this.getLaFuncFromMap,this.setLaFuncCache=this.setLaFuncCacheUsingMap):(this.getLaFuncFromCache=this.getLaFuncFromObj,this.setLaFuncCache=this.setLaFuncUsingObj)},t.prototype.preComputeLookaheadFunctions=function(t){var e=this;i.forEach(t,(function(t){e.TRACE_INIT(t.name+" Rule Lookahead",(function(){var n=s.collectMethods(t),o=n.alternation,c=n.repetition,u=n.option,l=n.repetitionMandatory,p=n.repetitionMandatoryWithSeparator,f=n.repetitionWithSeparator;i.forEach(o,(function(n){var i=0===n.idx?"":n.idx;e.TRACE_INIT(""+s.getProductionDslName(n)+i,(function(){var i=r.buildLookaheadFuncForOr(n.idx,t,n.maxLookahead||e.maxLookahead,n.hasPredicates,e.dynamicTokensEnabled,e.lookAheadBuilderForAlternatives),o=a.getKeyForAutomaticLookahead(e.fullRuleNameToShort[t.name],a.OR_IDX,n.idx);e.setLaFuncCache(o,i)}))})),i.forEach(c,(function(n){e.computeLookaheadFunc(t,n.idx,a.MANY_IDX,r.PROD_TYPE.REPETITION,n.maxLookahead,s.getProductionDslName(n))})),i.forEach(u,(function(n){e.computeLookaheadFunc(t,n.idx,a.OPTION_IDX,r.PROD_TYPE.OPTION,n.maxLookahead,s.getProductionDslName(n))})),i.forEach(l,(function(n){e.computeLookaheadFunc(t,n.idx,a.AT_LEAST_ONE_IDX,r.PROD_TYPE.REPETITION_MANDATORY,n.maxLookahead,s.getProductionDslName(n))})),i.forEach(p,(function(n){e.computeLookaheadFunc(t,n.idx,a.AT_LEAST_ONE_SEP_IDX,r.PROD_TYPE.REPETITION_MANDATORY_WITH_SEPARATOR,n.maxLookahead,s.getProductionDslName(n))})),i.forEach(f,(function(n){e.computeLookaheadFunc(t,n.idx,a.MANY_SEP_IDX,r.PROD_TYPE.REPETITION_WITH_SEPARATOR,n.maxLookahead,s.getProductionDslName(n))}))}))}))},t.prototype.computeLookaheadFunc=function(t,e,n,i,o,s){var c=this;this.TRACE_INIT(""+s+(0===e?"":e),(function(){var s=r.buildLookaheadFuncForOptionalProd(e,t,o||c.maxLookahead,c.dynamicTokensEnabled,i,c.lookAheadBuilderForOptional),u=a.getKeyForAutomaticLookahead(c.fullRuleNameToShort[t.name],n,e);c.setLaFuncCache(u,s)}))},t.prototype.lookAheadBuilderForOptional=function(t,e,n){return r.buildSingleAlternativeLookaheadFunction(t,e,n)},t.prototype.lookAheadBuilderForAlternatives=function(t,e,n,i){return r.buildAlternativesLookAheadFunc(t,e,n,i)},t.prototype.getKeyForAutomaticLookahead=function(t,e){var n=this.getLastExplicitRuleShortName();return a.getKeyForAutomaticLookahead(n,t,e)},t.prototype.getLaFuncFromCache=function(t){},t.prototype.getLaFuncFromMap=function(t){return this.lookAheadFuncsCache.get(t)},t.prototype.getLaFuncFromObj=function(t){return this.lookAheadFuncsCache[t]},t.prototype.setLaFuncCache=function(t,e){},t.prototype.setLaFuncCacheUsingMap=function(t,e){this.lookAheadFuncsCache.set(t,e)},t.prototype.setLaFuncUsingObj=function(t,e){this.lookAheadFuncsCache[t]=e},t}();e.LooksAhead=c},906:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.PerformanceTracer=void 0;var r=n(146),i=n(941),o=function(){function t(){}return t.prototype.initPerformanceTracer=function(t){if(r.has(t,"traceInitPerf")){var e=t.traceInitPerf,n="number"==typeof e;this.traceInitMaxIdent=n?e:1/0,this.traceInitPerf=n?e>0:e}else this.traceInitMaxIdent=0,this.traceInitPerf=i.DEFAULT_PARSER_CONFIG.traceInitPerf;this.traceInitIndent=-1},t.prototype.TRACE_INIT=function(t,e){if(!0===this.traceInitPerf){this.traceInitIndent++;var n=new Array(this.traceInitIndent+1).join("\t");this.traceInitIndent<this.traceInitMaxIdent&&console.log(n+"--\x3e <"+t+">");var i=r.timer(e),o=i.time,a=i.value,s=o>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&s(n+"<-- <"+t+"> time: "+o+"ms"),this.traceInitIndent--,a}return e()},t}();e.PerformanceTracer=o},503:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.RecognizerApi=void 0;var r=n(146),i=n(643),o=n(941),a=n(7),s=n(870),c=n(946),u=function(){function t(){}return t.prototype.ACTION=function(t){return t.call(this)},t.prototype.consume=function(t,e,n){return this.consumeInternal(e,t,n)},t.prototype.subrule=function(t,e,n){return this.subruleInternal(e,t,n)},t.prototype.option=function(t,e){return this.optionInternal(e,t)},t.prototype.or=function(t,e){return this.orInternal(e,t)},t.prototype.many=function(t,e){return this.manyInternal(t,e)},t.prototype.atLeastOne=function(t,e){return this.atLeastOneInternal(t,e)},t.prototype.CONSUME=function(t,e){return this.consumeInternal(t,0,e)},t.prototype.CONSUME1=function(t,e){return this.consumeInternal(t,1,e)},t.prototype.CONSUME2=function(t,e){return this.consumeInternal(t,2,e)},t.prototype.CONSUME3=function(t,e){return this.consumeInternal(t,3,e)},t.prototype.CONSUME4=function(t,e){return this.consumeInternal(t,4,e)},t.prototype.CONSUME5=function(t,e){return this.consumeInternal(t,5,e)},t.prototype.CONSUME6=function(t,e){return this.consumeInternal(t,6,e)},t.prototype.CONSUME7=function(t,e){return this.consumeInternal(t,7,e)},t.prototype.CONSUME8=function(t,e){return this.consumeInternal(t,8,e)},t.prototype.CONSUME9=function(t,e){return this.consumeInternal(t,9,e)},t.prototype.SUBRULE=function(t,e){return this.subruleInternal(t,0,e)},t.prototype.SUBRULE1=function(t,e){return this.subruleInternal(t,1,e)},t.prototype.SUBRULE2=function(t,e){return this.subruleInternal(t,2,e)},t.prototype.SUBRULE3=function(t,e){return this.subruleInternal(t,3,e)},t.prototype.SUBRULE4=function(t,e){return this.subruleInternal(t,4,e)},t.prototype.SUBRULE5=function(t,e){return this.subruleInternal(t,5,e)},t.prototype.SUBRULE6=function(t,e){return this.subruleInternal(t,6,e)},t.prototype.SUBRULE7=function(t,e){return this.subruleInternal(t,7,e)},t.prototype.SUBRULE8=function(t,e){return this.subruleInternal(t,8,e)},t.prototype.SUBRULE9=function(t,e){return this.subruleInternal(t,9,e)},t.prototype.OPTION=function(t){return this.optionInternal(t,0)},t.prototype.OPTION1=function(t){return this.optionInternal(t,1)},t.prototype.OPTION2=function(t){return this.optionInternal(t,2)},t.prototype.OPTION3=function(t){return this.optionInternal(t,3)},t.prototype.OPTION4=function(t){return this.optionInternal(t,4)},t.prototype.OPTION5=function(t){return this.optionInternal(t,5)},t.prototype.OPTION6=function(t){return this.optionInternal(t,6)},t.prototype.OPTION7=function(t){return this.optionInternal(t,7)},t.prototype.OPTION8=function(t){return this.optionInternal(t,8)},t.prototype.OPTION9=function(t){return this.optionInternal(t,9)},t.prototype.OR=function(t){return this.orInternal(t,0)},t.prototype.OR1=function(t){return this.orInternal(t,1)},t.prototype.OR2=function(t){return this.orInternal(t,2)},t.prototype.OR3=function(t){return this.orInternal(t,3)},t.prototype.OR4=function(t){return this.orInternal(t,4)},t.prototype.OR5=function(t){return this.orInternal(t,5)},t.prototype.OR6=function(t){return this.orInternal(t,6)},t.prototype.OR7=function(t){return this.orInternal(t,7)},t.prototype.OR8=function(t){return this.orInternal(t,8)},t.prototype.OR9=function(t){return this.orInternal(t,9)},t.prototype.MANY=function(t){this.manyInternal(0,t)},t.prototype.MANY1=function(t){this.manyInternal(1,t)},t.prototype.MANY2=function(t){this.manyInternal(2,t)},t.prototype.MANY3=function(t){this.manyInternal(3,t)},t.prototype.MANY4=function(t){this.manyInternal(4,t)},t.prototype.MANY5=function(t){this.manyInternal(5,t)},t.prototype.MANY6=function(t){this.manyInternal(6,t)},t.prototype.MANY7=function(t){this.manyInternal(7,t)},t.prototype.MANY8=function(t){this.manyInternal(8,t)},t.prototype.MANY9=function(t){this.manyInternal(9,t)},t.prototype.MANY_SEP=function(t){this.manySepFirstInternal(0,t)},t.prototype.MANY_SEP1=function(t){this.manySepFirstInternal(1,t)},t.prototype.MANY_SEP2=function(t){this.manySepFirstInternal(2,t)},t.prototype.MANY_SEP3=function(t){this.manySepFirstInternal(3,t)},t.prototype.MANY_SEP4=function(t){this.manySepFirstInternal(4,t)},t.prototype.MANY_SEP5=function(t){this.manySepFirstInternal(5,t)},t.prototype.MANY_SEP6=function(t){this.manySepFirstInternal(6,t)},t.prototype.MANY_SEP7=function(t){this.manySepFirstInternal(7,t)},t.prototype.MANY_SEP8=function(t){this.manySepFirstInternal(8,t)},t.prototype.MANY_SEP9=function(t){this.manySepFirstInternal(9,t)},t.prototype.AT_LEAST_ONE=function(t){this.atLeastOneInternal(0,t)},t.prototype.AT_LEAST_ONE1=function(t){return this.atLeastOneInternal(1,t)},t.prototype.AT_LEAST_ONE2=function(t){this.atLeastOneInternal(2,t)},t.prototype.AT_LEAST_ONE3=function(t){this.atLeastOneInternal(3,t)},t.prototype.AT_LEAST_ONE4=function(t){this.atLeastOneInternal(4,t)},t.prototype.AT_LEAST_ONE5=function(t){this.atLeastOneInternal(5,t)},t.prototype.AT_LEAST_ONE6=function(t){this.atLeastOneInternal(6,t)},t.prototype.AT_LEAST_ONE7=function(t){this.atLeastOneInternal(7,t)},t.prototype.AT_LEAST_ONE8=function(t){this.atLeastOneInternal(8,t)},t.prototype.AT_LEAST_ONE9=function(t){this.atLeastOneInternal(9,t)},t.prototype.AT_LEAST_ONE_SEP=function(t){this.atLeastOneSepFirstInternal(0,t)},t.prototype.AT_LEAST_ONE_SEP1=function(t){this.atLeastOneSepFirstInternal(1,t)},t.prototype.AT_LEAST_ONE_SEP2=function(t){this.atLeastOneSepFirstInternal(2,t)},t.prototype.AT_LEAST_ONE_SEP3=function(t){this.atLeastOneSepFirstInternal(3,t)},t.prototype.AT_LEAST_ONE_SEP4=function(t){this.atLeastOneSepFirstInternal(4,t)},t.prototype.AT_LEAST_ONE_SEP5=function(t){this.atLeastOneSepFirstInternal(5,t)},t.prototype.AT_LEAST_ONE_SEP6=function(t){this.atLeastOneSepFirstInternal(6,t)},t.prototype.AT_LEAST_ONE_SEP7=function(t){this.atLeastOneSepFirstInternal(7,t)},t.prototype.AT_LEAST_ONE_SEP8=function(t){this.atLeastOneSepFirstInternal(8,t)},t.prototype.AT_LEAST_ONE_SEP9=function(t){this.atLeastOneSepFirstInternal(9,t)},t.prototype.RULE=function(t,e,n){if(void 0===n&&(n=o.DEFAULT_RULE_CONFIG),r.contains(this.definedRulesNames,t)){var i={message:a.defaultGrammarValidatorErrorProvider.buildDuplicateRuleNameError({topLevelRule:t,grammarName:this.className}),type:o.ParserDefinitionErrorType.DUPLICATE_RULE_NAME,ruleName:t};this.definitionErrors.push(i)}this.definedRulesNames.push(t);var s=this.defineRule(t,e,n);return this[t]=s,s},t.prototype.OVERRIDE_RULE=function(t,e,n){void 0===n&&(n=o.DEFAULT_RULE_CONFIG);var r=[];r=r.concat(s.validateRuleIsOverridden(t,this.definedRulesNames,this.className)),this.definitionErrors=this.definitionErrors.concat(r);var i=this.defineRule(t,e,n);return this[t]=i,i},t.prototype.BACKTRACK=function(t,e){return function(){this.isBackTrackingStack.push(1);var n=this.saveRecogState();try{return t.apply(this,e),!0}catch(t){if(i.isRecognitionException(t))return!1;throw t}finally{this.reloadRecogState(n),this.isBackTrackingStack.pop()}}},t.prototype.getGAstProductions=function(){return this.gastProductionsCache},t.prototype.getSerializedGastProductions=function(){return c.serializeGrammar(r.values(this.gastProductionsCache))},t}();e.RecognizerApi=u},273:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.RecognizerEngine=void 0;var r=n(146),i=n(209),o=n(643),a=n(677),s=n(985),c=n(941),u=n(992),l=n(736),p=n(201),f=n(105),h=function(){function t(){}return t.prototype.initRecognizerEngine=function(t,e){if(this.className=f.classNameFromInstance(this),this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=p.tokenStructuredMatcherNoCategories,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},r.has(e,"serializedGrammar"))throw Error("The Parser's configuration can no longer contain a <serializedGrammar> property.\n\tSee: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0\n\tFor Further details.");if(r.isArray(t)){if(r.isEmpty(t))throw Error("A Token Vocabulary cannot be empty.\n\tNote that the first argument for the parser constructor\n\tis no longer a Token vector (since v4.0).");if("number"==typeof t[0].startOffset)throw Error("The Parser constructor no longer accepts a token vector as the first argument.\n\tSee: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0\n\tFor Further details.")}if(r.isArray(t))this.tokensMap=r.reduce(t,(function(t,e){return t[e.name]=e,t}),{});else if(r.has(t,"modes")&&r.every(r.flatten(r.values(t.modes)),p.isTokenType)){var n=r.flatten(r.values(t.modes)),i=r.uniq(n);this.tokensMap=r.reduce(i,(function(t,e){return t[e.name]=e,t}),{})}else{if(!r.isObject(t))throw new Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap=r.cloneObj(t)}this.tokensMap.EOF=l.EOF;var o=r.every(r.values(t),(function(t){return r.isEmpty(t.categoryMatches)}));this.tokenMatcher=o?p.tokenStructuredMatcherNoCategories:p.tokenStructuredMatcher,p.augmentTokenTypes(r.values(this.tokensMap))},t.prototype.defineRule=function(t,e,n){if(this.selfAnalysisDone)throw Error("Grammar rule <"+t+"> may not be defined after the 'performSelfAnalysis' method has been called'\nMake sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.");var o=r.has(n,"resyncEnabled")?n.resyncEnabled:c.DEFAULT_RULE_CONFIG.resyncEnabled,a=r.has(n,"recoveryValueFunc")?n.recoveryValueFunc:c.DEFAULT_RULE_CONFIG.recoveryValueFunc,s=this.ruleShortNameIdx<<i.BITS_FOR_METHOD_TYPE+i.BITS_FOR_OCCURRENCE_IDX;function u(t){try{if(!0===this.outputCst){e.apply(this,t);var n=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(n),n}return e.apply(this,t)}catch(t){return this.invokeRuleCatch(t,o,a)}finally{this.ruleFinallyStateUpdate()}}this.ruleShortNameIdx++,this.shortRuleNameToFull[s]=t,this.fullRuleNameToShort[t]=s;var l=function(e,n){return void 0===e&&(e=0),this.ruleInvocationStateUpdate(s,t,e),u.call(this,n)};return l.ruleName=t,l.originalGrammarAction=e,l},t.prototype.invokeRuleCatch=function(t,e,n){var r=1===this.RULE_STACK.length,i=e&&!this.isBackTracking()&&this.recoveryEnabled;if(o.isRecognitionException(t)){var a=t;if(i){var s,c=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(c))return a.resyncedTokens=this.reSyncTo(c),this.outputCst?((s=this.CST_STACK[this.CST_STACK.length-1]).recoveredNode=!0,s):n();throw this.outputCst&&((s=this.CST_STACK[this.CST_STACK.length-1]).recoveredNode=!0,a.partialCstResult=s),a}if(r)return this.moveToTerminatedState(),n();throw a}throw t},t.prototype.optionInternal=function(t,e){var n=this.getKeyForAutomaticLookahead(i.OPTION_IDX,e);return this.optionInternalLogic(t,e,n)},t.prototype.optionInternalLogic=function(t,e,n){var r,i,o=this,a=this.getLaFuncFromCache(n);if(void 0!==t.DEF){if(r=t.DEF,void 0!==(i=t.GATE)){var s=a;a=function(){return i.call(o)&&s.call(o)}}}else r=t;if(!0===a.call(this))return r.call(this)},t.prototype.atLeastOneInternal=function(t,e){var n=this.getKeyForAutomaticLookahead(i.AT_LEAST_ONE_IDX,t);return this.atLeastOneInternalLogic(t,e,n)},t.prototype.atLeastOneInternalLogic=function(t,e,n){var r,o,c=this,u=this.getLaFuncFromCache(n);if(void 0!==e.DEF){if(r=e.DEF,void 0!==(o=e.GATE)){var l=u;u=function(){return o.call(c)&&l.call(c)}}}else r=e;if(!0!==u.call(this))throw this.raiseEarlyExitException(t,a.PROD_TYPE.REPETITION_MANDATORY,e.ERR_MSG);for(var p=this.doSingleRepetition(r);!0===u.call(this)&&!0===p;)p=this.doSingleRepetition(r);this.attemptInRepetitionRecovery(this.atLeastOneInternal,[t,e],u,i.AT_LEAST_ONE_IDX,t,s.NextTerminalAfterAtLeastOneWalker)},t.prototype.atLeastOneSepFirstInternal=function(t,e){var n=this.getKeyForAutomaticLookahead(i.AT_LEAST_ONE_SEP_IDX,t);this.atLeastOneSepFirstInternalLogic(t,e,n)},t.prototype.atLeastOneSepFirstInternalLogic=function(t,e,n){var r=this,o=e.DEF,c=e.SEP;if(!0!==this.getLaFuncFromCache(n).call(this))throw this.raiseEarlyExitException(t,a.PROD_TYPE.REPETITION_MANDATORY_WITH_SEPARATOR,e.ERR_MSG);o.call(this);for(var u=function(){return r.tokenMatcher(r.LA(1),c)};!0===this.tokenMatcher(this.LA(1),c);)this.CONSUME(c),o.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[t,c,u,o,s.NextTerminalAfterAtLeastOneSepWalker],u,i.AT_LEAST_ONE_SEP_IDX,t,s.NextTerminalAfterAtLeastOneSepWalker)},t.prototype.manyInternal=function(t,e){var n=this.getKeyForAutomaticLookahead(i.MANY_IDX,t);return this.manyInternalLogic(t,e,n)},t.prototype.manyInternalLogic=function(t,e,n){var r,o,a=this,c=this.getLaFuncFromCache(n);if(void 0!==e.DEF){if(r=e.DEF,void 0!==(o=e.GATE)){var u=c;c=function(){return o.call(a)&&u.call(a)}}}else r=e;for(var l=!0;!0===c.call(this)&&!0===l;)l=this.doSingleRepetition(r);this.attemptInRepetitionRecovery(this.manyInternal,[t,e],c,i.MANY_IDX,t,s.NextTerminalAfterManyWalker,l)},t.prototype.manySepFirstInternal=function(t,e){var n=this.getKeyForAutomaticLookahead(i.MANY_SEP_IDX,t);this.manySepFirstInternalLogic(t,e,n)},t.prototype.manySepFirstInternalLogic=function(t,e,n){var r=this,o=e.DEF,a=e.SEP;if(!0===this.getLaFuncFromCache(n).call(this)){o.call(this);for(var c=function(){return r.tokenMatcher(r.LA(1),a)};!0===this.tokenMatcher(this.LA(1),a);)this.CONSUME(a),o.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[t,a,c,o,s.NextTerminalAfterManySepWalker],c,i.MANY_SEP_IDX,t,s.NextTerminalAfterManySepWalker)}},t.prototype.repetitionSepSecondInternal=function(t,e,n,r,o){for(;n();)this.CONSUME(e),r.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[t,e,n,r,o],n,i.AT_LEAST_ONE_SEP_IDX,t,o)},t.prototype.doSingleRepetition=function(t){var e=this.getLexerPosition();return t.call(this),this.getLexerPosition()>e},t.prototype.orInternal=function(t,e){var n=this.getKeyForAutomaticLookahead(i.OR_IDX,e),o=r.isArray(t)?t:t.DEF,a=this.getLaFuncFromCache(n).call(this,o);if(void 0!==a)return o[a].ALT.call(this);this.raiseNoAltException(e,t.ERR_MSG)},t.prototype.ruleFinallyStateUpdate=function(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),0===this.RULE_STACK.length&&!1===this.isAtEndOfInput()){var t=this.LA(1),e=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:t,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new o.NotAllInputParsedException(e,t))}},t.prototype.subruleInternal=function(t,e,n){var r;try{var i=void 0!==n?n.ARGS:void 0;return r=t.call(this,e,i),this.cstPostNonTerminal(r,void 0!==n&&void 0!==n.LABEL?n.LABEL:t.ruleName),r}catch(e){this.subruleInternalError(e,n,t.ruleName)}},t.prototype.subruleInternalError=function(t,e,n){throw o.isRecognitionException(t)&&void 0!==t.partialCstResult&&(this.cstPostNonTerminal(t.partialCstResult,void 0!==e&&void 0!==e.LABEL?e.LABEL:n),delete t.partialCstResult),t},t.prototype.consumeInternal=function(t,e,n){var r;try{var i=this.LA(1);!0===this.tokenMatcher(i,t)?(this.consumeToken(),r=i):this.consumeInternalError(t,i,n)}catch(n){r=this.consumeInternalRecovery(t,e,n)}return this.cstPostTerminal(void 0!==n&&void 0!==n.LABEL?n.LABEL:t.name,r),r},t.prototype.consumeInternalError=function(t,e,n){var r,i=this.LA(0);throw r=void 0!==n&&n.ERR_MSG?n.ERR_MSG:this.errorMessageProvider.buildMismatchTokenMessage({expected:t,actual:e,previous:i,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new o.MismatchedTokenException(r,e,i))},t.prototype.consumeInternalRecovery=function(t,e,n){if(!this.recoveryEnabled||"MismatchedTokenException"!==n.name||this.isBackTracking())throw n;var r=this.getFollowsForInRuleRecovery(t,e);try{return this.tryInRuleRecovery(t,r)}catch(t){throw t.name===u.IN_RULE_RECOVERY_EXCEPTION?n:t}},t.prototype.saveRecogState=function(){var t=this.errors,e=r.cloneArr(this.RULE_STACK);return{errors:t,lexerState:this.exportLexerState(),RULE_STACK:e,CST_STACK:this.CST_STACK}},t.prototype.reloadRecogState=function(t){this.errors=t.errors,this.importLexerState(t.lexerState),this.RULE_STACK=t.RULE_STACK},t.prototype.ruleInvocationStateUpdate=function(t,e,n){this.RULE_OCCURRENCE_STACK.push(n),this.RULE_STACK.push(t),this.cstInvocationStateUpdate(e,t)},t.prototype.isBackTracking=function(){return 0!==this.isBackTrackingStack.length},t.prototype.getCurrRuleFullName=function(){var t=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[t]},t.prototype.shortRuleNameToFullName=function(t){return this.shortRuleNameToFull[t]},t.prototype.isAtEndOfInput=function(){return this.tokenMatcher(this.LA(1),l.EOF)},t.prototype.reset=function(){this.resetLexerState(),this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]},t}();e.RecognizerEngine=h},992:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.attemptInRepetitionRecovery=e.Recoverable=e.InRuleRecoveryException=e.IN_RULE_RECOVERY_EXCEPTION=e.EOF_FOLLOW_KEY=void 0;var r=n(736),i=n(146),o=n(643),a=n(710),s=n(941);function c(t){this.name=e.IN_RULE_RECOVERY_EXCEPTION,this.message=t}e.EOF_FOLLOW_KEY={},e.IN_RULE_RECOVERY_EXCEPTION="InRuleRecoveryException",e.InRuleRecoveryException=c,c.prototype=Error.prototype;var u=function(){function t(){}return t.prototype.initRecoverable=function(t){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=i.has(t,"recoveryEnabled")?t.recoveryEnabled:s.DEFAULT_PARSER_CONFIG.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=l)},t.prototype.getTokenToInsert=function(t){var e=r.createTokenInstance(t,"",NaN,NaN,NaN,NaN,NaN,NaN);return e.isInsertedInRecovery=!0,e},t.prototype.canTokenTypeBeInsertedInRecovery=function(t){return!0},t.prototype.tryInRepetitionRecovery=function(t,e,n,r){for(var a=this,s=this.findReSyncTokenType(),c=this.exportLexerState(),u=[],l=!1,p=this.LA(1),f=this.LA(1),h=function(){var t=a.LA(0),e=a.errorMessageProvider.buildMismatchTokenMessage({expected:r,actual:p,previous:t,ruleName:a.getCurrRuleFullName()}),n=new o.MismatchedTokenException(e,p,a.LA(0));n.resyncedTokens=i.dropRight(u),a.SAVE_ERROR(n)};!l;){if(this.tokenMatcher(f,r))return void h();if(n.call(this))return h(),void t.apply(this,e);this.tokenMatcher(f,s)?l=!0:(f=this.SKIP_TOKEN(),this.addToResyncTokens(f,u))}this.importLexerState(c)},t.prototype.shouldInRepetitionRecoveryBeTried=function(t,e,n){return!1!==n&&void 0!==t&&void 0!==e&&!this.tokenMatcher(this.LA(1),t)&&!this.isBackTracking()&&!this.canPerformInRuleRecovery(t,this.getFollowsForInRuleRecovery(t,e))},t.prototype.getFollowsForInRuleRecovery=function(t,e){var n=this.getCurrentGrammarPath(t,e);return this.getNextPossibleTokenTypes(n)},t.prototype.tryInRuleRecovery=function(t,e){if(this.canRecoverWithSingleTokenInsertion(t,e))return this.getTokenToInsert(t);if(this.canRecoverWithSingleTokenDeletion(t)){var n=this.SKIP_TOKEN();return this.consumeToken(),n}throw new c("sad sad panda")},t.prototype.canPerformInRuleRecovery=function(t,e){return this.canRecoverWithSingleTokenInsertion(t,e)||this.canRecoverWithSingleTokenDeletion(t)},t.prototype.canRecoverWithSingleTokenInsertion=function(t,e){var n=this;if(!this.canTokenTypeBeInsertedInRecovery(t))return!1;if(i.isEmpty(e))return!1;var r=this.LA(1);return void 0!==i.find(e,(function(t){return n.tokenMatcher(r,t)}))},t.prototype.canRecoverWithSingleTokenDeletion=function(t){return this.tokenMatcher(this.LA(2),t)},t.prototype.isInCurrentRuleReSyncSet=function(t){var e=this.getCurrFollowKey(),n=this.getFollowSetFromFollowKey(e);return i.contains(n,t)},t.prototype.findReSyncTokenType=function(){for(var t=this.flattenFollowSet(),e=this.LA(1),n=2;;){var r=e.tokenType;if(i.contains(t,r))return r;e=this.LA(n),n++}},t.prototype.getCurrFollowKey=function(){if(1===this.RULE_STACK.length)return e.EOF_FOLLOW_KEY;var t=this.getLastExplicitRuleShortName(),n=this.getLastExplicitRuleOccurrenceIndex(),r=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(t),idxInCallingRule:n,inRule:this.shortRuleNameToFullName(r)}},t.prototype.buildFullFollowKeyStack=function(){var t=this,n=this.RULE_STACK,r=this.RULE_OCCURRENCE_STACK;return i.map(n,(function(i,o){return 0===o?e.EOF_FOLLOW_KEY:{ruleName:t.shortRuleNameToFullName(i),idxInCallingRule:r[o],inRule:t.shortRuleNameToFullName(n[o-1])}}))},t.prototype.flattenFollowSet=function(){var t=this,e=i.map(this.buildFullFollowKeyStack(),(function(e){return t.getFollowSetFromFollowKey(e)}));return i.flatten(e)},t.prototype.getFollowSetFromFollowKey=function(t){if(t===e.EOF_FOLLOW_KEY)return[r.EOF];var n=t.ruleName+t.idxInCallingRule+a.IN+t.inRule;return this.resyncFollows[n]},t.prototype.addToResyncTokens=function(t,e){return this.tokenMatcher(t,r.EOF)||e.push(t),e},t.prototype.reSyncTo=function(t){for(var e=[],n=this.LA(1);!1===this.tokenMatcher(n,t);)n=this.SKIP_TOKEN(),this.addToResyncTokens(n,e);return i.dropRight(e)},t.prototype.attemptInRepetitionRecovery=function(t,e,n,r,i,o,a){},t.prototype.getCurrentGrammarPath=function(t,e){return{ruleStack:this.getHumanReadableRuleStack(),occurrenceStack:i.cloneArr(this.RULE_OCCURRENCE_STACK),lastTok:t,lastTokOccurrence:e}},t.prototype.getHumanReadableRuleStack=function(){var t=this;return i.map(this.RULE_STACK,(function(e){return t.shortRuleNameToFullName(e)}))},t}();function l(t,e,n,i,o,a,s){var c=this.getKeyForAutomaticLookahead(i,o),u=this.firstAfterRepMap[c];if(void 0===u){var l=this.getCurrRuleFullName();u=new a(this.getGAstProductions()[l],o).startWalking(),this.firstAfterRepMap[c]=u}var p=u.token,f=u.occurrence,h=u.isEndOfRule;1===this.RULE_STACK.length&&h&&void 0===p&&(p=r.EOF,f=1),this.shouldInRepetitionRecoveryBeTried(p,f,s)&&this.tryInRepetitionRecovery(t,e,n,p)}e.Recoverable=u,e.attemptInRepetitionRecovery=l},225:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TreeBuilder=void 0;var r=n(485),i=n(146),o=n(169),a=n(941),s=function(){function t(){}return t.prototype.initTreeBuilder=function(t){if(this.CST_STACK=[],this.outputCst=t.outputCst,this.nodeLocationTracking=i.has(t,"nodeLocationTracking")?t.nodeLocationTracking:a.DEFAULT_PARSER_CONFIG.nodeLocationTracking,this.outputCst)if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=r.setNodeLocationFull,this.setNodeLocationFromNode=r.setNodeLocationFull,this.cstPostRule=i.NOOP,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=i.NOOP,this.setNodeLocationFromNode=i.NOOP,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=r.setNodeLocationOnlyOffset,this.setNodeLocationFromNode=r.setNodeLocationOnlyOffset,this.cstPostRule=i.NOOP,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=i.NOOP,this.setNodeLocationFromNode=i.NOOP,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else{if(!/none/i.test(this.nodeLocationTracking))throw Error('Invalid <nodeLocationTracking> config option: "'+t.nodeLocationTracking+'"');this.setNodeLocationFromToken=i.NOOP,this.setNodeLocationFromNode=i.NOOP,this.cstPostRule=i.NOOP,this.setInitialNodeLocation=i.NOOP}else this.cstInvocationStateUpdate=i.NOOP,this.cstFinallyStateUpdate=i.NOOP,this.cstPostTerminal=i.NOOP,this.cstPostNonTerminal=i.NOOP,this.cstPostRule=i.NOOP},t.prototype.setInitialNodeLocationOnlyOffsetRecovery=function(t){t.location={startOffset:NaN,endOffset:NaN}},t.prototype.setInitialNodeLocationOnlyOffsetRegular=function(t){t.location={startOffset:this.LA(1).startOffset,endOffset:NaN}},t.prototype.setInitialNodeLocationFullRecovery=function(t){t.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}},t.prototype.setInitialNodeLocationFullRegular=function(t){var e=this.LA(1);t.location={startOffset:e.startOffset,startLine:e.startLine,startColumn:e.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}},t.prototype.cstInvocationStateUpdate=function(t,e){var n={name:t,children:{}};this.setInitialNodeLocation(n),this.CST_STACK.push(n)},t.prototype.cstFinallyStateUpdate=function(){this.CST_STACK.pop()},t.prototype.cstPostRuleFull=function(t){var e=this.LA(0),n=t.location;n.startOffset<=e.startOffset==1?(n.endOffset=e.endOffset,n.endLine=e.endLine,n.endColumn=e.endColumn):(n.startOffset=NaN,n.startLine=NaN,n.startColumn=NaN)},t.prototype.cstPostRuleOnlyOffset=function(t){var e=this.LA(0),n=t.location;n.startOffset<=e.startOffset==1?n.endOffset=e.endOffset:n.startOffset=NaN},t.prototype.cstPostTerminal=function(t,e){var n=this.CST_STACK[this.CST_STACK.length-1];r.addTerminalToCst(n,e,t),this.setNodeLocationFromToken(n.location,e)},t.prototype.cstPostNonTerminal=function(t,e){var n=this.CST_STACK[this.CST_STACK.length-1];r.addNoneTerminalToCst(n,e,t),this.setNodeLocationFromNode(n.location,t.location)},t.prototype.getBaseCstVisitorConstructor=function(){if(i.isUndefined(this.baseCstVisitorConstructor)){var t=o.createBaseSemanticVisitorConstructor(this.className,i.keys(this.gastProductionsCache));return this.baseCstVisitorConstructor=t,t}return this.baseCstVisitorConstructor},t.prototype.getBaseCstVisitorConstructorWithDefaults=function(){if(i.isUndefined(this.baseCstVisitorWithDefaultsConstructor)){var t=o.createBaseVisitorConstructorWithDefaults(this.className,i.keys(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=t,t}return this.baseCstVisitorWithDefaultsConstructor},t.prototype.getLastExplicitRuleShortName=function(){var t=this.RULE_STACK;return t[t.length-1]},t.prototype.getPreviousExplicitRuleShortName=function(){var t=this.RULE_STACK;return t[t.length-2]},t.prototype.getLastExplicitRuleOccurrenceIndex=function(){var t=this.RULE_OCCURRENCE_STACK;return t[t.length-1]},t}();e.TreeBuilder=s},803:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.applyMixins=void 0,e.applyMixins=function(t,e){e.forEach((function(e){var n=e.prototype;Object.getOwnPropertyNames(n).forEach((function(r){if("constructor"!==r){var i=Object.getOwnPropertyDescriptor(n,r);i&&(i.get||i.set)?Object.defineProperty(t.prototype,r,i):t.prototype[r]=e.prototype[r]}}))}))}},178:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0}),e.charCodeToOptimizedIndex=e.minOptimizationVal=e.buildLineBreakIssueMessage=e.LineTerminatorOptimizedTester=e.isShortPattern=e.isCustomPattern=e.cloneEmptyGroups=e.performWarningRuntimeChecks=e.performRuntimeChecks=e.addStickyFlag=e.addStartOfInput=e.findUnreachablePatterns=e.findModesThatDoNotExist=e.findInvalidGroupType=e.findDuplicatePatterns=e.findUnsupportedFlags=e.findStartOfInputAnchor=e.findEmptyMatchRegExps=e.findEndOfInputAnchor=e.findInvalidPatterns=e.findMissingPatterns=e.validatePatterns=e.analyzeTokenTypes=e.enableSticky=e.disableSticky=e.SUPPORT_STICKY=e.MODES=e.DEFAULT_MODE=void 0;var o=n(844),a=n(27),s=n(146),c=n(480),u=n(67);function l(t){var e=s.filter(t,(function(t){return!s.has(t,"PATTERN")}));return{errors:s.map(e,(function(t){return{message:"Token Type: ->"+t.name+"<- missing static 'PATTERN' property",type:a.LexerDefinitionErrorType.MISSING_PATTERN,tokenTypes:[t]}})),valid:s.difference(t,e)}}function p(t){var e=s.filter(t,(function(t){var e=t.PATTERN;return!(s.isRegExp(e)||s.isFunction(e)||s.has(e,"exec")||s.isString(e))}));return{errors:s.map(e,(function(t){return{message:"Token Type: ->"+t.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:a.LexerDefinitionErrorType.INVALID_PATTERN,tokenTypes:[t]}})),valid:s.difference(t,e)}}e.DEFAULT_MODE="defaultMode",e.MODES="modes",e.SUPPORT_STICKY="boolean"==typeof new RegExp("(?:)").sticky,e.disableSticky=function(){e.SUPPORT_STICKY=!1},e.enableSticky=function(){e.SUPPORT_STICKY=!0},e.analyzeTokenTypes=function(t,n){var r,i=(n=s.defaults(n,{useSticky:e.SUPPORT_STICKY,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r","\n"],tracer:function(t,e){return e()}})).tracer;i("initCharCodeToOptimizedIndexMap",(function(){!function(){if(s.isEmpty(L)){L=new Array(65536);for(var t=0;t<65536;t++)L[t]=t>255?255+~~(t/255):t}}()})),i("Reject Lexer.NA",(function(){r=s.reject(t,(function(t){return t.PATTERN===a.Lexer.NA}))}));var o,u,l,p,f,h,d,E,m,y,T,v=!1;i("Transform Patterns",(function(){v=!1,o=s.map(r,(function(t){var e=t.PATTERN;if(s.isRegExp(e)){var r=e.source;return 1!==r.length||"^"===r||"$"===r||"."===r||e.ignoreCase?2!==r.length||"\\"!==r[0]||s.contains(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],r[1])?n.useSticky?A(e):g(e):r[1]:r}if(s.isFunction(e))return v=!0,{exec:e};if(s.has(e,"exec"))return v=!0,e;if("string"==typeof e){if(1===e.length)return e;var i=e.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),o=new RegExp(i);return n.useSticky?A(o):g(o)}throw Error("non exhaustive match")}))})),i("misc mapping",(function(){u=s.map(r,(function(t){return t.tokenTypeIdx})),l=s.map(r,(function(t){var e=t.GROUP;if(e!==a.Lexer.SKIPPED){if(s.isString(e))return e;if(s.isUndefined(e))return!1;throw Error("non exhaustive match")}})),p=s.map(r,(function(t){var e=t.LONGER_ALT;if(e)return s.indexOf(r,e)})),f=s.map(r,(function(t){return t.PUSH_MODE})),h=s.map(r,(function(t){return s.has(t,"POP_MODE")}))})),i("Line Terminator Handling",(function(){var t=S(n.lineTerminatorCharacters);d=s.map(r,(function(t){return!1})),"onlyOffset"!==n.positionTracking&&(d=s.map(r,(function(e){return s.has(e,"LINE_BREAKS")?e.LINE_BREAKS:!1===I(e,t)?c.canMatchCharCode(t,e.PATTERN):void 0})))})),i("Misc Mapping #2",(function(){E=s.map(r,_),m=s.map(o,N),y=s.reduce(r,(function(t,e){var n=e.GROUP;return s.isString(n)&&n!==a.Lexer.SKIPPED&&(t[n]=[]),t}),{}),T=s.map(o,(function(t,e){return{pattern:o[e],longerAlt:p[e],canLineTerminator:d[e],isCustom:E[e],short:m[e],group:l[e],push:f[e],pop:h[e],tokenTypeIdx:u[e],tokenType:r[e]}}))}));var R=!0,O=[];return n.safeMode||i("First Char Optimization",(function(){O=s.reduce(r,(function(t,e,r){if("string"==typeof e.PATTERN){var i=C(e.PATTERN.charCodeAt(0));k(t,i,T[r])}else if(s.isArray(e.START_CHARS_HINT)){var o;s.forEach(e.START_CHARS_HINT,(function(e){var n=C("string"==typeof e?e.charCodeAt(0):e);o!==n&&(o=n,k(t,n,T[r]))}))}else if(s.isRegExp(e.PATTERN))if(e.PATTERN.unicode)R=!1,n.ensureOptimizations&&s.PRINT_ERROR(c.failedOptimizationPrefixMsg+"\tUnable to analyze < "+e.PATTERN.toString()+" > pattern.\n\tThe regexp unicode flag is not currently supported by the regexp-to-ast library.\n\tThis will disable the lexer's first char optimizations.\n\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE");else{var a=c.getOptimizedStartCodesIndices(e.PATTERN,n.ensureOptimizations);s.isEmpty(a)&&(R=!1),s.forEach(a,(function(e){k(t,e,T[r])}))}else n.ensureOptimizations&&s.PRINT_ERROR(c.failedOptimizationPrefixMsg+"\tTokenType: <"+e.name+"> is using a custom token pattern without providing <start_chars_hint> parameter.\n\tThis will disable the lexer's first char optimizations.\n\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE"),R=!1;return t}),[])})),i("ArrayPacking",(function(){O=s.packArray(O)})),{emptyGroups:y,patternIdxToConfig:T,charCodeToPatternIdxToConfig:O,hasCustom:v,canBeOptimized:R}},e.validatePatterns=function(t,e){var n=[],r=l(t);n=n.concat(r.errors);var i=p(r.valid),o=i.valid;return(n=(n=(n=(n=n.concat(i.errors)).concat(function(t){var e=[],n=s.filter(t,(function(t){return s.isRegExp(t.PATTERN)}));return(e=(e=(e=(e=e.concat(h(n))).concat(m(n))).concat(y(n))).concat(T(n))).concat(d(n))}(o))).concat(v(o))).concat(R(o,e))).concat(O(o))},e.findMissingPatterns=l,e.findInvalidPatterns=p;var f=/[^\\][\$]/;function h(t){var e=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.found=!1,e}return i(e,t),e.prototype.visitEndAnchor=function(t){this.found=!0},e}(o.BaseRegExpVisitor),n=s.filter(t,(function(t){var n=t.PATTERN;try{var r=u.getRegExpAst(n),i=new e;return i.visit(r),i.found}catch(t){return f.test(n.source)}}));return s.map(n,(function(t){return{message:"Unexpected RegExp Anchor Error:\n\tToken Type: ->"+t.name+"<- static 'PATTERN' cannot contain end of input anchor '$'\n\tSee chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS\tfor details.",type:a.LexerDefinitionErrorType.EOI_ANCHOR_FOUND,tokenTypes:[t]}}))}function d(t){var e=s.filter(t,(function(t){return t.PATTERN.test("")}));return s.map(e,(function(t){return{message:"Token Type: ->"+t.name+"<- static 'PATTERN' must not match an empty string",type:a.LexerDefinitionErrorType.EMPTY_MATCH_PATTERN,tokenTypes:[t]}}))}e.findEndOfInputAnchor=h,e.findEmptyMatchRegExps=d;var E=/[^\\[][\^]|^\^/;function m(t){var e=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.found=!1,e}return i(e,t),e.prototype.visitStartAnchor=function(t){this.found=!0},e}(o.BaseRegExpVisitor),n=s.filter(t,(function(t){var n=t.PATTERN;try{var r=u.getRegExpAst(n),i=new e;return i.visit(r),i.found}catch(t){return E.test(n.source)}}));return s.map(n,(function(t){return{message:"Unexpected RegExp Anchor Error:\n\tToken Type: ->"+t.name+"<- static 'PATTERN' cannot contain start of input anchor '^'\n\tSee https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS\tfor details.",type:a.LexerDefinitionErrorType.SOI_ANCHOR_FOUND,tokenTypes:[t]}}))}function y(t){var e=s.filter(t,(function(t){var e=t.PATTERN;return e instanceof RegExp&&(e.multiline||e.global)}));return s.map(e,(function(t){return{message:"Token Type: ->"+t.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:a.LexerDefinitionErrorType.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[t]}}))}function T(t){var e=[],n=s.map(t,(function(n){return s.reduce(t,(function(t,r){return n.PATTERN.source!==r.PATTERN.source||s.contains(e,r)||r.PATTERN===a.Lexer.NA||(e.push(r),t.push(r)),t}),[])}));n=s.compact(n);var r=s.filter(n,(function(t){return t.length>1}));return s.map(r,(function(t){var e=s.map(t,(function(t){return t.name}));return{message:"The same RegExp pattern ->"+s.first(t).PATTERN+"<-has been used in all of the following Token Types: "+e.join(", ")+" <-",type:a.LexerDefinitionErrorType.DUPLICATE_PATTERNS_FOUND,tokenTypes:t}}))}function v(t){var e=s.filter(t,(function(t){if(!s.has(t,"GROUP"))return!1;var e=t.GROUP;return e!==a.Lexer.SKIPPED&&e!==a.Lexer.NA&&!s.isString(e)}));return s.map(e,(function(t){return{message:"Token Type: ->"+t.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:a.LexerDefinitionErrorType.INVALID_GROUP_TYPE_FOUND,tokenTypes:[t]}}))}function R(t,e){var n=s.filter(t,(function(t){return void 0!==t.PUSH_MODE&&!s.contains(e,t.PUSH_MODE)}));return s.map(n,(function(t){return{message:"Token Type: ->"+t.name+"<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->"+t.PUSH_MODE+"<-which does not exist",type:a.LexerDefinitionErrorType.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[t]}}))}function O(t){var e=[],n=s.reduce(t,(function(t,e,n){var r,i,o=e.PATTERN;return o===a.Lexer.NA||(s.isString(o)?t.push({str:o,idx:n,tokenType:e}):s.isRegExp(o)&&(r=o,i=[".","\\","[","]","|","^","$","(",")","?","*","+","{"],void 0===s.find(i,(function(t){return-1!==r.source.indexOf(t)})))&&t.push({str:o.source,idx:n,tokenType:e})),t}),[]);return s.forEach(t,(function(t,r){s.forEach(n,(function(n){var i=n.str,o=n.idx,c=n.tokenType;if(r<o&&function(t,e){if(s.isRegExp(e)){var n=e.exec(t);return null!==n&&0===n.index}if(s.isFunction(e))return e(t,0,[],{});if(s.has(e,"exec"))return e.exec(t,0,[],{});if("string"==typeof e)return e===t;throw Error("non exhaustive match")}(i,t.PATTERN)){var u="Token: ->"+c.name+"<- can never be matched.\nBecause it appears AFTER the Token Type ->"+t.name+"<-in the lexer's definition.\nSee https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE";e.push({message:u,type:a.LexerDefinitionErrorType.UNREACHABLE_PATTERN,tokenTypes:[t,c]})}}))})),e}function g(t){var e=t.ignoreCase?"i":"";return new RegExp("^(?:"+t.source+")",e)}function A(t){var e=t.ignoreCase?"iy":"y";return new RegExp(""+t.source,e)}function _(t){var e=t.PATTERN;if(s.isRegExp(e))return!1;if(s.isFunction(e))return!0;if(s.has(e,"exec"))return!0;if(s.isString(e))return!1;throw Error("non exhaustive match")}function N(t){return!(!s.isString(t)||1!==t.length)&&t.charCodeAt(0)}function I(t,e){if(s.has(t,"LINE_BREAKS"))return!1;if(s.isRegExp(t.PATTERN)){try{c.canMatchCharCode(e,t.PATTERN)}catch(t){return{issue:a.LexerDefinitionErrorType.IDENTIFY_TERMINATOR,errMsg:t.message}}return!1}if(s.isString(t.PATTERN))return!1;if(_(t))return{issue:a.LexerDefinitionErrorType.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}function P(t,e){if(e.issue===a.LexerDefinitionErrorType.IDENTIFY_TERMINATOR)return"Warning: unable to identify line terminator usage in pattern.\n\tThe problem is in the <"+t.name+"> Token Type\n\t Root cause: "+e.errMsg+".\n\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR";if(e.issue===a.LexerDefinitionErrorType.CUSTOM_LINE_BREAK)return"Warning: A Custom Token Pattern should specify the <line_breaks> option.\n\tThe problem is in the <"+t.name+"> Token Type\n\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK";throw Error("non exhaustive match")}function S(t){return s.map(t,(function(t){return s.isString(t)&&t.length>0?t.charCodeAt(0):t}))}function k(t,e,n){void 0===t[e]?t[e]=[n]:t[e].push(n)}e.findStartOfInputAnchor=m,e.findUnsupportedFlags=y,e.findDuplicatePatterns=T,e.findInvalidGroupType=v,e.findModesThatDoNotExist=R,e.findUnreachablePatterns=O,e.addStartOfInput=g,e.addStickyFlag=A,e.performRuntimeChecks=function(t,n,r){var i=[];return s.has(t,e.DEFAULT_MODE)||i.push({message:"A MultiMode Lexer cannot be initialized without a <"+e.DEFAULT_MODE+"> property in its definition\n",type:a.LexerDefinitionErrorType.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),s.has(t,e.MODES)||i.push({message:"A MultiMode Lexer cannot be initialized without a <"+e.MODES+"> property in its definition\n",type:a.LexerDefinitionErrorType.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),s.has(t,e.MODES)&&s.has(t,e.DEFAULT_MODE)&&!s.has(t.modes,t.defaultMode)&&i.push({message:"A MultiMode Lexer cannot be initialized with a "+e.DEFAULT_MODE+": <"+t.defaultMode+">which does not exist\n",type:a.LexerDefinitionErrorType.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),s.has(t,e.MODES)&&s.forEach(t.modes,(function(t,e){s.forEach(t,(function(t,n){s.isUndefined(t)&&i.push({message:"A Lexer cannot be initialized using an undefined Token Type. Mode:<"+e+"> at index: <"+n+">\n",type:a.LexerDefinitionErrorType.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED})}))})),i},e.performWarningRuntimeChecks=function(t,e,n){var r=[],i=!1,o=s.compact(s.flatten(s.mapValues(t.modes,(function(t){return t})))),u=s.reject(o,(function(t){return t.PATTERN===a.Lexer.NA})),l=S(n);return e&&s.forEach(u,(function(t){var e=I(t,l);if(!1!==e){var n={message:P(t,e),type:e.issue,tokenType:t};r.push(n)}else s.has(t,"LINE_BREAKS")?!0===t.LINE_BREAKS&&(i=!0):c.canMatchCharCode(l,t.PATTERN)&&(i=!0)})),e&&!i&&r.push({message:"Warning: No LINE_BREAKS Found.\n\tThis Lexer has been defined to track line and column information,\n\tBut none of the Token Types can be identified as matching a line terminator.\n\tSee https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS \n\tfor details.",type:a.LexerDefinitionErrorType.NO_LINE_BREAKS_FLAGS}),r},e.cloneEmptyGroups=function(t){var e={},n=s.keys(t);return s.forEach(n,(function(n){var r=t[n];if(!s.isArray(r))throw Error("non exhaustive match");e[n]=[]})),e},e.isCustomPattern=_,e.isShortPattern=N,e.LineTerminatorOptimizedTester={test:function(t){for(var e=t.length,n=this.lastIndex;n<e;n++){var r=t.charCodeAt(n);if(10===r)return this.lastIndex=n+1,!0;if(13===r)return 10===t.charCodeAt(n+1)?this.lastIndex=n+2:this.lastIndex=n+1,!0}return!1},lastIndex:0},e.buildLineBreakIssueMessage=P,e.minOptimizationVal=256;var L=[];function C(t){return t<e.minOptimizationVal?t:L[t]}e.charCodeToOptimizedIndex=C},495:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.defaultLexerErrorProvider=void 0,e.defaultLexerErrorProvider={buildUnableToPopLexerModeMessage:function(t){return"Unable to pop Lexer Mode after encountering Token ->"+t.image+"<- The Mode Stack is empty"},buildUnexpectedCharactersMessage:function(t,e,n,r,i){return"unexpected character: ->"+t.charAt(e)+"<- at offset: "+e+", skipped "+n+" characters."}}},27:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Lexer=e.LexerDefinitionErrorType=void 0;var r,i=n(178),o=n(146),a=n(201),s=n(495),c=n(67);(r=e.LexerDefinitionErrorType||(e.LexerDefinitionErrorType={}))[r.MISSING_PATTERN=0]="MISSING_PATTERN",r[r.INVALID_PATTERN=1]="INVALID_PATTERN",r[r.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",r[r.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",r[r.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",r[r.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",r[r.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",r[r.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",r[r.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",r[r.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",r[r.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",r[r.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",r[r.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",r[r.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",r[r.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",r[r.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",r[r.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK";var u={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:["\n","\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:s.defaultLexerErrorProvider,traceInitPerf:!1,skipValidations:!1};Object.freeze(u);var l=function(){function t(t,e){var n=this;if(void 0===e&&(e=u),this.lexerDefinition=t,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.config=void 0,this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},"boolean"==typeof e)throw Error("The second argument to the Lexer constructor is now an ILexerConfig Object.\na boolean 2nd argument is no longer supported");this.config=o.merge(u,e);var r=this.config.traceInitPerf;!0===r?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):"number"==typeof r&&(this.traceInitMaxIdent=r,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",(function(){var r,s=!0;n.TRACE_INIT("Lexer Config handling",(function(){if(n.config.lineTerminatorsPattern===u.lineTerminatorsPattern)n.config.lineTerminatorsPattern=i.LineTerminatorOptimizedTester;else if(n.config.lineTerminatorCharacters===u.lineTerminatorCharacters)throw Error("Error: Missing <lineTerminatorCharacters> property on the Lexer config.\n\tFor details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS");if(e.safeMode&&e.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');n.trackStartLines=/full|onlyStart/i.test(n.config.positionTracking),n.trackEndLines=/full/i.test(n.config.positionTracking),o.isArray(t)?((r={modes:{}}).modes[i.DEFAULT_MODE]=o.cloneArr(t),r[i.DEFAULT_MODE]=i.DEFAULT_MODE):(s=!1,r=o.cloneObj(t))})),!1===n.config.skipValidations&&(n.TRACE_INIT("performRuntimeChecks",(function(){n.lexerDefinitionErrors=n.lexerDefinitionErrors.concat(i.performRuntimeChecks(r,n.trackStartLines,n.config.lineTerminatorCharacters))})),n.TRACE_INIT("performWarningRuntimeChecks",(function(){n.lexerDefinitionWarning=n.lexerDefinitionWarning.concat(i.performWarningRuntimeChecks(r,n.trackStartLines,n.config.lineTerminatorCharacters))}))),r.modes=r.modes?r.modes:{},o.forEach(r.modes,(function(t,e){r.modes[e]=o.reject(t,(function(t){return o.isUndefined(t)}))}));var l=o.keys(r.modes);if(o.forEach(r.modes,(function(t,r){n.TRACE_INIT("Mode: <"+r+"> processing",(function(){var s;n.modes.push(r),!1===n.config.skipValidations&&n.TRACE_INIT("validatePatterns",(function(){n.lexerDefinitionErrors=n.lexerDefinitionErrors.concat(i.validatePatterns(t,l))})),o.isEmpty(n.lexerDefinitionErrors)&&(a.augmentTokenTypes(t),n.TRACE_INIT("analyzeTokenTypes",(function(){s=i.analyzeTokenTypes(t,{lineTerminatorCharacters:n.config.lineTerminatorCharacters,positionTracking:e.positionTracking,ensureOptimizations:e.ensureOptimizations,safeMode:e.safeMode,tracer:n.TRACE_INIT.bind(n)})})),n.patternIdxToConfig[r]=s.patternIdxToConfig,n.charCodeToPatternIdxToConfig[r]=s.charCodeToPatternIdxToConfig,n.emptyGroups=o.merge(n.emptyGroups,s.emptyGroups),n.hasCustom=s.hasCustom||n.hasCustom,n.canModeBeOptimized[r]=s.canBeOptimized)}))})),n.defaultMode=r.defaultMode,!o.isEmpty(n.lexerDefinitionErrors)&&!n.config.deferDefinitionErrorsHandling){var p=o.map(n.lexerDefinitionErrors,(function(t){return t.message})).join("-----------------------\n");throw new Error("Errors detected in definition of Lexer:\n"+p)}o.forEach(n.lexerDefinitionWarning,(function(t){o.PRINT_WARNING(t.message)})),n.TRACE_INIT("Choosing sub-methods implementations",(function(){if(i.SUPPORT_STICKY?(n.chopInput=o.IDENTITY,n.match=n.matchWithTest):(n.updateLastIndex=o.NOOP,n.match=n.matchWithExec),s&&(n.handleModes=o.NOOP),!1===n.trackStartLines&&(n.computeNewColumn=o.IDENTITY),!1===n.trackEndLines&&(n.updateTokenEndLineColumnLocation=o.NOOP),/full/i.test(n.config.positionTracking))n.createTokenInstance=n.createFullToken;else if(/onlyStart/i.test(n.config.positionTracking))n.createTokenInstance=n.createStartOnlyToken;else{if(!/onlyOffset/i.test(n.config.positionTracking))throw Error('Invalid <positionTracking> config option: "'+n.config.positionTracking+'"');n.createTokenInstance=n.createOffsetOnlyToken}n.hasCustom?(n.addToken=n.addTokenUsingPush,n.handlePayload=n.handlePayloadWithCustom):(n.addToken=n.addTokenUsingMemberAccess,n.handlePayload=n.handlePayloadNoCustom)})),n.TRACE_INIT("Failed Optimization Warnings",(function(){var t=o.reduce(n.canModeBeOptimized,(function(t,e,n){return!1===e&&t.push(n),t}),[]);if(e.ensureOptimizations&&!o.isEmpty(t))throw Error("Lexer Modes: < "+t.join(", ")+' > cannot be optimized.\n\t Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.\n\t Or inspect the console log for details on how to resolve these issues.')})),n.TRACE_INIT("clearRegExpParserCache",(function(){c.clearRegExpParserCache()})),n.TRACE_INIT("toFastProperties",(function(){o.toFastProperties(n)}))}))}return t.prototype.tokenize=function(t,e){if(void 0===e&&(e=this.defaultMode),!o.isEmpty(this.lexerDefinitionErrors)){var n=o.map(this.lexerDefinitionErrors,(function(t){return t.message})).join("-----------------------\n");throw new Error("Unable to Tokenize because Errors detected in definition of Lexer:\n"+n)}return this.tokenizeInternal(t,e)},t.prototype.tokenizeInternal=function(t,e){var n,r,a,s,c,u,l,p,f,h,d,E,m,y,T=this,v=t,R=v.length,O=0,g=0,A=this.hasCustom?0:Math.floor(t.length/10),_=new Array(A),N=[],I=this.trackStartLines?1:void 0,P=this.trackStartLines?1:void 0,S=i.cloneEmptyGroups(this.emptyGroups),k=this.trackStartLines,L=this.config.lineTerminatorsPattern,C=0,x=[],M=[],b=[],F=[];Object.freeze(F);var D=void 0;function w(){return x}function U(t){var e=i.charCodeToOptimizedIndex(t),n=M[e];return void 0===n?F:n}var j,G=function(t){if(1===b.length&&void 0===t.tokenType.PUSH_MODE){var e=T.config.errorMessageProvider.buildUnableToPopLexerModeMessage(t);N.push({offset:t.startOffset,line:void 0!==t.startLine?t.startLine:void 0,column:void 0!==t.startColumn?t.startColumn:void 0,length:t.image.length,message:e})}else{b.pop();var n=o.last(b);x=T.patternIdxToConfig[n],M=T.charCodeToPatternIdxToConfig[n],C=x.length;var r=T.canModeBeOptimized[n]&&!1===T.config.safeMode;D=M&&r?U:w}};function B(t){b.push(t),M=this.charCodeToPatternIdxToConfig[t],x=this.patternIdxToConfig[t],C=x.length,C=x.length;var e=this.canModeBeOptimized[t]&&!1===this.config.safeMode;D=M&&e?U:w}for(B.call(this,e);O<R;){c=null;var W=v.charCodeAt(O),V=D(W),K=V.length;for(n=0;n<K;n++){var z=(j=V[n]).pattern;if(u=null,!1!==(et=j.short)?W===et&&(c=z):!0===j.isCustom?null!==(y=z.exec(v,O,_,S))?(c=y[0],void 0!==y.payload&&(u=y.payload)):c=null:(this.updateLastIndex(z,O),c=this.match(z,t,O)),null!==c){if(void 0!==(s=j.longerAlt)){var Y=x[s],H=Y.pattern;l=null,!0===Y.isCustom?null!==(y=H.exec(v,O,_,S))?(a=y[0],void 0!==y.payload&&(l=y.payload)):a=null:(this.updateLastIndex(H,O),a=this.match(H,t,O)),a&&a.length>c.length&&(c=a,u=l,j=Y)}break}}if(null!==c){if(p=c.length,void 0!==(f=j.group)&&(h=j.tokenTypeIdx,d=this.createTokenInstance(c,O,h,j.tokenType,I,P,p),this.handlePayload(d,u),!1===f?g=this.addToken(_,g,d):S[f].push(d)),t=this.chopInput(t,p),O+=p,P=this.computeNewColumn(P,p),!0===k&&!0===j.canLineTerminator){var X=0,q=void 0,$=void 0;L.lastIndex=0;do{!0===(q=L.test(c))&&($=L.lastIndex-1,X++)}while(!0===q);0!==X&&(I+=X,P=p-$,this.updateTokenEndLineColumnLocation(d,f,$,X,I,P,p))}this.handleModes(j,G,B,d)}else{for(var Q=O,Z=I,J=P,tt=!1;!tt&&O<R;)for(v.charCodeAt(O),t=this.chopInput(t,1),O++,r=0;r<C;r++){var et,nt=x[r];if(z=nt.pattern,!1!==(et=nt.short)?v.charCodeAt(O)===et&&(tt=!0):!0===nt.isCustom?tt=null!==z.exec(v,O,_,S):(this.updateLastIndex(z,O),tt=null!==z.exec(t)),!0===tt)break}E=O-Q,m=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(v,Q,E,Z,J),N.push({offset:Q,line:Z,column:J,length:E,message:m})}}return this.hasCustom||(_.length=g),{tokens:_,groups:S,errors:N}},t.prototype.handleModes=function(t,e,n,r){if(!0===t.pop){var i=t.push;e(r),void 0!==i&&n.call(this,i)}else void 0!==t.push&&n.call(this,t.push)},t.prototype.chopInput=function(t,e){return t.substring(e)},t.prototype.updateLastIndex=function(t,e){t.lastIndex=e},t.prototype.updateTokenEndLineColumnLocation=function(t,e,n,r,i,o,a){var s,c;void 0!==e&&(c=(s=n===a-1)?-1:0,1===r&&!0===s||(t.endLine=i+c,t.endColumn=o-1-c))},t.prototype.computeNewColumn=function(t,e){return t+e},t.prototype.createTokenInstance=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return null},t.prototype.createOffsetOnlyToken=function(t,e,n,r){return{image:t,startOffset:e,tokenTypeIdx:n,tokenType:r}},t.prototype.createStartOnlyToken=function(t,e,n,r,i,o){return{image:t,startOffset:e,startLine:i,startColumn:o,tokenTypeIdx:n,tokenType:r}},t.prototype.createFullToken=function(t,e,n,r,i,o,a){return{image:t,startOffset:e,endOffset:e+a-1,startLine:i,endLine:i,startColumn:o,endColumn:o+a-1,tokenTypeIdx:n,tokenType:r}},t.prototype.addToken=function(t,e,n){return 666},t.prototype.addTokenUsingPush=function(t,e,n){return t.push(n),e},t.prototype.addTokenUsingMemberAccess=function(t,e,n){return t[e]=n,++e},t.prototype.handlePayload=function(t,e){},t.prototype.handlePayloadNoCustom=function(t,e){},t.prototype.handlePayloadWithCustom=function(t,e){null!==e&&(t.payload=e)},t.prototype.match=function(t,e,n){return null},t.prototype.matchWithTest=function(t,e,n){return!0===t.test(e)?e.substring(n,t.lastIndex):null},t.prototype.matchWithExec=function(t,e){var n=t.exec(e);return null!==n?n[0]:n},t.prototype.TRACE_INIT=function(t,e){if(!0===this.traceInitPerf){this.traceInitIndent++;var n=new Array(this.traceInitIndent+1).join("\t");this.traceInitIndent<this.traceInitMaxIdent&&console.log(n+"--\x3e <"+t+">");var r=o.timer(e),i=r.time,a=r.value,s=i>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&s(n+"<-- <"+t+"> time: "+i+"ms"),this.traceInitIndent--,a}return e()},t.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.",t.NA=/NOT_APPLICABLE/,t}();e.Lexer=l},480:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0}),e.canMatchCharCode=e.firstCharOptimizedIndices=e.getOptimizedStartCodesIndices=e.failedOptimizationPrefixMsg=void 0;var o=n(844),a=n(146),s=n(67),c=n(178),u="Complement Sets are not supported for first char optimization";function l(t,e,n){switch(t.type){case"Disjunction":for(var r=0;r<t.value.length;r++)l(t.value[r],e,n);break;case"Alternative":var i=t.value;for(r=0;r<i.length;r++){var o=i[r];switch(o.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}var s=o;switch(s.type){case"Character":p(s.value,e,n);break;case"Set":if(!0===s.complement)throw Error(u);a.forEach(s.value,(function(t){if("number"==typeof t)p(t,e,n);else{var r=t;if(!0===n)for(var i=r.from;i<=r.to;i++)p(i,e,n);else{for(i=r.from;i<=r.to&&i<c.minOptimizationVal;i++)p(i,e,n);if(r.to>=c.minOptimizationVal)for(var o=r.from>=c.minOptimizationVal?r.from:c.minOptimizationVal,a=r.to,s=c.charCodeToOptimizedIndex(o),u=c.charCodeToOptimizedIndex(a),l=s;l<=u;l++)e[l]=l}}}));break;case"Group":l(s.value,e,n);break;default:throw Error("Non Exhaustive Match")}var f=void 0!==s.quantifier&&0===s.quantifier.atLeast;if("Group"===s.type&&!1===h(s)||"Group"!==s.type&&!1===f)break}break;default:throw Error("non exhaustive match!")}return a.values(e)}function p(t,e,n){var r=c.charCodeToOptimizedIndex(t);e[r]=r,!0===n&&function(t,e){var n=String.fromCharCode(t),r=n.toUpperCase();if(r!==n)e[i=c.charCodeToOptimizedIndex(r.charCodeAt(0))]=i;else{var i,o=n.toLowerCase();o!==n&&(e[i=c.charCodeToOptimizedIndex(o.charCodeAt(0))]=i)}}(t,e)}function f(t,e){return a.find(t.value,(function(t){if("number"==typeof t)return a.contains(e,t);var n=t;return void 0!==a.find(e,(function(t){return n.from<=t&&t<=n.to}))}))}function h(t){return!(!t.quantifier||0!==t.quantifier.atLeast)||!!t.value&&(a.isArray(t.value)?a.every(t.value,h):h(t.value))}e.failedOptimizationPrefixMsg='Unable to use "first char" lexer optimizations:\n',e.getOptimizedStartCodesIndices=function(t,n){void 0===n&&(n=!1);try{var r=s.getRegExpAst(t);return l(r.value,{},r.flags.ignoreCase)}catch(r){if(r.message===u)n&&a.PRINT_WARNING(e.failedOptimizationPrefixMsg+"\tUnable to optimize: < "+t.toString()+" >\n\tComplement Sets cannot be automatically optimized.\n\tThis will disable the lexer's first char optimizations.\n\tSee: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.");else{var i="";n&&(i="\n\tThis will disable the lexer's first char optimizations.\n\tSee: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details."),a.PRINT_ERROR(e.failedOptimizationPrefixMsg+"\n\tFailed parsing: < "+t.toString()+" >\n\tUsing the regexp-to-ast library version: "+o.VERSION+"\n\tPlease open an issue at: https://github.com/bd82/regexp-to-ast/issues"+i)}}return[]},e.firstCharOptimizedIndices=l;var d=function(t){function e(e){var n=t.call(this)||this;return n.targetCharCodes=e,n.found=!1,n}return i(e,t),e.prototype.visitChildren=function(e){if(!0!==this.found){switch(e.type){case"Lookahead":return void this.visitLookahead(e);case"NegativeLookahead":return void this.visitNegativeLookahead(e)}t.prototype.visitChildren.call(this,e)}},e.prototype.visitCharacter=function(t){a.contains(this.targetCharCodes,t.value)&&(this.found=!0)},e.prototype.visitSet=function(t){t.complement?void 0===f(t,this.targetCharCodes)&&(this.found=!0):void 0!==f(t,this.targetCharCodes)&&(this.found=!0)},e}(o.BaseRegExpVisitor);e.canMatchCharCode=function(t,e){if(e instanceof RegExp){var n=s.getRegExpAst(e),r=new d(t);return r.visit(n),r.found}return void 0!==a.find(e,(function(e){return a.contains(t,e.charCodeAt(0))}))}},67:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.clearRegExpParserCache=e.getRegExpAst=void 0;var r=n(844),i={},o=new r.RegExpParser;e.getRegExpAst=function(t){var e=t.toString();if(i.hasOwnProperty(e))return i[e];var n=o.pattern(e);return i[e]=n,n},e.clearRegExpParserCache=function(){i={}}},201:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isTokenType=e.hasExtendingTokensTypesMapProperty=e.hasExtendingTokensTypesProperty=e.hasCategoriesProperty=e.hasShortKeyProperty=e.singleAssignCategoriesToksMap=e.assignCategoriesMapProp=e.assignCategoriesTokensProp=e.assignTokenDefaultProps=e.expandCategories=e.augmentTokenTypes=e.tokenIdxToClass=e.tokenShortNameIdx=e.tokenStructuredMatcherNoCategories=e.tokenStructuredMatcher=void 0;var r=n(146);function i(t){for(var e=r.cloneArr(t),n=t,i=!0;i;){n=r.compact(r.flatten(r.map(n,(function(t){return t.CATEGORIES}))));var o=r.difference(n,e);e=e.concat(o),r.isEmpty(o)?i=!1:n=o}return e}function o(t){r.forEach(t,(function(t){u(t)||(e.tokenIdxToClass[e.tokenShortNameIdx]=t,t.tokenTypeIdx=e.tokenShortNameIdx++),l(t)&&!r.isArray(t.CATEGORIES)&&(t.CATEGORIES=[t.CATEGORIES]),l(t)||(t.CATEGORIES=[]),p(t)||(t.categoryMatches=[]),f(t)||(t.categoryMatchesMap={})}))}function a(t){r.forEach(t,(function(t){t.categoryMatches=[],r.forEach(t.categoryMatchesMap,(function(n,r){t.categoryMatches.push(e.tokenIdxToClass[r].tokenTypeIdx)}))}))}function s(t){r.forEach(t,(function(t){c([],t)}))}function c(t,e){r.forEach(t,(function(t){e.categoryMatchesMap[t.tokenTypeIdx]=!0})),r.forEach(e.CATEGORIES,(function(n){var i=t.concat(e);r.contains(i,n)||c(i,n)}))}function u(t){return r.has(t,"tokenTypeIdx")}function l(t){return r.has(t,"CATEGORIES")}function p(t){return r.has(t,"categoryMatches")}function f(t){return r.has(t,"categoryMatchesMap")}e.tokenStructuredMatcher=function(t,e){var n=t.tokenTypeIdx;return n===e.tokenTypeIdx||!0===e.isParent&&!0===e.categoryMatchesMap[n]},e.tokenStructuredMatcherNoCategories=function(t,e){return t.tokenTypeIdx===e.tokenTypeIdx},e.tokenShortNameIdx=1,e.tokenIdxToClass={},e.augmentTokenTypes=function(t){var e=i(t);o(e),s(e),a(e),r.forEach(e,(function(t){t.isParent=t.categoryMatches.length>0}))},e.expandCategories=i,e.assignTokenDefaultProps=o,e.assignCategoriesTokensProp=a,e.assignCategoriesMapProp=s,e.singleAssignCategoriesToksMap=c,e.hasShortKeyProperty=u,e.hasCategoriesProperty=l,e.hasExtendingTokensTypesProperty=p,e.hasExtendingTokensTypesMapProperty=f,e.isTokenType=function(t){return r.has(t,"tokenTypeIdx")}},736:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.tokenMatcher=e.createTokenInstance=e.EOF=e.createToken=e.hasTokenLabel=e.tokenName=e.tokenLabel=void 0;var r=n(146),i=n(27),o=n(201);function a(t){return r.isString(t.LABEL)&&""!==t.LABEL}e.tokenLabel=function(t){return a(t)?t.LABEL:t.name},e.tokenName=function(t){return t.name},e.hasTokenLabel=a;function s(t){return function(t){var e=t.pattern,n={};if(n.name=t.name,r.isUndefined(e)||(n.PATTERN=e),r.has(t,"parent"))throw"The parent property is no longer supported.\nSee: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.";return r.has(t,"categories")&&(n.CATEGORIES=t.categories),o.augmentTokenTypes([n]),r.has(t,"label")&&(n.LABEL=t.label),r.has(t,"group")&&(n.GROUP=t.group),r.has(t,"pop_mode")&&(n.POP_MODE=t.pop_mode),r.has(t,"push_mode")&&(n.PUSH_MODE=t.push_mode),r.has(t,"longer_alt")&&(n.LONGER_ALT=t.longer_alt),r.has(t,"line_breaks")&&(n.LINE_BREAKS=t.line_breaks),r.has(t,"start_chars_hint")&&(n.START_CHARS_HINT=t.start_chars_hint),n}(t)}e.createToken=s,e.EOF=s({name:"EOF",pattern:i.Lexer.NA}),o.augmentTokenTypes([e.EOF]),e.createTokenInstance=function(t,e,n,r,i,o,a,s){return{image:e,startOffset:n,endOffset:r,startLine:i,endLine:o,startColumn:a,endColumn:s,tokenTypeIdx:t.tokenTypeIdx,tokenType:t}},e.tokenMatcher=function(t,e){return o.tokenStructuredMatcher(t,e)}},979:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.VERSION=void 0,e.VERSION="9.0.1"},146:function(t,e){"use strict";var n=this&&this.__spreadArray||function(t,e){for(var n=0,r=e.length,i=t.length;n<r;n++,i++)t[i]=e[n];return t};function r(t){return t&&0===t.length}function i(t){return null==t?[]:Object.keys(t)}function o(t){for(var e=[],n=Object.keys(t),r=0;r<n.length;r++)e.push(t[n[r]]);return e}function a(t,e){if(Array.isArray(t))for(var n=0;n<t.length;n++)e.call(null,t[n],n);else{if(!E(t))throw Error("non exhaustive match");var r=i(t);for(n=0;n<r.length;n++){var o=r[n],a=t[o];e.call(null,a,o)}}}function s(t,e){var n=[];if(Array.isArray(t))for(var r=0;r<t.length;r++){var i=t[r];e.call(null,i)&&n.push(i)}return n}function c(t,e){return s(t,(function(t){return!e(t)}))}function u(t,e){return!!E(t)&&t.hasOwnProperty(e)}function l(t,e){return void 0!==h(t,(function(t){return t===e}))}function p(t){for(var e=[],n=0;n<t.length;n++)e.push(t[n]);return e}function f(t){var e={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}function h(t,e){for(var n=0;n<t.length;n++){var r=t[n];if(e.call(null,r))return r}}function d(t,e,n){for(var r=Array.isArray(t),a=r?t:o(t),s=r?[]:i(t),c=n,u=0;u<a.length;u++)c=e.call(null,c,a[u],r?u:s[u]);return c}function E(t){return t instanceof Object}function m(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];for(var r=0;r<e.length;r++)for(var o=e[r],a=i(o),s=0;s<a.length;s++){var c=a[s];u(t,c)||(t[c]=o[c])}return t}Object.defineProperty(e,"__esModule",{value:!0}),e.toFastProperties=e.timer=e.peek=e.isES2015MapSupported=e.PRINT_WARNING=e.PRINT_ERROR=e.packArray=e.IDENTITY=e.NOOP=e.merge=e.groupBy=e.defaults=e.assignNoOverwrite=e.assign=e.zipObject=e.sortBy=e.indexOf=e.some=e.difference=e.every=e.isObject=e.isRegExp=e.isArray=e.partial=e.uniq=e.compact=e.reduce=e.findAll=e.find=e.cloneObj=e.cloneArr=e.contains=e.has=e.pick=e.reject=e.filter=e.dropRight=e.drop=e.isFunction=e.isUndefined=e.isString=e.forEach=e.last=e.first=e.flatten=e.map=e.mapValues=e.values=e.keys=e.isEmpty=void 0,e.isEmpty=r,e.keys=i,e.values=o,e.mapValues=function(t,e){for(var n=[],r=i(t),o=0;o<r.length;o++){var a=r[o];n.push(e.call(null,t[a],a))}return n},e.map=function(t,e){for(var n=[],r=0;r<t.length;r++)n.push(e.call(null,t[r],r));return n},e.flatten=function t(e){for(var n=[],r=0;r<e.length;r++){var i=e[r];Array.isArray(i)?n=n.concat(t(i)):n.push(i)}return n},e.first=function(t){return r(t)?void 0:t[0]},e.last=function(t){var e=t&&t.length;return e?t[e-1]:void 0},e.forEach=a,e.isString=function(t){return"string"==typeof t},e.isUndefined=function(t){return void 0===t},e.isFunction=function(t){return t instanceof Function},e.drop=function(t,e){return void 0===e&&(e=1),t.slice(e,t.length)},e.dropRight=function(t,e){return void 0===e&&(e=1),t.slice(0,t.length-e)},e.filter=s,e.reject=c,e.pick=function(t,e){for(var n=Object.keys(t),r={},i=0;i<n.length;i++){var o=n[i],a=t[o];e(a)&&(r[o]=a)}return r},e.has=u,e.contains=l,e.cloneArr=p,e.cloneObj=f,e.find=h,e.findAll=function(t,e){for(var n=[],r=0;r<t.length;r++){var i=t[r];e.call(null,i)&&n.push(i)}return n},e.reduce=d,e.compact=function(t){return c(t,(function(t){return null==t}))},e.uniq=function(t,e){void 0===e&&(e=function(t){return t});var n=[];return d(t,(function(t,r){var i=e(r);return l(n,i)?t:(n.push(i),t.concat(r))}),[])},e.partial=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];var r=[null],i=r.concat(e);return Function.bind.apply(t,i)},e.isArray=function(t){return Array.isArray(t)},e.isRegExp=function(t){return t instanceof RegExp},e.isObject=E,e.every=function(t,e){for(var n=0;n<t.length;n++)if(!e(t[n],n))return!1;return!0},e.difference=function(t,e){return c(t,(function(t){return l(e,t)}))},e.some=function(t,e){for(var n=0;n<t.length;n++)if(e(t[n]))return!0;return!1},e.indexOf=function(t,e){for(var n=0;n<t.length;n++)if(t[n]===e)return n;return-1},e.sortBy=function(t,e){var n=p(t);return n.sort((function(t,n){return e(t)-e(n)})),n},e.zipObject=function(t,e){if(t.length!==e.length)throw Error("can't zipObject with different number of keys and values!");for(var n={},r=0;r<t.length;r++)n[t[r]]=e[r];return n},e.assign=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];for(var r=0;r<e.length;r++)for(var o=e[r],a=i(o),s=0;s<a.length;s++){var c=a[s];t[c]=o[c]}return t},e.assignNoOverwrite=m,e.defaults=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return m.apply(void 0,n([{}],t))},e.groupBy=function(t,e){var n={};return a(t,(function(t){var r=e(t),i=n[r];i?i.push(t):n[r]=[t]})),n},e.merge=function(t,e){for(var n=f(t),r=i(e),o=0;o<r.length;o++){var a=r[o],s=e[a];n[a]=s}return n},e.NOOP=function(){},e.IDENTITY=function(t){return t},e.packArray=function(t){for(var e=[],n=0;n<t.length;n++){var r=t[n];e.push(void 0!==r?r:void 0)}return e},e.PRINT_ERROR=function(t){console&&console.error&&console.error("Error: "+t)},e.PRINT_WARNING=function(t){console&&console.warn&&console.warn("Warning: "+t)},e.isES2015MapSupported=function(){return"function"==typeof Map},e.peek=function(t){return t[t.length-1]},e.timer=function(t){var e=(new Date).getTime(),n=t();return{time:(new Date).getTime()-e,value:n}},e.toFastProperties=function(t){function e(){}e.prototype=t;var n=new e;function r(){return typeof n.bar}return r(),r(),t}}},e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={exports:{}};return t[r].call(i.exports,i,i.exports,n),i.exports}var r={};return(()=>{"use strict";var t=r;Object.defineProperty(t,"__esModule",{value:!0}),t.Parser=t.createSyntaxDiagramsCode=t.clearCache=t.GAstVisitor=t.serializeProduction=t.serializeGrammar=t.Terminal=t.Rule=t.RepetitionWithSeparator=t.RepetitionMandatoryWithSeparator=t.RepetitionMandatory=t.Repetition=t.Option=t.NonTerminal=t.Alternative=t.Alternation=t.defaultLexerErrorProvider=t.NoViableAltException=t.NotAllInputParsedException=t.MismatchedTokenException=t.isRecognitionException=t.EarlyExitException=t.defaultParserErrorProvider=t.tokenName=t.tokenMatcher=t.tokenLabel=t.EOF=t.createTokenInstance=t.createToken=t.LexerDefinitionErrorType=t.Lexer=t.EMPTY_ALT=t.ParserDefinitionErrorType=t.EmbeddedActionsParser=t.CstParser=t.VERSION=void 0;var e=n(979);Object.defineProperty(t,"VERSION",{enumerable:!0,get:function(){return e.VERSION}});var i=n(941);Object.defineProperty(t,"CstParser",{enumerable:!0,get:function(){return i.CstParser}}),Object.defineProperty(t,"EmbeddedActionsParser",{enumerable:!0,get:function(){return i.EmbeddedActionsParser}}),Object.defineProperty(t,"ParserDefinitionErrorType",{enumerable:!0,get:function(){return i.ParserDefinitionErrorType}}),Object.defineProperty(t,"EMPTY_ALT",{enumerable:!0,get:function(){return i.EMPTY_ALT}});var o=n(27);Object.defineProperty(t,"Lexer",{enumerable:!0,get:function(){return o.Lexer}}),Object.defineProperty(t,"LexerDefinitionErrorType",{enumerable:!0,get:function(){return o.LexerDefinitionErrorType}});var a=n(736);Object.defineProperty(t,"createToken",{enumerable:!0,get:function(){return a.createToken}}),Object.defineProperty(t,"createTokenInstance",{enumerable:!0,get:function(){return a.createTokenInstance}}),Object.defineProperty(t,"EOF",{enumerable:!0,get:function(){return a.EOF}}),Object.defineProperty(t,"tokenLabel",{enumerable:!0,get:function(){return a.tokenLabel}}),Object.defineProperty(t,"tokenMatcher",{enumerable:!0,get:function(){return a.tokenMatcher}}),Object.defineProperty(t,"tokenName",{enumerable:!0,get:function(){return a.tokenName}});var s=n(7);Object.defineProperty(t,"defaultParserErrorProvider",{enumerable:!0,get:function(){return s.defaultParserErrorProvider}});var c=n(643);Object.defineProperty(t,"EarlyExitException",{enumerable:!0,get:function(){return c.EarlyExitException}}),Object.defineProperty(t,"isRecognitionException",{enumerable:!0,get:function(){return c.isRecognitionException}}),Object.defineProperty(t,"MismatchedTokenException",{enumerable:!0,get:function(){return c.MismatchedTokenException}}),Object.defineProperty(t,"NotAllInputParsedException",{enumerable:!0,get:function(){return c.NotAllInputParsedException}}),Object.defineProperty(t,"NoViableAltException",{enumerable:!0,get:function(){return c.NoViableAltException}});var u=n(495);Object.defineProperty(t,"defaultLexerErrorProvider",{enumerable:!0,get:function(){return u.defaultLexerErrorProvider}});var l=n(946);Object.defineProperty(t,"Alternation",{enumerable:!0,get:function(){return l.Alternation}}),Object.defineProperty(t,"Alternative",{enumerable:!0,get:function(){return l.Alternative}}),Object.defineProperty(t,"NonTerminal",{enumerable:!0,get:function(){return l.NonTerminal}}),Object.defineProperty(t,"Option",{enumerable:!0,get:function(){return l.Option}}),Object.defineProperty(t,"Repetition",{enumerable:!0,get:function(){return l.Repetition}}),Object.defineProperty(t,"RepetitionMandatory",{enumerable:!0,get:function(){return l.RepetitionMandatory}}),Object.defineProperty(t,"RepetitionMandatoryWithSeparator",{enumerable:!0,get:function(){return l.RepetitionMandatoryWithSeparator}}),Object.defineProperty(t,"RepetitionWithSeparator",{enumerable:!0,get:function(){return l.RepetitionWithSeparator}}),Object.defineProperty(t,"Rule",{enumerable:!0,get:function(){return l.Rule}}),Object.defineProperty(t,"Terminal",{enumerable:!0,get:function(){return l.Terminal}});var p=n(946);Object.defineProperty(t,"serializeGrammar",{enumerable:!0,get:function(){return p.serializeGrammar}}),Object.defineProperty(t,"serializeProduction",{enumerable:!0,get:function(){return p.serializeProduction}});var f=n(841);Object.defineProperty(t,"GAstVisitor",{enumerable:!0,get:function(){return f.GAstVisitor}}),t.clearCache=function(){console.warn("The clearCache function was 'soft' removed from the Chevrotain API.\n\t It performs no action other than printing this message.\n\t Please avoid using it as it will be completely removed in the future")};var h=n(781);Object.defineProperty(t,"createSyntaxDiagramsCode",{enumerable:!0,get:function(){return h.createSyntaxDiagramsCode}});t.Parser=function(){throw new Error("The Parser class has been deprecated, use CstParser or EmbeddedActionsParser instead.\t\nSee: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_7-0-0")}})(),r})()}));
