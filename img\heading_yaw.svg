<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="400.667px" height="400.666px" viewBox="0 0 400.667 400.666" enable-background="new 0 0 400.667 400.666"
	 xml:space="preserve">
<circle fill="#232323" cx="200.333" cy="200" r="161"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="200.333" y1="350.099" x2="200.333" y2="326.667"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="213.416" y1="349.528" x2="212.023" y2="333.609"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="239.182" y1="344.985" x2="235.046" y2="329.55"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="263.768" y1="336.036" x2="257.015" y2="321.554"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="286.427" y1="322.955" x2="277.261" y2="309.865"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="306.47" y1="306.137" x2="295.17" y2="294.837"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="323.289" y1="286.094" x2="310.198" y2="276.929"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="336.371" y1="263.436" x2="321.887" y2="256.683"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="345.32" y1="238.85" x2="329.883" y2="234.714"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="349.864" y1="213.083" x2="333.943" y2="211.691"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="349.864" y1="186.918" x2="333.944" y2="188.313"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="345.321" y1="161.151" x2="329.885" y2="165.289"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="336.373" y1="136.565" x2="321.89" y2="143.32"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="323.291" y1="113.906" x2="310.202" y2="123.073"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="306.472" y1="93.863" x2="295.174" y2="105.163"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="286.429" y1="77.045" x2="277.265" y2="90.135"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="263.771" y1="63.962" x2="257.019" y2="78.446"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="239.185" y1="55.013" x2="235.05" y2="70.449"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="213.418" y1="50.469" x2="212.027" y2="66.389"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="187.254" y1="50.469" x2="188.648" y2="66.388"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="161.488" y1="55.012" x2="165.625" y2="70.447"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="136.901" y1="63.96" x2="143.656" y2="78.443"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="114.242" y1="77.042" x2="123.409" y2="90.131"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="94.199" y1="93.86" x2="105.499" y2="105.159"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="77.38" y1="113.903" x2="90.47" y2="123.067"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="64.298" y1="136.562" x2="78.781" y2="143.314"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="55.349" y1="161.148" x2="70.785" y2="165.283"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="50.805" y1="186.915" x2="66.724" y2="188.306"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="50.804" y1="213.079" x2="66.723" y2="211.685"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="55.347" y1="238.845" x2="70.783" y2="234.709"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="64.295" y1="263.432" x2="78.778" y2="256.678"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="77.376" y1="286.091" x2="90.466" y2="276.925"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="94.194" y1="306.135" x2="105.494" y2="294.834"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="114.237" y1="322.954" x2="123.402" y2="309.863"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="136.895" y1="336.037" x2="143.648" y2="321.553"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="161.481" y1="344.986" x2="165.617" y2="329.549"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="187.248" y1="349.53" x2="188.64" y2="333.61"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="226.398" y1="347.819" x2="222.33" y2="324.742"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="251.67" y1="341.047" x2="243.657" y2="319.028"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="275.383" y1="329.99" x2="263.667" y2="309.697"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="296.816" y1="314.983" x2="281.754" y2="297.033"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="315.317" y1="296.483" x2="297.367" y2="281.42"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="330.324" y1="275.05" x2="310.031" y2="263.333"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="341.381" y1="251.337" x2="319.363" y2="243.323"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="348.153" y1="226.065" x2="325.078" y2="221.996"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="350.433" y1="200" x2="327.001" y2="200"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="348.153" y1="173.936" x2="325.078" y2="178.004"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="341.381" y1="148.664" x2="319.363" y2="156.677"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="330.324" y1="124.951" x2="310.032" y2="136.667"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="315.317" y1="103.519" x2="297.368" y2="118.58"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="296.816" y1="85.018" x2="281.755" y2="102.967"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="275.384" y1="70.011" x2="263.668" y2="90.303"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="251.672" y1="58.954" x2="243.658" y2="80.971"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="226.4" y1="52.182" x2="222.331" y2="75.256"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="200.335" y1="49.901" x2="200.334" y2="73.332"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="174.271" y1="52.181" x2="178.339" y2="75.256"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="148.999" y1="58.953" x2="157.012" y2="80.971"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="125.286" y1="70.01" x2="137.001" y2="90.302"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="103.853" y1="85.017" x2="118.915" y2="102.966"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="85.352" y1="103.517" x2="103.302" y2="118.579"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="70.345" y1="124.949" x2="90.638" y2="136.665"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="59.288" y1="148.662" x2="81.307" y2="156.676"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="52.515" y1="173.934" x2="75.592" y2="178.002"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="50.235" y1="199.999" x2="73.667" y2="199.998"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="52.515" y1="226.063" x2="75.591" y2="221.993"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="59.287" y1="251.335" x2="81.306" y2="243.32"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="70.343" y1="275.048" x2="90.637" y2="263.331"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="85.35" y1="296.481" x2="103.301" y2="281.417"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="103.851" y1="314.982" x2="118.914" y2="297.03"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="125.284" y1="329.989" x2="137" y2="309.695"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="148.997" y1="341.047" x2="157.01" y2="319.026"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="174.269" y1="347.819" x2="178.337" y2="324.741"/>
<g>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M192.637,101.333V79.786h0.469l14.341,16.518V79.786h2.124v21.548
		h-0.483l-14.224-16.316v16.316H192.637z"/>
</g>
<g>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M135.018,109.119l-1.585,0.916
		c-0.452-1.449-0.468-2.755-0.05-3.919c0.419-1.162,1.186-2.064,2.299-2.708c0.811-0.468,1.674-0.719,2.588-0.754
		c0.915-0.035,1.746,0.15,2.492,0.556c0.746,0.406,1.316,0.95,1.711,1.634c0.773,1.339,0.72,2.784-0.161,4.336
		c0.923-0.128,1.792-0.038,2.608,0.27c1.153,0.434,2.024,1.162,2.614,2.184c0.509,0.882,0.762,1.856,0.759,2.925
		c-0.004,1.068-0.281,2.053-0.832,2.954c-0.552,0.9-1.324,1.638-2.317,2.211c-1.34,0.773-2.68,1.032-4.02,0.778
		c-1.341-0.256-2.579-1.051-3.718-2.387l1.522-0.879c0.851,0.854,1.655,1.362,2.416,1.523c0.971,0.201,1.953,0.014,2.946-0.56
		c1.128-0.651,1.838-1.512,2.131-2.584c0.293-1.07,0.178-2.061-0.347-2.97c-0.351-0.606-0.849-1.075-1.496-1.406
		s-1.335-0.465-2.063-0.401c-0.729,0.064-1.715,0.412-2.957,1.044l-0.823-1.427c0.712-0.411,1.287-0.91,1.729-1.498
		c0.44-0.588,0.681-1.152,0.721-1.692c0.041-0.54-0.07-1.039-0.335-1.498c-0.391-0.676-0.998-1.105-1.822-1.288
		c-0.824-0.182-1.648-0.034-2.473,0.441c-0.67,0.387-1.139,0.909-1.408,1.566C134.879,107.144,134.836,108.021,135.018,109.119z"/>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M147.018,102.192l-1.586,0.915
		c-0.452-1.449-0.469-2.755-0.05-3.918c0.419-1.163,1.185-2.065,2.298-2.708c0.811-0.468,1.674-0.719,2.588-0.754
		c0.916-0.034,1.746,0.151,2.492,0.556c0.747,0.406,1.317,0.95,1.712,1.633c0.773,1.34,0.72,2.785-0.161,4.336
		c0.922-0.128,1.791-0.038,2.608,0.27c1.153,0.434,2.024,1.162,2.614,2.184c0.509,0.881,0.762,1.856,0.758,2.924
		c-0.003,1.068-0.28,2.053-0.832,2.954c-0.551,0.9-1.323,1.638-2.317,2.212c-1.339,0.773-2.679,1.032-4.019,0.777
		c-1.342-0.256-2.58-1.05-3.718-2.386l1.522-0.879c0.85,0.854,1.654,1.362,2.416,1.523c0.971,0.201,1.953,0.015,2.947-0.56
		c1.127-0.65,1.838-1.512,2.131-2.583c0.292-1.071,0.177-2.061-0.349-2.97c-0.35-0.606-0.849-1.075-1.495-1.406
		s-1.335-0.465-2.064-0.401c-0.729,0.064-1.714,0.412-2.955,1.044l-0.824-1.427c0.711-0.411,1.287-0.91,1.729-1.498
		c0.439-0.588,0.681-1.152,0.721-1.692c0.041-0.54-0.071-1.039-0.336-1.498c-0.391-0.676-0.998-1.105-1.821-1.288
		c-0.824-0.182-1.648-0.035-2.474,0.441c-0.669,0.387-1.138,0.909-1.406,1.566C146.877,100.216,146.834,101.094,147.018,102.192z"/>
</g>
<g>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M98.427,152.854l-0.915,1.585
		c-1.116-1.029-1.783-2.152-2.003-3.369c-0.218-1.216-0.006-2.381,0.637-3.495c0.468-0.81,1.09-1.459,1.864-1.947
		c0.775-0.487,1.587-0.742,2.436-0.764c0.85-0.022,1.615,0.165,2.299,0.559c1.34,0.773,2.016,2.052,2.029,3.835
		c0.734-0.572,1.532-0.928,2.394-1.07c1.215-0.2,2.334-0.006,3.355,0.584c0.882,0.509,1.588,1.227,2.119,2.154
		s0.783,1.918,0.757,2.974c-0.027,1.056-0.328,2.08-0.901,3.074c-0.773,1.339-1.805,2.233-3.092,2.683
		c-1.29,0.45-2.76,0.38-4.413-0.208l0.879-1.522c1.163,0.314,2.114,0.352,2.854,0.111c0.941-0.312,1.699-0.965,2.273-1.958
		c0.65-1.127,0.835-2.229,0.553-3.303c-0.281-1.074-0.877-1.875-1.786-2.399c-0.606-0.35-1.272-0.507-1.998-0.47
		c-0.726,0.037-1.388,0.265-1.988,0.685c-0.599,0.42-1.278,1.214-2.038,2.382l-1.427-0.824c0.411-0.712,0.66-1.433,0.748-2.162
		c0.087-0.729,0.014-1.338-0.222-1.826c-0.234-0.488-0.581-0.864-1.04-1.129c-0.676-0.391-1.417-0.458-2.222-0.205
		c-0.805,0.255-1.444,0.794-1.921,1.619c-0.387,0.67-0.531,1.356-0.436,2.06C97.319,151.214,97.72,151.996,98.427,152.854z"/>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M108.273,145.444c-2.051-1.184-3.509-2.319-4.376-3.407
		c-0.866-1.087-1.355-2.199-1.471-3.336c-0.113-1.137,0.116-2.203,0.69-3.196c0.581-1.008,1.399-1.752,2.452-2.235
		c1.054-0.481,2.299-0.625,3.736-0.429c1.438,0.196,3.137,0.859,5.096,1.991c1.952,1.127,3.371,2.259,4.256,3.395
		c0.886,1.136,1.385,2.286,1.497,3.451s-0.122,2.25-0.704,3.258c-0.574,0.993-1.377,1.724-2.407,2.19
		c-1.03,0.467-2.24,0.598-3.63,0.392S110.31,146.62,108.273,145.444z M109.148,143.99c1.757,1.014,3.175,1.624,4.255,1.83
		c1.08,0.205,2.03,0.136,2.85-0.209c0.82-0.344,1.427-0.858,1.821-1.542c0.398-0.691,0.544-1.467,0.435-2.329
		c-0.108-0.861-0.529-1.71-1.26-2.546c-0.884-1.027-2.104-1.99-3.663-2.89c-1.566-0.904-2.948-1.471-4.146-1.703
		c-1.197-0.23-2.203-0.175-3.017,0.168c-0.814,0.342-1.417,0.852-1.808,1.529c-0.398,0.691-0.542,1.473-0.43,2.345
		c0.112,0.874,0.526,1.728,1.243,2.564C106.145,142.044,107.385,142.972,109.148,143.99z"/>
</g>
<g>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M109.917,264.448l0.86,1.49c-1.558,0.843-3.112,1.057-4.665,0.643
		c-1.552-0.414-2.745-1.344-3.579-2.789c-0.826-1.431-1.033-2.855-0.619-4.272c0.412-1.418,1.246-2.489,2.501-3.214
		c0.882-0.509,1.83-0.775,2.846-0.799c1.016-0.025,2.534,0.191,4.554,0.646l6.408,1.466l-4.143-7.175l1.502-0.866l6.097,10.561
		l-9.851-2.234c-1.987-0.459-3.397-0.69-4.23-0.693c-0.834-0.002-1.597,0.196-2.288,0.596c-0.874,0.504-1.439,1.265-1.694,2.281
		s-0.109,1.997,0.436,2.941c0.574,0.993,1.383,1.628,2.425,1.905C107.52,265.209,108.667,265.047,109.917,264.448z"/>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M93.859,248.767l-0.182-0.315l10.846-6.262l-1.086-1.882
		l1.48-0.854l1.086,1.882l3.616-2.087l0.891,1.543l-3.616,2.088l4.297,7.441L93.859,248.767z M105.415,243.733l-6.531,3.771
		l9.191,0.836L105.415,243.733z"/>
</g>
<g>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M153.804,300.688l1.491,0.86c-0.928,1.509-2.167,2.472-3.72,2.889
		c-1.551,0.417-3.049,0.208-4.494-0.626c-1.431-0.826-2.322-1.955-2.673-3.391c-0.352-1.434-0.165-2.778,0.56-4.033
		c0.509-0.882,1.198-1.586,2.065-2.115s2.29-1.102,4.267-1.718l6.282-1.935l-7.174-4.142l0.867-1.502l10.561,6.098l-9.648,2.991
		c-1.951,0.595-3.288,1.1-4.011,1.514c-0.723,0.415-1.284,0.969-1.683,1.66c-0.505,0.873-0.614,1.815-0.326,2.823
		c0.287,1.007,0.903,1.784,1.847,2.329c0.994,0.573,2.012,0.719,3.053,0.438C152.109,302.546,153.021,301.833,153.804,300.688z"/>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M137.497,297.756l-3.055-1.764l8.978-15.551l1.543,0.891
		l-8.1,14.029l2.442,1.409L137.497,297.756z"/>
</g>
<g>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M265.364,296.643l-3.056,1.764l-8.979-15.551l1.544-0.892
		l8.099,14.029l2.442-1.41L265.364,296.643z"/>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M248.674,306.278l-0.854-1.479l5.525-3.19l-1.67-4.689
		c-0.562,0.588-1.112,1.038-1.655,1.352c-1.415,0.817-2.855,1.017-4.321,0.6c-1.466-0.418-2.627-1.366-3.481-2.847
		c-0.59-1.021-0.895-2.096-0.915-3.221c-0.021-1.126,0.242-2.148,0.787-3.069c0.544-0.921,1.345-1.686,2.402-2.296
		c1.262-0.729,2.548-0.957,3.86-0.686c1.313,0.272,2.408,0.971,3.285,2.1l-1.597,0.922c-0.466-0.53-0.949-0.899-1.45-1.108
		c-0.501-0.209-1.055-0.291-1.664-0.245c-0.607,0.046-1.172,0.219-1.693,0.52c-1.021,0.59-1.676,1.484-1.963,2.681
		c-0.285,1.198-0.093,2.376,0.578,3.538c0.604,1.048,1.428,1.709,2.468,1.981c1.041,0.272,2.114,0.089,3.221-0.55
		c0.916-0.529,1.865-1.449,2.848-2.758l3.024,8.558L248.674,306.278z"/>
</g>
<g>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M305.018,250.334l-1.765,3.056l-15.551-8.979l0.892-1.543
		l14.028,8.099l1.41-2.442L305.018,250.334z"/>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M295.219,256.076l0.86-1.49c1.509,0.928,2.472,2.167,2.889,3.719
		c0.417,1.551,0.209,3.05-0.626,4.495c-0.825,1.431-1.955,2.322-3.391,2.672c-1.434,0.352-2.778,0.165-4.033-0.56
		c-0.881-0.509-1.586-1.197-2.115-2.064c-0.528-0.868-1.102-2.29-1.718-4.267l-1.935-6.282l-4.142,7.174l-1.501-0.867l6.097-10.561
		l2.991,9.648c0.595,1.95,1.1,3.287,1.514,4.011c0.416,0.724,0.969,1.284,1.66,1.684c0.874,0.505,1.815,0.614,2.823,0.326
		c1.008-0.287,1.784-0.903,2.329-1.848c0.574-0.993,0.719-2.012,0.438-3.053C297.078,257.771,296.364,256.859,295.219,256.076z"/>
</g>
<g>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M302.287,142.898l-0.097,1.762l-8.791-0.532
		c0.516,0.455,0.916,0.929,1.201,1.423c0.713,1.235,0.873,2.541,0.479,3.917c-0.395,1.376-1.244,2.44-2.547,3.193
		c-0.896,0.517-1.824,0.775-2.787,0.776s-1.879-0.259-2.747-0.778c-0.868-0.519-1.557-1.22-2.066-2.102
		c-0.493-0.854-0.744-1.765-0.753-2.733c-0.01-0.968,0.239-1.876,0.746-2.724s1.187-1.518,2.039-2.01
		c0.635-0.366,1.376-0.623,2.227-0.77c0.85-0.147,1.96-0.174,3.33-0.08L302.287,142.898z M293.023,145.97
		c-0.522-0.903-1.286-1.491-2.292-1.762c-1.007-0.27-1.964-0.143-2.874,0.383c-0.902,0.521-1.488,1.284-1.757,2.291
		c-0.269,1.008-0.142,1.963,0.379,2.866s1.286,1.491,2.292,1.762c1.007,0.271,1.961,0.146,2.863-0.376
		c0.909-0.524,1.498-1.291,1.767-2.298S293.545,146.873,293.023,145.97z"/>
</g>
<g>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M252.478,100.756l-1.585-0.915
		c1.029-1.116,2.152-1.783,3.369-2.003c1.216-0.218,2.381-0.006,3.495,0.637c0.81,0.468,1.459,1.09,1.947,1.864
		c0.487,0.775,0.742,1.587,0.764,2.436c0.022,0.85-0.165,1.615-0.559,2.299c-0.773,1.34-2.052,2.016-3.835,2.029
		c0.572,0.734,0.928,1.532,1.07,2.394c0.2,1.215,0.006,2.334-0.584,3.355c-0.509,0.882-1.227,1.588-2.154,2.119
		s-1.918,0.783-2.974,0.757c-1.056-0.027-2.08-0.328-3.074-0.901c-1.339-0.773-2.233-1.805-2.683-3.092
		c-0.45-1.29-0.38-2.76,0.208-4.413l1.522,0.879c-0.314,1.163-0.352,2.114-0.111,2.854c0.312,0.941,0.965,1.699,1.958,2.273
		c1.127,0.65,2.229,0.835,3.303,0.553c1.074-0.281,1.875-0.877,2.399-1.786c0.35-0.606,0.507-1.272,0.47-1.998
		c-0.037-0.726-0.265-1.388-0.685-1.988c-0.42-0.599-1.214-1.278-2.382-2.038l0.824-1.427c0.712,0.411,1.433,0.66,2.162,0.748
		c0.729,0.087,1.338,0.014,1.826-0.222c0.488-0.234,0.864-0.581,1.129-1.04c0.391-0.676,0.458-1.417,0.205-2.222
		c-0.255-0.805-0.794-1.444-1.619-1.921c-0.67-0.387-1.356-0.531-2.06-0.436C254.118,99.647,253.336,100.049,252.478,100.756z"/>
</g>
<g>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M80.119,212.927v-2.212l15.527-4.402l-15.527-6.177v-0.447
		l15.527-6.105l-15.527-4.477v-2.195l21.548,6.191v0.4l-16.304,6.394l16.304,6.519v0.402L80.119,212.927z"/>
</g>
<g>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M206.271,302.679l-1.831,1.099
		c-1.289-2.373-2.778-3.56-4.468-3.56c-0.723,0-1.401,0.168-2.036,0.505s-1.118,0.789-1.45,1.355s-0.498,1.167-0.498,1.802
		c0,0.723,0.244,1.431,0.732,2.124c0.674,0.957,1.904,2.109,3.691,3.457c1.797,1.357,2.915,2.339,3.354,2.944
		c0.762,1.016,1.143,2.114,1.143,3.296c0,0.938-0.225,1.792-0.674,2.563s-1.082,1.379-1.897,1.824s-1.702,0.667-2.659,0.667
		c-1.016,0-1.965-0.251-2.849-0.754s-1.819-1.428-2.805-2.776l1.758-1.333c0.811,1.074,1.501,1.782,2.073,2.124
		s1.194,0.513,1.868,0.513c0.869,0,1.58-0.264,2.131-0.791s0.828-1.177,0.828-1.948c0-0.469-0.098-0.923-0.293-1.362
		s-0.552-0.918-1.069-1.436c-0.283-0.273-1.211-0.991-2.783-2.153c-1.865-1.377-3.145-2.603-3.838-3.677s-1.04-2.153-1.04-3.237
		c0-1.563,0.593-2.92,1.78-4.072s2.629-1.729,4.329-1.729c1.309,0,2.495,0.349,3.56,1.047S205.373,301.039,206.271,302.679z"/>
</g>
<g>
	<path fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" d="M320.547,193.303v12.349h-2.109v-10.195h-6.753v10.107h-2.109
		v-10.107h-8.467v10.107H299v-12.261H320.547z"/>
</g>
</svg>
