<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="400.667px" height="400.666px" viewBox="0 0 400.667 400.666" enable-background="new 0 0 400.667 400.666"
	 xml:space="preserve">
<circle fill="#232323" cx="200.333" cy="200" r="161"/>
<g>
	<text transform="matrix(1 0 0 1 289.6499 203.833)" fill="#FFFFFF" font-family="sans-serif" font-size="11">990</text>
	<text transform="matrix(0.9848 0.1736 -0.1736 0.9848 287.627 219.2837)" fill="#FFFFFF" font-family="sans-serif" font-size="11">985</text>
	<text transform="matrix(0.9397 0.342 -0.342 0.9397 282.9517 234.1489)" fill="#FFFFFF" font-family="sans-serif" font-size="11">980</text>
	<text transform="matrix(0.9848 -0.1736 0.1736 0.9848 288.959 188.2651)" fill="#FFFFFF" font-family="sans-serif" font-size="11">995</text>
	<text transform="matrix(0.9397 -0.342 0.342 0.9397 280.3481 174.9556)" fill="#FFFFFF" font-family="sans-serif" font-size="11">1000</text>
	<text transform="matrix(0.866 -0.5 0.5 0.866 274.7842 161.4429)" fill="#FFFFFF" font-family="sans-serif" font-size="11">1005</text>
	<text transform="matrix(0.766 -0.6428 0.6428 0.766 266.9575 149.1006)" fill="#FFFFFF" font-family="sans-serif" font-size="11">1010</text>
	<text transform="matrix(0.6428 -0.766 0.766 0.6428 257.1064 138.3047)" fill="#FFFFFF" font-family="sans-serif" font-size="11">1015</text>
	<text transform="matrix(0.5 -0.866 0.866 0.5 245.5322 129.3848)" fill="#FFFFFF" font-family="sans-serif" font-size="11">1020</text>
	<text transform="matrix(0.342 -0.9397 0.9397 0.342 232.583 122.6084)" fill="#FFFFFF" font-family="sans-serif" font-size="11">1025</text>
	<text transform="matrix(0.1736 -0.9848 0.9848 0.1736 218.6543 118.1841)" fill="#FFFFFF" font-family="sans-serif" font-size="11">1030</text>
	
		<text transform="matrix(2.980232e-008 -1 1 2.980232e-008 204.168 116.2451)" fill="#FFFFFF" font-family="sans-serif" font-size="11">1035</text>
	<g>
		
			<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="313.562" y1="200.333" x2="324.167" y2="200.333"/>
	</g>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="315.803" y1="196.3" x2="321.803" y2="196.091"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="315.604" y1="192.273" x2="321.592" y2="191.854"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="315.264" y1="188.255" x2="321.234" y2="187.628"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="314.784" y1="184.251" x2="320.729" y2="183.417"/>
	<g>
		
			<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="311.784" y1="219.989" x2="322.227" y2="221.83"/>
	</g>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="314.691" y1="216.407" x2="320.636" y2="217.243"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="315.194" y1="212.406" x2="321.165" y2="213.034"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="315.557" y1="208.391" x2="321.545" y2="208.81"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="315.78" y1="204.364" x2="321.78" y2="204.574"/>
	<g>
		
			<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="306.619" y1="239.038" x2="316.584" y2="242.665"/>
	</g>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="310.104" y1="236.016" x2="315.814" y2="237.871"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="311.293" y1="232.162" x2="317.065" y2="233.818"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="312.349" y1="228.271" x2="318.173" y2="229.724"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="313.268" y1="224.345" x2="319.14" y2="225.594"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="302.182" y1="254.529" x2="307.483" y2="257.349"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="304.022" y1="250.941" x2="309.418" y2="253.574"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="305.737" y1="247.293" x2="311.221" y2="249.734"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="307.325" y1="243.586" x2="312.89" y2="245.835"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="314.165" y1="180.267" x2="320.078" y2="179.225"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="313.407" y1="176.307" x2="319.28" y2="175.059"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="312.51" y1="172.375" x2="318.337" y2="170.923"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="311.478" y1="168.478" x2="317.25" y2="166.823"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="310.311" y1="164.619" x2="316.022" y2="162.764"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="309.009" y1="160.802" x2="314.652" y2="158.75"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="307.575" y1="157.034" x2="313.143" y2="154.786"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="306.01" y1="153.317" x2="311.497" y2="150.876"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="304.317" y1="149.658" x2="309.715" y2="147.027"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="302.497" y1="146.06" x2="307.8" y2="143.242"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="300.552" y1="142.528" x2="305.754" y2="139.526"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="298.486" y1="139.065" x2="303.58" y2="135.885"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="296.299" y1="135.677" x2="301.279" y2="132.321"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="293.996" y1="132.368" x2="298.855" y2="128.839"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="291.579" y1="129.141" x2="296.312" y2="125.445"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="289.05" y1="126" x2="293.652" y2="122.141"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="286.414" y1="122.949" x2="290.878" y2="118.932"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="283.672" y1="119.992" x2="287.994" y2="115.821"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="280.83" y1="117.133" x2="285.002" y2="112.813"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="277.889" y1="114.375" x2="281.908" y2="109.912"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="274.854" y1="111.72" x2="278.714" y2="107.121"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="271.728" y1="109.173" x2="275.425" y2="104.442"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="268.515" y1="106.737" x2="272.044" y2="101.88"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="265.219" y1="104.415" x2="268.577" y2="99.438"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="261.844" y1="102.209" x2="265.026" y2="97.118"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="258.394" y1="100.123" x2="261.396" y2="94.923"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="254.873" y1="98.158" x2="257.692" y2="92.856"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="251.286" y1="96.316" x2="253.917" y2="90.92"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="247.636" y1="94.602" x2="250.078" y2="89.117"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="243.929" y1="93.016" x2="246.178" y2="87.449"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="240.169" y1="91.56" x2="242.222" y2="85.918"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="236.361" y1="90.236" x2="238.215" y2="84.526"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="232.508" y1="89.045" x2="234.163" y2="83.275"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="228.617" y1="87.991" x2="230.069" y2="82.166"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="224.691" y1="87.072" x2="225.939" y2="81.2"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="220.735" y1="86.291" x2="221.778" y2="80.379"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="216.754" y1="85.648" x2="217.59" y2="79.704"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="212.753" y1="85.146" x2="213.381" y2="79.175"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="208.738" y1="84.783" x2="209.157" y2="78.794"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="204.711" y1="84.56" x2="204.921" y2="78.561"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="196.648" y1="84.534" x2="196.439" y2="78.535"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="192.621" y1="84.734" x2="192.203" y2="78.746"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="188.603" y1="85.074" x2="187.976" y2="79.103"/>
	<line fill="none" stroke="#FFFFFF" stroke-miterlimit="10" x1="184.599" y1="85.554" x2="183.764" y2="79.609"/>
	<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="311.9" y1="180.666" x2="322.343" y2="178.824"/>
	
		<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="306.847" y1="161.586" x2="316.812" y2="157.959"/>
	
		<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="298.559" y1="143.674" x2="307.743" y2="138.372"/>
	<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="287.286" y1="127.474" x2="295.41" y2="120.657"/>
	
		<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="273.371" y1="113.477" x2="280.187" y2="105.353"/>
	<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="257.236" y1="102.108" x2="262.539" y2="92.924"/>
	<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="239.373" y1="93.715" x2="243" y2="83.75"/>
	<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="220.324" y1="88.55" x2="222.165" y2="78.107"/>
	<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="200.667" y1="86.772" x2="200.667" y2="76.168"/>
</g>
</svg>
