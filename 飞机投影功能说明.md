# 飞机实时地面投影功能

## 功能概述
为3D飞机可视化系统添加了实时地面投影功能，飞机的投影会实时显示在地面上，并根据飞机的位置、高度和航向进行动态调整。

## 主要特性

### 1. 实时位置跟踪
- 投影会实时跟随飞机的X、Y坐标移动
- 投影始终保持在地面高度（Z=1）
- 与飞机的移动完全同步

### 2. 逼真的飞机形状投影
- 使用自定义Shape几何体创建飞机轮廓投影
- 包含机身、机翼和尾翼的简化形状
- 比简单的圆形或椭圆形投影更加逼真

### 3. 高度相关的视觉效果
- **透明度变化**: 飞机高度越高，投影越淡
- **尺寸变化**: 飞机高度越高，投影越大（模拟光源距离效果）
- 最小透明度: 0.1，最大透明度: 0.4

### 4. 航向角同步
- 投影会根据飞机的Y轴旋转（航向角）进行相应旋转
- 确保投影方向与飞机朝向一致

### 5. 动态视觉增强
- 轻微的透明度变化，模拟真实阴影的动态效果
- 细微的尺寸变化，让投影更加生动
- 使用深蓝黑色(0x001122)作为阴影颜色，更加逼真

## 技术实现

### 核心函数

#### `createPlaneShadow()`
- 创建飞机形状的投影几何体
- 使用THREE.Shape绘制飞机轮廓
- 设置投影材质和初始位置

#### `updatePlaneShadow(planePosition)`
- 更新投影的实时位置
- 根据飞机高度调整透明度和尺寸
- 同步飞机的航向角
- 添加动态视觉效果

### 关键参数
- **投影高度**: Z = 1 (避免z-fighting)
- **最大高度**: 1000 (用于计算透明度和尺寸)
- **基础缩放**: 1.0
- **颜色**: 0x001122 (深蓝黑色)

## 使用方法

1. 投影会在飞机模型加载时自动创建
2. 在`setPosition()`函数中自动调用`updatePlaneShadow()`
3. 投影会随着飞机的移动自动更新
4. 无需额外的用户操作

## 视觉效果
- 投影呈现飞机的简化轮廓形状
- 随飞机高度变化透明度和大小
- 跟随飞机航向旋转
- 具有轻微的动态变化效果
- 颜色为深蓝黑色，更符合真实阴影的视觉效果

这个功能大大增强了3D场景的真实感和沉浸感，让用户能够更直观地感受到飞机在3D空间中的位置关系。
