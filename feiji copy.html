<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title> 3D地图可视化</title>
    <style>
        html,
        body,
        #container {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div id="container"></div>
    <!-- <script>
        console.warn = console.log;
    </script> -->
    <script src="./js/three.js"></script>
    <script src="./js/loaders/GLTFLoader.js"></script>
    <script src="./js/loaders/OBJLoader.js"></script>
    <script src="./js/turf.min.js"></script>
    <script src="./js/modifiers/CurveModifier.js"></script>
    <script
        src="https://webapi.amap.com/maps?v=2.0&key=61975505b7e621f5f8247d703e08fdfc&plugin=AMap.ControlBar,AMap.ToolBar,AMap.MouseTool"></script>
    <script type="text/javascript">
        console.warn = console.log;
        javascript: (function () {
            var script = document.createElement('script');
            script.onload = function () {
                var stats = new Stats();
                document.body.appendChild(stats.dom);
                requestAnimationFrame(function loop() {
                    stats.update();
                    requestAnimationFrame(loop)
                });
            };
            script.src = './js/stats.min.js';
            document.head.appendChild(script);
        })()
    </script>
    <script type="text/javascript">
        var map = new AMap.Map('container', {
            center: [108.983823, 34.246131],
            zooms: [10, 26],
            rotation: 45,
            wallColor: '#666',   //楼面周围颜色
            roofColor: '#000 ',    //楼的顶部颜色
            buildingAnimation: true,//楼块出现是否带动画
            zoom: 17,
            viewMode: '3D',
            pitch: 50,
        });
        var controlBar = new AMap.ControlBar({
            position: {
                right: '10px',
                top: '10px'
            }
        });
        controlBar.addTo(map);
        var toolBar = new AMap.ToolBar({
            position: {
                right: '40px',
                top: '110px'
            }
        });
        toolBar.addTo(map);
        const ACTION_SELECT = 1, ACTION_NONE = 0;
        const curveHandles = [];
        const mouse = new THREE.Vector2();
        var camera;
        var renderer;
        var scene;
        // 数据转换工具
        var customCoords = map.customCoords;
        var meshes = [];
        // 数据使用转换工具进行转换，这个操作必须要提前执行（在获取镜头参数 函数之前执行），否则将会获得一个错误信息。
        var mesh2,mesh3
        var data = customCoords.lngLatsToCoords([
            [108.982923, 34.246131],
            [108.984823, 34.246131],
            [108.986823, 34.246131],
        ]);
        var object, box;
        var objPosition = [108.983823, 34.246131]
        var objPosition1 = [108.985823, 34.246131]
        // 创建 GL 图层
        var gllayer = new AMap.GLCustomLayer({
            // 图层的层级
            zIndex: 10,
            // heightFactor: 100, //2倍于默认高度，3D下有效
            // 初始化的操作，创建图层过程中执行一次。
            init: (gl) => {
                // 这里我们的地图模式是 3D，所以创建一个透视相机，相机的参数初始化可以随意设置，因为在 render 函数中，每一帧都需要同步相机参数，因此这里变得不那么重要。
                // 如果你需要 2D 地图（viewMode: '2D'），那么你需要创建一个正交相机
                camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 100, 1 << 30);
                renderer = new THREE.WebGLRenderer({
                    context: gl,  // 地图的 gl 上下文
                    // alpha: true,
                    // antialias: true,
                    // canvas: gl.canvas,
                });
                // 自动清空画布这里必须设置为 false，否则地图底图将无法显示
                renderer.autoClear = false;
                scene = new THREE.Scene();
                // 环境光照和平行光
                var aLight = new THREE.AmbientLight(0xffffff, 0.3);
                var dLight = new THREE.DirectionalLight(0xffffff, 1);
                dLight.position.set(1000, -100, 900);
                scene.add(dLight);
                scene.add(aLight);
                  initGltf()

            },
            render: () => {
                // 这里必须执行！！重新设置 three 的 gl 上下文状态。
                renderer.resetState();
                // 重新设置图层的渲染中心点，将模型等物体的渲染中心点重置
                // 否则和 LOCA 可视化等多个图层能力使用的时候会出现物体位置偏移的问题
                customCoords.setCenter([116.271363, 39.992414]);
                var { near, far, fov, up, lookAt, position } = customCoords.getCameraParams();
                // 2D 地图下使用的正交相机
                // var { near, far, top, bottom, left, right, position, rotation } = customCoords.getCameraParams();
                // 这里的顺序不能颠倒，否则可能会出现绘制卡顿的效果。
                camera.near = near;
                camera.far = far;
                camera.fov = fov;
                camera.position.set(...position);
                camera.up.set(...up);
                camera.lookAt(...lookAt);
                camera.updateProjectionMatrix();
                // 2D 地图使用的正交相机参数赋值
                // camera.top = top;
                // camera.bottom = bottom;
                // camera.left = left;
                // camera.right = right;
                // camera.position.set(...position);
                // camera.updateProjectionMatrix();
                renderer.render(scene, camera);
                // 这里必须执行！！重新设置 three 的 gl 上下文状态。
                renderer.resetState();
            },
        });
        map.add(gllayer);
        // 初始化模型
        function initGltf() {
            var loader = new THREE.GLTFLoader();
            loader.load('./gltf/DamagedHelmet.gltf', (gltf) => {
                // loader.load('https://a.amap.com/jsapi_demos/static/gltf/Duck.gltf', (gltf) => {
                object = gltf.scene;
                object.scale.set(30, 30, 30)    //模型大小缩放
                setRotation({
                    x: 90, y: 0, z: 0
                });
                console.log(object)
                console.log(objPosition)
                 mesh2 = object.clone();//克隆网格模型
                 //mesh2.position.set(434.14601409621537,0,100)
                 mesh3 = object.clone();//克隆网格模型
                 //mesh3.position.set(434.15601409621537,0,100)
                 setPosition(mesh2,objPosition);  //模型位置定义
                 setPosition(mesh3,objPosition);  //模型位置定义
                 console.log(mesh2)
                //  mesh3.position.x=434.14601409621537;
                //  mesh3.position.y=0;
                //  mesh3.position.z=100;
                 setPositionn(mesh3,[108.989823, 34.246131]);
                // setPosition(objPosition);  //模型位置定义
                scene.add(object);
                scene.add(mesh2);
                scene.add(mesh3);//
                // 模型动画
                startMove();//运动
            });
        }
        console.log(scene);
        // 模型移动函数



        // 设置模型角度
        function setRotation(rotation) {
            var x = Math.PI / 180 * (rotation.x || 0);
            var y = Math.PI / 180 * (rotation.y || 0);
            var z = Math.PI / 180 * (rotation.z || 0);
            object.rotation.set(x, y, z);
        }
        // 设置模型位置
        function setPosition(object,lnglat) {
            // 对模型的经纬度进行转换*//*****************
            var position = customCoords.lngLatsToCoords([
                lnglat
            ])[0];
            // console.log(lnglat);
            // console.log(position);
            object.position.setX(position[0])           // 模型坐标
            object.position.setY(position[1])          // 模型坐标
            // console.log(position)
            //   map.setCenter(lnglat, true)
            object.position.setZ(200)            // 模型高度   此处为定义的模型高度
        }
        //定义模型位置编号
        function setPositionn(obj,lnglat) {
            var p = customCoords.lngLatsToCoords([
                lnglat
            ])[0];
            // 对模型的经纬度进行转换*//*****************
            console.log(p);
            obj.position.setX(p[0])           // 模型坐标
            obj.position.setY(p[1])          // 模型坐标
            obj.position.setZ(300)            // 模型高度   此处为定义的模型高度
        }
        // 设置模型角度
        function setAngle(angle) {

            var x = object.rotation.x;
            var z = object.rotation.z;
            var y = Math.PI / 180 * angle;
            object.rotation.set(x, y, z);
        }
        // 动画
        function animate() {
            for (let i = 0; i < meshes.length; i++) {
                let { mesh, count } = meshes[i];
                count += 1;
                mesh.rotateZ((count / 180) * Math.PI);
            }
            map.render();
            requestAnimationFrame(animate);
        }
        animate();
        // 改变模型位置和角度
        var centerPoint = turf.point([108.983823, 34.246131]);
        var timer = 0;
        function startMove() {
            if (timer > 360) {
                timer = 0;
            }
            requestAnimationFrame(function () {
                timer += 0.4;
                var pos = turf.transformTranslate(centerPoint, 0.3, timer).geometry.coordinates;
                //console.log(pos);
                var angle = timer;
               // console.log(pos)
                setPosition(object,pos);   //物体运动的那个位置设定
                // console.log(pos, angle)
              //  object.lookAt(object.position);  //物体朝向
                // console.log(pos, angle



                map.setCenter(pos, true)   //新增代码
                ///console.log(pos, angle)
                //  setAngle(angle);
                // 执行地图的渲染
                map.render();
                // 这里可以添加一些动画效果
                /// map.setCenter(pos, false)   //新增代码
                startMove();
            });
        }
        // 测试移动
        // let  a=108.989823;
        // let b=34.246131
        // setInterval(() => {
        //     a=a+0.000001;
        //     b=b+0.000001;
        //     setPositionn(mesh3,[a,b ]);
        // }, 1);

    </script>
</body>

</html>