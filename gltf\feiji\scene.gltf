{"accessors": [{"bufferView": 2, "componentType": 5126, "count": 384, "max": [0.06732004135847092, -1.4928587675094604, -0.12341403216123581], "min": [-0.06731980293989182, -1.5023587942123413, -0.25805389881134033], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4608, "componentType": 5126, "count": 384, "max": [1.0, 1.1143473784613889e-05, 1.0], "min": [-1.0, -1.1143172741867602e-05, -1.0], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 384, "max": [0.8127309083938599, 0.3127310276031494], "min": [0.6872691512107849, 0.18726903200149536], "type": "VEC2"}, {"bufferView": 0, "componentType": 5125, "count": 1536, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 9216, "componentType": 5126, "count": 720, "max": [0.13951750099658966, -1.497182846069336, -0.05121655389666557], "min": [-0.13951727747917175, -1.5051140785217285, -0.3302513659000397], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 17856, "componentType": 5126, "count": 720, "max": [0.9419171810150146, -0.33579739928245544, 0.9419195652008057], "min": [-0.9419191479682922, -1.0, -0.941916823387146], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3072, "componentType": 5126, "count": 720, "max": [0.9306460022926331, 0.4306459128856659], "min": [0.569354236125946, 0.06935412436723709], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6144, "componentType": 5125, "count": 3072, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 26496, "componentType": 5126, "count": 640, "max": [0.14195573329925537, -1.5023585557937622, -0.04877832531929016], "min": [-0.14195550978183746, -1.5076295137405396, -0.3326895833015442], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 34176, "componentType": 5126, "count": 640, "max": [0.7180912494659424, -0.6959201097488403, 0.7180839776992798], "min": [-0.7180953025817871, -0.7302188873291016, -0.7180886268615723], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 8832, "componentType": 5126, "count": 640, "max": [0.9356608390808105, 0.435660719871521], "min": [0.5643393993377686, 0.06433933228254318], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 18432, "componentType": 5125, "count": 3072, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 41856, "componentType": 5126, "count": 640, "max": [0.16956879198551178, -1.497182846069336, -0.021165279671549797], "min": [-0.16956856846809387, -1.5076295137405396, -0.3603026568889618], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 49536, "componentType": 5126, "count": 640, "max": [0.1595272570848465, -0.9871914982795715, 0.15952558815479279], "min": [-0.15952710807323456, -1.0, -0.15952560305595398], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 13952, "componentType": 5126, "count": 640, "max": [0.9900000095367432, 0.49000000953674316], "min": [0.5099999904632568, 0.01000000536441803], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 30720, "componentType": 5125, "count": 3072, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 57216, "componentType": 5126, "count": 454, "max": [0.16956879198551178, -1.2770730257034302, -0.021165277808904648], "min": [-0.16956856846809387, -1.5076295137405396, -0.3603026568889618], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 62664, "componentType": 5126, "count": 454, "max": [1.0, 1.0, 1.0], "min": [-1.0, -7.719396535321721e-07, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 19072, "componentType": 5126, "count": 454, "max": [1.0000001192092896, 1.0000001192092896], "min": [0.0, 0.010000006295740604], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 43008, "componentType": 5125, "count": 1920, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 68112, "componentType": 5126, "count": 134, "max": [0.06732003390789032, -1.4928587675094604, -0.12341403216123581], "min": [-0.06731980293989182, -1.4928590059280396, -0.25805389881134033], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 69720, "componentType": 5126, "count": 134, "max": [1.5845163943595253e-05, -1.0, 1.5366731531685218e-05], "min": [-2.1039129933342338e-05, -1.0, -1.5366755178547464e-05], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 22704, "componentType": 5126, "count": 134, "max": [0.8127310276031494, 0.3127310574054718], "min": [0.6872692108154297, 0.18726904690265656], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 50688, "componentType": 5125, "count": 384, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 71328, "componentType": 5126, "count": 4880, "max": [1.8424293994903564, 0.6737903952598572, 0.7619789838790894], "min": [-1.8424293994903564, -0.3281458020210266, -0.9864026308059692], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 129888, "componentType": 5126, "count": 4880, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 23776, "componentType": 5126, "count": 4880, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 52224, "componentType": 5125, "count": 14352, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 188448, "componentType": 5126, "count": 439, "max": [0.2786727249622345, 0.7462801933288574, 0.1002410501241684], "min": [-0.2786727249622345, 0.23751020431518555, -0.6164501905441284], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 193716, "componentType": 5126, "count": 439, "max": [0.9975656867027283, 0.9996768236160278, 0.9986117482185364], "min": [-0.9975656867027283, -0.8011741638183594, -0.9987232089042664], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 62816, "componentType": 5126, "count": 439, "max": [0.8750000596046448, 1.0], "min": [0.125, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 109632, "componentType": 5125, "count": 1620, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 198984, "componentType": 5126, "count": 623, "max": [1.8570150136947632, 0.09957657009363174, 1.026995301246643], "min": [-1.9313832521438599, -0.5234354138374329, 0.9001646041870117], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 206460, "componentType": 5126, "count": 623, "max": [0.013723920099437237, -0.1532738357782364, -0.9840271472930908], "min": [-0.0006246648845262825, -0.17760558426380157, -0.9881822466850281], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 66328, "componentType": 5126, "count": 623, "max": [0.37834885716438293, 0.8997161388397217], "min": [0.2805771827697754, 0.7996862530708313], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 116112, "componentType": 5125, "count": 2400, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 213936, "componentType": 5126, "count": 5249, "max": [2.0496108531951904, 0.20230570435523987, 1.5987586975097656], "min": [-2.1257781982421875, -1.1443793773651123, 0.9001646041870117], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 276924, "componentType": 5126, "count": 5249, "max": [0.9994133114814758, 0.9678494930267334, 0.9969825744628906], "min": [-0.9967150092124939, -0.7514427304267883, -0.9953799843788147], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 71312, "componentType": 5126, "count": 5249, "max": [0.6626511216163635, 1.002312421798706], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 125712, "componentType": 5125, "count": 19200, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 339912, "componentType": 5126, "count": 15221, "max": [2.0560102462768555, 0.20230570435523987, 1.5987586975097656], "min": [-2.1420950889587402, -1.139247179031372, 0.4716288149356842], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 522564, "componentType": 5126, "count": 15221, "max": [0.9987705945968628, 0.9975789785385132, 0.9999362826347351], "min": [-0.9999946355819702, -0.9998505711555481, -0.9996896982192993], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 113304, "componentType": 5126, "count": 15221, "max": [0.8750644326210022, 1.0260437726974487], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 202512, "componentType": 5125, "count": 57936, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 705216, "componentType": 5126, "count": 5015, "max": [2.025315523147583, 0.1957869827747345, 1.5788986682891846], "min": [-2.101578712463379, -1.14201021194458, 0.5677094459533691], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 765396, "componentType": 5126, "count": 5015, "max": [0.9787987470626831, 0.9685704708099365, 0.9913845062255859], "min": [-0.9967150092124939, -0.9999650716781616, -0.9953799843788147], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 235072, "componentType": 5126, "count": 5015, "max": [0.7814860939979553, 1.0024945735931396], "min": [0.1866939514875412, 0.0010638358071446419], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 434256, "componentType": 5125, "count": 18864, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 825576, "componentType": 5126, "count": 5936, "max": [1.8105756044387817, -0.8172969818115234, 1.4305158853530884], "min": [-2.0176382064819336, -1.2250549793243408, 1.1183338165283203], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 896808, "componentType": 5126, "count": 5936, "max": [0.9858425259590149, 0.06635526567697525, 0.9658700823783875], "min": [-0.9990662336349487, -0.999998152256012, -0.9967124462127686], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 275192, "componentType": 5126, "count": 5936, "max": [0.37500011920928955, 1.0021893978118896], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 509712, "componentType": 5125, "count": 19200, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 968040, "componentType": 5126, "count": 730, "max": [0.2856225073337555, -0.8862612247467041, -0.32209262251853943], "min": [-0.2856225073337555, -0.9987876415252686, -0.5775437951087952], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 976800, "componentType": 5126, "count": 730, "max": [0.9993947148323059, -0.025358431041240692, 0.2942904233932495], "min": [-0.9993946552276611, -0.3632476329803467, -0.22199533879756927], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 322680, "componentType": 5126, "count": 730, "max": [0.5920817255973816, 0.7310685515403748], "min": [0.4184783697128296, 0.5282065868377686], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 586512, "componentType": 5125, "count": 2208, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 985560, "componentType": 5126, "count": 4654, "max": [0.2856225073337555, -0.786353588104248, -0.24099096655845642], "min": [-0.2856225073337555, -1.0309467315673828, -0.6388373374938965], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1041408, "componentType": 5126, "count": 4654, "max": [0.9830414056777954, -0.0360611267387867, 0.8728825449943542], "min": [-0.9830414652824402, -0.999923050403595, -0.9989373683929443], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 328520, "componentType": 5126, "count": 4654, "max": [0.6258726119995117, 0.8748246431350708], "min": [0.25017547607421875, 0.37517184019088745], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 595344, "componentType": 5125, "count": 19488, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1097256, "componentType": 5126, "count": 2123, "max": [0.2870747148990631, -0.7823227643966675, -0.24099096655845642], "min": [-0.2870747148990631, -0.9727045893669128, -0.6388373374938965], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1122732, "componentType": 5126, "count": 2123, "max": [0.9725809693336487, 0.7979283332824707, 0.9438664317131042], "min": [-0.9725801944732666, -0.3294776678085327, -0.6200562119483948], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 365752, "componentType": 5126, "count": 2123, "max": [0.6258726119995117, 0.8748245239257812], "min": [0.4552037715911865, 0.37517547607421875], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 673296, "componentType": 5125, "count": 7296, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1148208, "componentType": 5126, "count": 52, "max": [0.47395825386047363, -0.261791467666626, 1.086158037185669], "min": [-0.47395825386047363, -0.5500547885894775, 0.5958244800567627], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1148832, "componentType": 5126, "count": 52, "max": [1.0, 0.43227851390838623, 0.9700399041175842], "min": [-1.0, -0.6474958658218384, -0.8837252855300903], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 382736, "componentType": 5126, "count": 52, "max": [0.75, 0.5], "min": [0.3565584719181061, 0.5], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 702480, "componentType": 5125, "count": 132, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1149456, "componentType": 5126, "count": 18, "max": [0.47395825386047363, -0.3630807399749756, 1.0739071369171143], "min": [-0.47395825386047363, -0.55911785364151, 0.6646639704704285], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1149672, "componentType": 5126, "count": 18, "max": [0.19124941527843475, -0.7708133459091187, 0.6302246451377869], "min": [-0.191249281167984, -0.9657204747200012, 0.25224387645721436], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 383152, "componentType": 5126, "count": 18, "max": [0.3750000298023224, 0.5], "min": [0.25, 0.5], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 703008, "componentType": 5125, "count": 48, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1149888, "componentType": 5126, "count": 76, "max": [0.47395825386047363, -0.261791467666626, 1.086158037185669], "min": [-0.47395825386047363, -0.5613136887550354, 0.5958244800567627], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1150800, "componentType": 5126, "count": 76, "max": [0.7542110681533813, 0.9021841287612915, 0.9700399041175842], "min": [-0.7542112469673157, -0.9657204747200012, -0.762069046497345], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 383296, "componentType": 5126, "count": 76, "max": [0.75, 0.5], "min": [0.25, 0.5], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 703200, "componentType": 5125, "count": 240, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1151712, "componentType": 5126, "count": 1208, "max": [0.2062806934118271, -0.5579894185066223, 0.9734918475151062], "min": [-0.20686331391334534, -0.572006344795227, 0.6110277771949768], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1166208, "componentType": 5126, "count": 1208, "max": [0.8586423397064209, -0.5123656392097473, 0.8058357238769531], "min": [-0.8586369752883911, -0.9997748732566833, -0.8058319687843323], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 383904, "componentType": 5126, "count": 1208, "max": [0.8850311636924744, 1.000000238418579], "min": [0.17852459847927094, 0.9661168456077576], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 704160, "componentType": 5125, "count": 4608, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1180704, "componentType": 5126, "count": 3994, "max": [0.2962474226951599, -0.1923239529132843, 1.0558888912200928], "min": [-0.2962473928928375, -0.572006344795227, 0.5196452736854553], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1228632, "componentType": 5126, "count": 3994, "max": [0.9990358948707581, 0.8137527108192444, 0.9149229526519775], "min": [-0.9990358948707581, -0.9995452761650085, -0.8888733386993408], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 393568, "componentType": 5126, "count": 3994, "max": [0.8850311636924744, 1.000000238418579], "min": [0.1990482658147812, 0.3821896016597748], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 722592, "componentType": 5125, "count": 13152, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1276560, "componentType": 5126, "count": 1443, "max": [0.21472008526325226, -0.5578412413597107, 0.9895870685577393], "min": [-0.21472008526325226, -0.5672903656959534, 0.6051583290100098], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1293876, "componentType": 5126, "count": 1443, "max": [0.06324788928031921, -0.9926416277885437, 0.12108955532312393], "min": [-0.06325233727693558, -0.9999992847442627, -0.060673922300338745], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 425520, "componentType": 5126, "count": 1443, "max": [0.8850311636924744, 1.000000238418579], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 775200, "componentType": 5125, "count": 4512, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1311192, "componentType": 5126, "count": 2029, "max": [0.26448169350624084, -0.4734058380126953, 0.8507285714149475], "min": [-0.26448166370391846, -0.5609045624732971, 0.6864433288574219], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1335540, "componentType": 5126, "count": 2029, "max": [0.9982717633247375, 0.07393526285886765, 0.9995511174201965], "min": [-0.9982715845108032, -0.999367892742157, -0.9999046921730042], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 437064, "componentType": 5126, "count": 2029, "max": [1.000396490097046, 1.0000001192092896], "min": [-0.00039643197669647634, 0.008118508383631706], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 793248, "componentType": 5125, "count": 7248, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1359888, "componentType": 5126, "count": 1479, "max": [0.13152089715003967, -0.5477561950683594, 0.8997443318367004], "min": [-0.13152089715003967, -0.558961033821106, 0.6880173087120056], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1377636, "componentType": 5126, "count": 1479, "max": [0.17562121152877808, -0.9779504537582397, 0.20883707702159882], "min": [-0.17562082409858704, -0.9887866973876953, -0.14938916265964508], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 453296, "componentType": 5126, "count": 1479, "max": [0.5916170477867126, 1.0000001192092896], "min": [0.46980375051498413, 0.9999996423721313], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 822240, "componentType": 5125, "count": 4608, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1395384, "componentType": 5126, "count": 2985, "max": [0.2022765874862671, -0.5501980185508728, 0.9695723652839661], "min": [-0.2022765874862671, -0.5718879699707031, 0.6189420223236084], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1431204, "componentType": 5126, "count": 2985, "max": [0.9353925585746765, -0.325276643037796, 0.9456189870834351], "min": [-0.9353927373886108, -0.9995444416999817, -0.9242199659347534], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 465128, "componentType": 5126, "count": 2985, "max": [0.8644359707832336, 1.000000238418579], "min": [0.1990482658147812, 0.9999995827674866], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 840672, "componentType": 5125, "count": 9216, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1467024, "componentType": 5126, "count": 4549, "max": [0.28513267636299133, -0.4491540789604187, 1.0558888912200928], "min": [-0.28513267636299133, -0.5672903656959534, 0.5477927923202515], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1521612, "componentType": 5126, "count": 4549, "max": [0.8412806987762451, -0.5207968354225159, 0.8536806702613831], "min": [-0.8412840962409973, -0.999546229839325, -0.7643647789955139], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 489008, "componentType": 5126, "count": 4549, "max": [0.9899449944496155, 1.0000001192092896], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 877536, "componentType": 5125, "count": 15120, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1576200, "componentType": 5126, "count": 500, "max": [0.12060143053531647, -0.5500260591506958, 0.889114499092102], "min": [-0.12060143053531647, -0.558961033821106, 0.6987648010253906], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1582200, "componentType": 5126, "count": 500, "max": [0.00014438308426178992, -0.9995418787002563, 0.030265865847468376], "min": [-0.0001796285796444863, -0.9995458722114563, 0.030137071385979652], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 525400, "componentType": 5126, "count": 500, "max": [0.5537008047103882, 1.0], "min": [0.5072826743125916, 0.9999996423721313], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 938016, "componentType": 5125, "count": 1200, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1588200, "componentType": 5126, "count": 768, "max": [0.2022765874862671, -0.5581079125404358, 0.9695723652839661], "min": [-0.2022765874862671, -0.5718879699707031, 0.6189420223236084], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1597416, "componentType": 5126, "count": 768, "max": [1.0648851457517594e-05, 0.9995436668395996, -0.030208054929971695], "min": [-1.0427008419355843e-05, 0.9995431303977966, -0.030223755165934563], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 529400, "componentType": 5126, "count": 768, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 942816, "componentType": 5125, "count": 1152, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1606632, "componentType": 5126, "count": 344, "max": [0.23067806661128998, 0.003380065318197012, 1.25803804397583], "min": [-0.23067806661128998, -0.3856167793273926, 1.2522684335708618], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1610760, "componentType": 5126, "count": 344, "max": [0.6833500862121582, 0.668084979057312, 0.7552633285522461], "min": [-0.683341920375824, -0.6680489182472229, 0.7300909161567688], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 535544, "componentType": 5126, "count": 344, "max": [0.6250001192092896, 1.0000001192092896], "min": [0.375, 0.7462042570114136], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 947424, "componentType": 5125, "count": 1536, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1614888, "componentType": 5126, "count": 138, "max": [0.2245188057422638, -0.003037144895642996, 1.25803804397583], "min": [-0.2245188057422638, -0.37919941544532776, 1.2580342292785645], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1616544, "componentType": 5126, "count": 138, "max": [7.113751507858979e-06, 0.0, 1.0], "min": [-6.8731683313671965e-06, -1.7977716197492555e-05, 1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 538296, "componentType": 5126, "count": 138, "max": [0.621168851852417, 0.9967681169509888], "min": [0.37883126735687256, 0.7529534697532654], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 953568, "componentType": 5125, "count": 384, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1618200, "componentType": 5126, "count": 824, "max": [0.23067808151245117, 0.003380065318197012, 1.2522717714309692], "min": [-0.23067808151245117, -0.38567349314689636, 1.0247776508331299], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1628088, "componentType": 5126, "count": 824, "max": [1.0, 1.0, 0.0002554204547777772], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 539400, "componentType": 5126, "count": 824, "max": [0.8750001788139343, 0.7494915723800659], "min": [0.1250000149011612, 0.003795832861214876], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 955104, "componentType": 5125, "count": 3456, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1637976, "componentType": 5126, "count": 456, "max": [1.6837389469146729, 0.9702268242835999, -1.1692686080932617], "min": [-1.6837389469146729, 0.6201249361038208, -1.2115062475204468], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1643448, "componentType": 5126, "count": 456, "max": [0.0021947806235402822, 0.13122659921646118, -0.991352379322052], "min": [-0.0021947859786450863, 0.1102927029132843, -0.9938969016075134], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 545992, "componentType": 5126, "count": 456, "max": [0.37834885716438293, 0.8997161388397217], "min": [0.2805771827697754, 0.7996862530708313], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 968928, "componentType": 5125, "count": 1728, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1648920, "componentType": 5126, "count": 7498, "max": [1.8822743892669678, 1.0245647430419922, 0.6149875521659851], "min": [-1.8822743892669678, -0.6767091155052185, -1.2115062475204468], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1738896, "componentType": 5126, "count": 7498, "max": [1.0, 0.9996353387832642, 0.9881174564361572], "min": [-1.0, -0.9744271039962769, -0.9686460494995117], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 549640, "componentType": 5126, "count": 7498, "max": [0.9295966029167175, 0.9929099082946777], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 975840, "componentType": 5125, "count": 27120, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1828872, "componentType": 5126, "count": 24600, "max": [1.8969660997390747, 1.0245647430419922, 0.9520008563995361], "min": [-1.8969660997390747, -0.6946097612380981, -1.204736590385437], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2124072, "componentType": 5126, "count": 24600, "max": [1.0, 1.0, 1.0], "min": [-1.0, -0.9999985694885254, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 609624, "componentType": 5126, "count": 24600, "max": [1.0000001192092896, 0.9929099082946777], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1084320, "componentType": 5125, "count": 92016, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2419272, "componentType": 5126, "count": 12715, "max": [1.8584423065185547, 1.0160932540893555, 0.5897521376609802], "min": [-1.8584423065185547, -0.6322561502456665, -1.017774224281311], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2571852, "componentType": 5126, "count": 12715, "max": [1.0, 1.0, 0.9999980926513672], "min": [-1.0, -1.0, -0.9996694326400757], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 806424, "componentType": 5126, "count": 12715, "max": [1.0003966093063354, 0.9929099082946777], "min": [-0.00039643203490413725, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1452384, "componentType": 5125, "count": 47184, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2724432, "componentType": 5126, "count": 202, "max": [0.2605225443840027, -0.2646044194698334, 0.1939913034439087], "min": [-0.2605225443840027, -0.5371336340904236, -0.4368119239807129], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2726856, "componentType": 5126, "count": 202, "max": [1.0, 1.1216033271921333e-05, 9.613905604055617e-06], "min": [-1.0, -1.3745855540037155e-05, -1.1367253136995714e-05], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 908144, "componentType": 5126, "count": 202, "max": [0.8123652338981628, 0.9571820497512817], "min": [0.1886758655309677, 0.2929530441761017], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1641120, "componentType": 5125, "count": 576, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2729280, "componentType": 5126, "count": 19096, "max": [0.38012444972991943, 0.4194331169128418, 0.4812198579311371], "min": [-0.38012444972991943, -0.6224648356437683, -0.9367339611053467], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2958432, "componentType": 5126, "count": 19096, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 909760, "componentType": 5126, "count": 19096, "max": [3.0862090587615967, 3.3150763511657715], "min": [-2.1552352905273438, -2.3150761127471924], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1643424, "componentType": 5125, "count": 81600, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3187584, "componentType": 5126, "count": 3864, "max": [0.35049623250961304, -0.2772935628890991, 0.22744378447532654], "min": [-0.35049623250961304, -0.5282812118530273, -0.3629812002182007], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3233952, "componentType": 5126, "count": 3864, "max": [1.0, 1.0, 0.9844856262207031], "min": [-1.0, -1.0, -0.9990242123603821], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1062528, "componentType": 5126, "count": 3864, "max": [0.8463705778121948, 0.7527952790260315], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1969824, "componentType": 5125, "count": 10728, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3280320, "componentType": 5126, "count": 224, "max": [0.27061501145362854, 0.55442875623703, 1.0010753870010376], "min": [-0.27061501145362854, 0.38336625695228577, 0.9356019496917725], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3283008, "componentType": 5126, "count": 224, "max": [0.9954683184623718, 1.0, 0.999998927116394], "min": [-0.9954683184623718, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1093440, "componentType": 5126, "count": 224, "max": [0.7500000596046448, 0.875], "min": [0.25, 0.375], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2012736, "componentType": 5125, "count": 612, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3285696, "componentType": 5126, "count": 60, "max": [0.3431549072265625, 0.19142401218414307, 1.2507953643798828], "min": [-0.3431549072265625, -0.21711650490760803, 1.0352786779403687], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3286416, "componentType": 5126, "count": 60, "max": [1.0, 1.0, 7.651673286090954e-07], "min": [-1.0, -1.0, -0.0008555480744689703], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1095232, "componentType": 5126, "count": 60, "max": [0.5753588676452637, 0.8600865006446838], "min": [0.5, 0.38991352915763855], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2015184, "componentType": 5125, "count": 168, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3287136, "componentType": 5126, "count": 92, "max": [0.3431549072265625, 0.19142401218414307, 1.253570795059204], "min": [-0.3431549072265625, -0.22075659036636353, 1.0307154655456543], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3288240, "componentType": 5126, "count": 92, "max": [0.7377216219902039, 0.7071011662483215, 0.7386909127235413], "min": [-0.7377222776412964, -0.7071054577827454, -0.7386941909790039], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1095712, "componentType": 5126, "count": 92, "max": [0.5753588676452637, 0.875], "min": [0.4999999701976776, 0.375], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2015856, "componentType": 5125, "count": 252, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3289344, "componentType": 5126, "count": 118, "max": [0.3431549072265625, 0.19142401218414307, 1.253570795059204], "min": [-0.3431549072265625, -0.22075659036636353, 1.0307154655456543], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3290760, "componentType": 5126, "count": 118, "max": [1.0, 0.807894229888916, 1.0], "min": [-0.7378133535385132, -0.7310281991958618, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1096448, "componentType": 5126, "count": 118, "max": [0.5753588676452637, 0.875], "min": [0.4999999701976776, 0.375], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2016864, "componentType": 5125, "count": 306, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3292176, "componentType": 5126, "count": 12, "max": [0.3773956000804901, 0.3125006854534149, 0.8855029344558716], "min": [-0.37737634778022766, -0.46970266103744507, 0.8855029344558716], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3292320, "componentType": 5126, "count": 12, "max": [0.0, 9.653276578092118e-08, -1.0], "min": [0.0, 9.653276578092118e-08, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1097392, "componentType": 5126, "count": 12, "max": [0.625, 0.5], "min": [0.35487374663352966, 0.25], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2018088, "componentType": 5125, "count": 30, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3292464, "componentType": 5126, "count": 12, "max": [0.3773956000804901, 0.3125006854534149, 0.9734079837799072], "min": [-0.37737634778022766, -0.46970266103744507, 0.9734079837799072], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3292608, "componentType": 5126, "count": 12, "max": [0.0, -6.435518784542182e-09, 1.0], "min": [0.0, -6.435518784542182e-09, 1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1097488, "componentType": 5126, "count": 12, "max": [0.625, 1.0], "min": [0.35487374663352966, 0.75], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2018208, "componentType": 5125, "count": 30, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3292752, "componentType": 5126, "count": 30, "max": [0.3773956000804901, 0.3125006854534149, 0.9734079837799072], "min": [-0.37737634778022766, -0.46970266103744507, 0.8855029344558716], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3293112, "componentType": 5126, "count": 30, "max": [1.0, 1.0, -0.0], "min": [-1.0, -0.9951847791671753, -0.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1097584, "componentType": 5126, "count": 30, "max": [0.875, 0.7606794238090515], "min": [0.125, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2018328, "componentType": 5125, "count": 72, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3293472, "componentType": 5126, "count": 40, "max": [0.2628818452358246, 0.2629493474960327, 1.1777600049972534], "min": [-0.2628818452358246, 0.16169174015522003, 1.0354701280593872], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3293952, "componentType": 5126, "count": 40, "max": [1.0, 0.9378069043159485, 0.6669183373451233], "min": [-1.0, 0.0, -0.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1097824, "componentType": 5126, "count": 40, "max": [0.875, 1.0], "min": [0.375, 0.5], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2018616, "componentType": 5125, "count": 72, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3294432, "componentType": 5126, "count": 40, "max": [0.2628818452358246, 0.2629493474960327, 1.1777600049972534], "min": [-0.2628818452358246, 0.16169174015522003, 1.0512694120407104], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3294912, "componentType": 5126, "count": 40, "max": [1.0, 0.47698190808296204, 0.8897199630737305], "min": [-1.0, -0.45910242199897766, -0.8790294528007507], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1098144, "componentType": 5126, "count": 40, "max": [0.625, 0.75], "min": [0.375, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2018904, "componentType": 5125, "count": 72, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3295392, "componentType": 5126, "count": 48, "max": [0.2628818452358246, 0.19078749418258667, 1.1777600049972534], "min": [-0.2628818452358246, 0.16169174015522003, 1.0354701280593872], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3295968, "componentType": 5126, "count": 48, "max": [1.0, 0.47719624638557434, 0.8507059812545776], "min": [-1.0, -1.0, -0.8789134621620178], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1098464, "componentType": 5126, "count": 48, "max": [0.3750000298023224, 1.0], "min": [0.125, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2019192, "componentType": 5125, "count": 96, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3296544, "componentType": 5126, "count": 180, "max": [0.17335575819015503, 1.2082301378250122, 0.8100144863128662], "min": [-0.17335575819015503, 1.0323785543441772, 0.6665348410606384], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3298704, "componentType": 5126, "count": 180, "max": [0.9999175667762756, 0.9900449514389038, 0.9900574684143066], "min": [-0.9999175667762756, -0.7995921969413757, -0.7995926141738892], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1098848, "componentType": 5126, "count": 180, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2019576, "componentType": 5125, "count": 528, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3300864, "componentType": 5126, "count": 408, "max": [0.146404430270195, 0.5472452640533447, 1.338822841644287], "min": [-0.1464044600725174, 0.41152694821357727, 1.2681949138641357], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3305760, "componentType": 5126, "count": 408, "max": [0.9347731471061707, 0.9717164635658264, 0.756386399269104], "min": [-0.9347730278968811, -0.9733595252037048, -0.056568268686532974], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1100288, "componentType": 5126, "count": 408, "max": [0.6528903245925903, 1.0], "min": [0.125, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2021688, "componentType": 5125, "count": 792, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3310656, "componentType": 5126, "count": 396, "max": [0.146404430270195, 0.5472452640533447, 1.338822841644287], "min": [-0.1464044600725174, 0.41152694821357727, 1.1941274404525757], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3315408, "componentType": 5126, "count": 396, "max": [0.9347731471061707, 0.9998295307159424, 0.9998481273651123], "min": [-0.9347730278968811, -0.9984289407730103, -0.6495187282562256], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1103552, "componentType": 5126, "count": 396, "max": [0.875, 0.9721096754074097], "min": [0.125, 0.125], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2024856, "componentType": 5125, "count": 792, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3320160, "componentType": 5126, "count": 144, "max": [0.146404430270195, 0.5460843443870544, 1.3374446630477905], "min": [-0.1464044600725174, 0.4126926064491272, 1.2681949138641357], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3321888, "componentType": 5126, "count": 144, "max": [1.0, 0.3495301306247711, 0.10496243089437485], "min": [-1.0, -0.36039456725120544, -2.9180318961152807e-07], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1106720, "componentType": 5126, "count": 144, "max": [0.6231462955474854, 0.7473253011703491], "min": [0.3768549859523773, 0.002674645744264126], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2028024, "componentType": 5125, "count": 360, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3323616, "componentType": 5126, "count": 4, "max": [0.36267876625061035, 0.4710886478424072, 0.4919512867927551], "min": [-0.3626788258552551, -0.47108882665634155, 0.4919512867927551], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3323664, "componentType": 5126, "count": 4, "max": [0.0, 0.0, -1.0], "min": [0.0, 0.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1107872, "componentType": 5126, "count": 4, "max": [0.625, 0.5], "min": [0.375, 0.25], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2029464, "componentType": 5125, "count": 6, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3323712, "componentType": 5126, "count": 20, "max": [0.36267876625061035, 0.4710886478424072, 0.8057118058204651], "min": [-0.3626788258552551, -0.47108882665634155, 0.4919512867927551], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3323952, "componentType": 5126, "count": 20, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -0.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1107904, "componentType": 5126, "count": 20, "max": [0.875, 1.0], "min": [0.125, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2029488, "componentType": 5125, "count": 30, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3324192, "componentType": 5126, "count": 56, "max": [0.3232884407043457, -0.13390672206878662, 0.6379126310348511], "min": [-0.3232884407043457, -1.0098634958267212, 0.4266580045223236], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3324864, "componentType": 5126, "count": 56, "max": [0.9951847195625305, 1.0, 1.0], "min": [-0.9951847195625305, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1108064, "componentType": 5126, "count": 56, "max": [0.75, 0.875], "min": [0.25, 0.375], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2029608, "componentType": 5125, "count": 144, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3325536, "componentType": 5126, "count": 8, "max": [0.3232884407043457, -0.13390672206878662, 0.6379126310348511], "min": [-0.3232884407043457, -0.8976243734359741, 0.4266580045223236], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3325632, "componentType": 5126, "count": 8, "max": [1.0, 0.0, -0.0], "min": [-1.0, -0.09801749885082245, -0.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1108512, "componentType": 5126, "count": 8, "max": [0.625, 0.75], "min": [0.4100155830383301, 0.5], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2030184, "componentType": 5125, "count": 12, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3325728, "componentType": 5126, "count": 628, "max": [0.20221339166164398, -0.26847758889198303, -0.6088889241218567], "min": [-0.20221339166164398, -0.6019653677940369, -0.9167212247848511], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3333264, "componentType": 5126, "count": 628, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1108576, "componentType": 5126, "count": 628, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2030232, "componentType": 5125, "count": 2016, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3340800, "componentType": 5126, "count": 168, "max": [0.1510711908340454, -0.37669894099235535, -0.9152942895889282], "min": [-0.15429380536079407, -0.4774373471736908, -0.9167212247848511], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3342816, "componentType": 5126, "count": 168, "max": [0.7580446600914001, 0.7117207646369934, -0.6494229435920715], "min": [-0.7468276023864746, -0.716261625289917, -0.7404836416244507], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1113600, "componentType": 5126, "count": 168, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2038296, "componentType": 5125, "count": 504, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3344832, "componentType": 5126, "count": 64, "max": [0.11479230225086212, 0.07247705012559891, -0.934400200843811], "min": [-0.11479230225086212, 0.060515858232975006, -0.9370620250701904], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3345600, "componentType": 5126, "count": 64, "max": [8.654299676891242e-07, 0.5378751158714294, -0.8307913541793823], "min": [-7.827137551430496e-07, -0.5565840601921082, -0.9999997615814209], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1114944, "componentType": 5126, "count": 64, "max": [0.3750000596046448, 0.4868465065956116], "min": [0.375, 0.2641647756099701], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2040312, "componentType": 5125, "count": 192, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3346368, "componentType": 5126, "count": 2253, "max": [0.1894751787185669, 0.5995824933052063, -0.7181466221809387], "min": [-0.18947026133537292, -0.126295804977417, -0.9370620250701904], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3373404, "componentType": 5126, "count": 2253, "max": [0.7107561826705933, 0.7212841510772705, 0.9966070652008057], "min": [-0.7126163840293884, -0.9967725276947021, -0.9669172167778015], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1115456, "componentType": 5126, "count": 2253, "max": [0.8750001192092896, 0.7500001192092896], "min": [0.125, 0.12278158962726593], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2041080, "componentType": 5125, "count": 6144, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3400440, "componentType": 5126, "count": 2053, "max": [0.1894751787185669, 0.5995824933052063, -0.7180675864219666], "min": [-0.18947026133537292, -0.126295804977417, -0.9370620250701904], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3425076, "componentType": 5126, "count": 2053, "max": [1.0, 1.0, 1.0], "min": [-1.0, -0.999998927116394, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1133480, "componentType": 5126, "count": 2053, "max": [0.8750001788139343, 1.0000001192092896], "min": [0.17325499653816223, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2065656, "componentType": 5125, "count": 6624, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3449712, "componentType": 5126, "count": 301, "max": [-0.010295149870216846, -0.40294885635375977, -0.9067251086235046], "min": [-0.15274575352668762, -0.4572722017765045, -0.9166365265846252], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3453324, "componentType": 5126, "count": 301, "max": [0.9970085620880127, 0.9719513058662415, 1.0], "min": [-0.9970086216926575, -0.9719513058662415, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1149904, "componentType": 5126, "count": 301, "max": [0.8750000596046448, 0.9951688051223755], "min": [0.125, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2092152, "componentType": 5125, "count": 690, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3456936, "componentType": 5126, "count": 159, "max": [-0.010295149870216846, -0.40294885635375977, -0.916068971157074], "min": [-0.1111564040184021, -0.4572722017765045, -0.9166365265846252], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3458844, "componentType": 5126, "count": 159, "max": [0.7039299607276917, 0.6774196624755859, -0.7081676125526428], "min": [-0.7039323449134827, -0.6774340271949768, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1152312, "componentType": 5126, "count": 159, "max": [0.6488224267959595, 0.5032546520233154], "min": [0.3511776626110077, 0.2548311948776245], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2094912, "componentType": 5125, "count": 414, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3460752, "componentType": 5126, "count": 48, "max": [0.11598454415798187, 0.5522170066833496, 1.006456732749939], "min": [-0.11598455905914307, 0.4869191348552704, 0.9945216774940491], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3461328, "componentType": 5126, "count": 48, "max": [0.7774145007133484, 0.8936783075332642, 0.6703833937644958], "min": [-0.7774142622947693, -0.8962177038192749, 0.4436146020889282], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1153584, "componentType": 5126, "count": 48, "max": [0.75, 0.7509839534759521], "min": [0.25, 0.75], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2096568, "componentType": 5125, "count": 132, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3461904, "componentType": 5126, "count": 24, "max": [0.11203780770301819, 0.5495631098747253, 1.006456732749939], "min": [-0.11203782260417938, 0.48952510952949524, 0.9997845888137817], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3462192, "componentType": 5126, "count": 24, "max": [0.5164498090744019, 0.0039964537136256695, 0.9999920129776001], "min": [-0.5164498090744019, 0.0034225787967443466, 0.8563107252120972], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1153968, "componentType": 5126, "count": 24, "max": [0.625, 0.875], "min": [0.375, 0.75], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2097096, "componentType": 5125, "count": 60, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3462480, "componentType": 5126, "count": 76, "max": [0.11598454415798187, 0.552273154258728, 1.0012149810791016], "min": [-0.11598455905914307, 0.4869191348552704, 0.9817996621131897], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3463392, "componentType": 5126, "count": 76, "max": [0.9805718660354614, 0.9999897480010986, 0.004732072819024324], "min": [-0.9805720448493958, -0.9999897480010986, -0.9999920725822449], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1154160, "componentType": 5126, "count": 76, "max": [0.75, 0.7500000596046448], "min": [0.25, 0.375], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2097336, "componentType": 5125, "count": 192, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3464304, "componentType": 5126, "count": 1743, "max": [0.2917287051677704, 1.2694777250289917, 1.151821255683899], "min": [-0.2917287051677704, 0.21848243474960327, 0.7215816378593445], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3485220, "componentType": 5126, "count": 1743, "max": [0.9903944134712219, 0.9845770597457886, 0.9770988821983337], "min": [-0.9903952479362488, -0.6346410512924194, -0.18906646966934204], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1154768, "componentType": 5126, "count": 1743, "max": [0.7500001192092896, 0.8750001192092896], "min": [0.018920980393886566, 0.015888188034296036], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2098104, "componentType": 5125, "count": 5808, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3506136, "componentType": 5126, "count": 5353, "max": [0.4075533151626587, 1.2817912101745605, 1.151821255683899], "min": [-0.4075533151626587, 0.06543374806642532, 0.708798885345459], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3570372, "componentType": 5126, "count": 5353, "max": [0.9895976185798645, 0.9869844913482666, 0.9982555508613586], "min": [-0.9895974397659302, -0.9998367428779602, -0.9959127902984619], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1168712, "componentType": 5126, "count": 5353, "max": [0.7500001192092896, 0.8750001788139343], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2121336, "componentType": 5125, "count": 23904, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3634608, "componentType": 5126, "count": 625, "max": [0.2366669923067093, 1.2817912101745605, 1.0341373682022095], "min": [-0.23666700720787048, 1.1077944040298462, 0.6965901255607605], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3642108, "componentType": 5126, "count": 625, "max": [0.9999790787696838, 0.8119498491287231, 0.1081225797533989], "min": [-0.9999790787696838, -0.6844435930252075, -0.5837605595588684], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1211536, "componentType": 5126, "count": 625, "max": [0.5986016988754272, 0.7508286833763123], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2216952, "componentType": 5125, "count": 2304, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3649608, "componentType": 5126, "count": 2220, "max": [0.39137330651283264, 0.8508429527282715, 1.1495696306228638], "min": [-0.39137330651283264, 0.20168036222457886, 0.8764503598213196], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3676248, "componentType": 5126, "count": 2220, "max": [0.9921078681945801, 0.9895541071891785, 0.6872437000274658], "min": [-0.9921079874038696, -0.990648090839386, -0.9978757500648499], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1216536, "componentType": 5126, "count": 2220, "max": [0.4450071156024933, 0.8750001192092896], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2226168, "componentType": 5125, "count": 7536, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3702888, "componentType": 5126, "count": 1113, "max": [0.39624738693237305, 0.8221755027770996, 1.111708164215088], "min": [-0.39624738693237305, 0.3631301820278168, 0.9051316976547241], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3716244, "componentType": 5126, "count": 1113, "max": [0.9455747008323669, 0.9977394938468933, 0.999626636505127], "min": [-0.9455795288085938, -0.9998114705085754, -0.7993932962417603], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1234296, "componentType": 5126, "count": 1113, "max": [0.5456283688545227, 0.7500001192092896], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2256312, "componentType": 5125, "count": 4512, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3729600, "componentType": 5126, "count": 5364, "max": [0.4075533151626587, 1.2299175262451172, 1.1244442462921143], "min": [-0.4075533151626587, 0.07738081365823746, 0.6887926459312439], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3793968, "componentType": 5126, "count": 5364, "max": [0.9999865293502808, 0.8371763229370117, 0.9999980330467224], "min": [-0.9999865889549255, -0.8971632122993469, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1243200, "componentType": 5126, "count": 5364, "max": [0.75, 0.8750001192092896], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2274360, "componentType": 5125, "count": 21216, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3858336, "componentType": 5126, "count": 46, "max": [0.1497000902891159, -0.37841784954071045, -0.9167682528495789], "min": [0.02099444717168808, -0.4756060242652893, -0.9175029993057251], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3858888, "componentType": 5126, "count": 46, "max": [0.7026969790458679, 0.7012242078781128, -0.708570659160614], "min": [-0.7027016282081604, -0.7012103199958801, -0.7146512269973755], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1286112, "componentType": 5126, "count": 46, "max": [0.6488224267959595, 0.5155055522918701], "min": [0.3511776626110077, 0.23449444770812988], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2359224, "componentType": 5125, "count": 120, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3859440, "componentType": 5126, "count": 20, "max": [0.14896535873413086, -0.3791525661945343, -0.9175029993057251], "min": [0.02172917127609253, -0.47487127780914307, -0.9175029993057251], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3859680, "componentType": 5126, "count": 20, "max": [0.0, -2.5074359655263834e-07, -1.0], "min": [0.0, -2.5074359655263834e-07, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1286480, "componentType": 5126, "count": 20, "max": [0.6453818082809448, 0.49877384305000305], "min": [0.3546183705329895, 0.25144657492637634], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2359704, "componentType": 5125, "count": 54, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3859920, "componentType": 5126, "count": 50, "max": [0.1497000902891159, -0.37841784954071045, -0.9056568145751953], "min": [0.02099444717168808, -0.4756060242652893, -0.9167682528495789], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3860520, "componentType": 5126, "count": 50, "max": [0.9958358407020569, 0.9944350123405457, -0.0], "min": [-0.9958358407020569, -0.9944350123405457, -0.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1286640, "componentType": 5126, "count": 50, "max": [0.875, 0.7630599737167358], "min": [0.125, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2359920, "componentType": 5125, "count": 120, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3861120, "componentType": 5126, "count": 20, "max": [0.1497000902891159, -0.37841784954071045, -0.9056568145751953], "min": [0.02099444717168808, -0.4756060242652893, -0.9056568145751953], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3861360, "componentType": 5126, "count": 20, "max": [0.0, 8.14862630704738e-08, 1.0], "min": [0.0, 8.14862630704738e-08, 1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1287040, "componentType": 5126, "count": 20, "max": [0.6488223671913147, 1.0], "min": [0.3511776626110077, 0.75], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2360400, "componentType": 5125, "count": 54, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3861600, "componentType": 5126, "count": 95, "max": [0.2931227684020996, 0.6621081829071045, -0.021863073110580444], "min": [-0.2931227684020996, 0.5627235174179077, -0.6277366876602173], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3862740, "componentType": 5126, "count": 95, "max": [0.5759343504905701, 0.9983465075492859, -0.014751628041267395], "min": [-0.5759344100952148, 0.8130045533180237, -0.11123867332935333], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1287200, "componentType": 5126, "count": 95, "max": [0.4433966279029846, 0.8215640783309937], "min": [0.291062593460083, 0.7213261723518372], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2360616, "componentType": 5125, "count": 438, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3863880, "componentType": 5126, "count": 800, "max": [0.34637096524238586, -0.26496729254722595, 1.4770230054855347], "min": [0.05489174276590347, -0.3713395297527313, 1.1855438947677612], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3873480, "componentType": 5126, "count": 800, "max": [0.7855133414268494, 0.6189160346984863, 0.785556972026825], "min": [-0.7855097055435181, -0.6189181208610535, -0.7855219841003418], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1287960, "componentType": 5126, "count": 800, "max": [1.0000001192092896, 1.0000001192092896], "min": [0.0, 0.5000000596046448], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2362368, "componentType": 5125, "count": 3072, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3883080, "componentType": 5126, "count": 325, "max": [0.34637096524238586, -0.2711918354034424, 1.4770230054855347], "min": [0.05489174276590347, -0.3651150166988373, 1.1855437755584717], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3886980, "componentType": 5126, "count": 325, "max": [1.0, 1.104175862565171e-05, 1.0], "min": [-1.0, -9.622487596061546e-06, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1294360, "componentType": 5126, "count": 325, "max": [1.0000001192092896, 0.970741868019104], "min": [0.0, 0.5292582511901855], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2374656, "componentType": 5125, "count": 1536, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3890880, "componentType": 5126, "count": 138, "max": [0.34146690368652344, -0.2649672329425812, 1.4721189737319946], "min": [0.05979570001363754, -0.26496732234954834, 1.1904476881027222], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3892536, "componentType": 5126, "count": 138, "max": [2.078760189760942e-06, 1.0, 1.0982678304571891e-06], "min": [-1.2022353530483088e-06, 1.0, -8.706931566848652e-07], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1296960, "componentType": 5126, "count": 138, "max": [0.4819243550300598, 0.4819244146347046], "min": [0.01807573065161705, 0.018075725063681602], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2380800, "componentType": 5125, "count": 384, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3894192, "componentType": 5126, "count": 130, "max": [0.3414669632911682, -0.37133944034576416, 1.4721192121505737], "min": [0.05979575961828232, -0.3713395595550537, 1.1904479265213013], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3895752, "componentType": 5126, "count": 130, "max": [2.274929101986345e-06, -1.0, 9.522190680399945e-07], "min": [-2.078768829960609e-06, -1.0, -1.3487705246006954e-06], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1298064, "componentType": 5126, "count": 130, "max": [0.981924295425415, 0.4819243848323822], "min": [0.5180758237838745, 0.0180757287889719], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2382336, "componentType": 5125, "count": 384, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3897312, "componentType": 5126, "count": 774, "max": [0.38568413257598877, 0.4319302439689636, 1.4517637491226196], "min": [-0.003297913121059537, 0.2771674692630768, 1.062781572341919], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3906600, "componentType": 5126, "count": 774, "max": [0.830381453037262, 0.9275938868522644, 0.5548117756843567], "min": [-0.9993931651115417, -0.05102076381444931, -0.999595582485199], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1299104, "componentType": 5126, "count": 774, "max": [1.0000001192092896, 1.0000001192092896], "min": [0.0, 0.5], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2383872, "componentType": 5125, "count": 2400, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3915888, "componentType": 5126, "count": 633, "max": [0.39298301935195923, 0.4319302439689636, 1.4590626955032349], "min": [-8.979440463008359e-05, 0.28734251856803894, 1.1024620532989502], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3923484, "componentType": 5126, "count": 633, "max": [0.9982935190200806, 1.0, 0.9982935786247253], "min": [-0.8278157711029053, 0.05838878080248833, -0.551285445690155], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1305296, "componentType": 5126, "count": 633, "max": [0.8750001192092896, 0.9456445574760437], "min": [0.03341997042298317, 0.03341997042298317], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2393472, "componentType": 5125, "count": 2784, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3931080, "componentType": 5126, "count": 1172, "max": [0.39298301935195923, 0.4319302439689636, 1.4590626955032349], "min": [-0.003297913121059537, 0.2771674692630768, 1.062781572341919], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3945144, "componentType": 5126, "count": 1172, "max": [0.7093365788459778, 0.9064236283302307, 0.7604781985282898], "min": [-0.9821146130561829, -0.9859617948532104, -0.9905953407287598], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1310360, "componentType": 5126, "count": 1172, "max": [1.0000001192092896, 0.9456445574760437], "min": [0.0, 0.06069684401154518], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2404608, "componentType": 5125, "count": 3264, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3959208, "componentType": 5126, "count": 193, "max": [0.29022541642189026, -0.11874586343765259, 1.4208775758743286], "min": [0.11103734374046326, -0.3101593554019928, 1.2416892051696777], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3961524, "componentType": 5126, "count": 193, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1319736, "componentType": 5126, "count": 193, "max": [1.0000001192092896, 1.0000001192092896], "min": [0.0, 0.010000006295740604], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2417664, "componentType": 5125, "count": 576, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3963840, "componentType": 5126, "count": 664, "max": [0.34926286339759827, 0.3711158335208893, 1.4799145460128784], "min": [0.052000246942043304, 0.0714784562587738, 1.1826517581939697], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3971808, "componentType": 5126, "count": 664, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1321280, "componentType": 5126, "count": 664, "max": [1.0000001192092896, 1.0000001192092896], "min": [0.0, 0.010000006295740604], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2419968, "componentType": 5125, "count": 2304, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3979776, "componentType": 5126, "count": 325, "max": [0.3428000807762146, 0.26297658681869507, 1.4734517335891724], "min": [0.058462925255298615, 0.25853973627090454, 1.1891144514083862], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3983676, "componentType": 5126, "count": 325, "max": [1.0, 0.00012051566591253504, 1.0], "min": [-1.0, -0.0001205167209263891, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1326592, "componentType": 5126, "count": 325, "max": [1.0000001192092896, 1.0000001192092896], "min": [0.0, 0.9899677038192749], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2429184, "componentType": 5125, "count": 1536, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3987576, "componentType": 5126, "count": 325, "max": [0.34926289319992065, 0.25665974617004395, 1.4799144268035889], "min": [0.0520002618432045, 0.0714784562587738, 1.1826518774032593], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3991476, "componentType": 5126, "count": 325, "max": [1.0, 9.683933967608027e-06, 1.0], "min": [-1.0, -6.918638973729685e-06, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1329192, "componentType": 5126, "count": 325, "max": [1.0000001192092896, 0.9699033498764038], "min": [0.0, 0.5], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2435328, "componentType": 5125, "count": 1536, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3995376, "componentType": 5126, "count": 969, "max": [0.3492627441883087, 0.26485675573349, 1.4799144268035889], "min": [0.052000246942043304, 0.2566596269607544, 1.1826517581939697], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4007004, "componentType": 5126, "count": 969, "max": [0.2793525755405426, 0.960202157497406, 0.27935782074928284], "min": [-0.27936261892318726, -0.960196852684021, -0.2793620228767395], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1331792, "componentType": 5126, "count": 969, "max": [1.0000001192092896, 1.0000001192092896], "min": [0.0, 0.9699032306671143], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2441472, "componentType": 5125, "count": 3072, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4018632, "componentType": 5126, "count": 6185, "max": [0.17946821451187134, -1.20425546169281, -0.011965911835432053], "min": [-0.17946821451187134, -1.5550223588943481, -0.36930447816848755], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4092852, "componentType": 5126, "count": 6185, "max": [1.0, 0.999861478805542, 0.9999998807907104], "min": [-1.0, -0.9999992251396179, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1339544, "componentType": 5126, "count": 6185, "max": [0.7500001788139343, 1.0000001192092896], "min": [0.5, 0.4999999701976776], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2453760, "componentType": 5125, "count": 23808, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4167072, "componentType": 5126, "count": 320, "max": [0.5005330443382263, -1.1794233322143555, 0.14767682552337646], "min": [-0.5005330443382263, -1.1794241666793823, 0.13102102279663086], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4170912, "componentType": 5126, "count": 320, "max": [0.00032393253059126437, -0.9999999403953552, 0.00018852968059945852], "min": [-0.00044169570901431143, -1.0, -0.0001917868066811934], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1389024, "componentType": 5126, "count": 320, "max": [0.5530656576156616, 1.0], "min": [0.5072826743125916, 0.9999997019767761], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2548992, "componentType": 5125, "count": 768, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4174752, "componentType": 5126, "count": 920, "max": [0.5822082161903381, -0.9912777543067932, -0.1686716377735138], "min": [-0.5822082161903381, -1.1638339757919312, -0.2129296362400055], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4185792, "componentType": 5126, "count": 920, "max": [0.9353998899459839, 0.8329599499702454, -0.13640019297599792], "min": [-0.9354033470153809, -0.9906538724899292, -0.5533772706985474], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1391584, "componentType": 5126, "count": 920, "max": [0.8644359707832336, 1.000000238418579], "min": [0.1990482658147812, 0.9999995827674866], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2552064, "componentType": 5125, "count": 3072, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4196832, "componentType": 5126, "count": 5449, "max": [0.6100005507469177, -0.9438672661781311, 0.25444087386131287], "min": [-0.6100005507469177, -1.1845427751541138, -0.20740145444869995], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4262220, "componentType": 5126, "count": 5449, "max": [0.9999092817306519, 0.9860448241233826, 1.0], "min": [-0.9999092221260071, -1.0, -0.9999349117279053], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1398944, "componentType": 5126, "count": 5449, "max": [1.0000001192092896, 1.000000238418579], "min": [0.0, 0.5], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2564352, "componentType": 5125, "count": 18528, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4327608, "componentType": 5126, "count": 880, "max": [0.5822082161903381, -1.1845381259918213, 0.227853462100029], "min": [-0.5822082161903381, -1.189935326576233, 0.05084390193223953], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4338168, "componentType": 5126, "count": 880, "max": [0.9353890419006348, -0.35350939631462097, 0.9353224635124207], "min": [-0.935390830039978, -0.3538283407688141, -0.9353363513946533], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1442536, "componentType": 5126, "count": 880, "max": [0.8644359707832336, 1.000000238418579], "min": [0.1990482658147812, 0.9999995827674866], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2638464, "componentType": 5125, "count": 3072, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4348728, "componentType": 5126, "count": 1978, "max": [0.5114525556564331, -1.0562363862991333, 0.15837036073207855], "min": [-0.5114525556564331, -1.1794239282608032, -0.18529824912548065], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4372464, "componentType": 5126, "count": 1978, "max": [0.17561423778533936, -0.04660944640636444, 0.1791394203901291], "min": [-0.17561736702919006, -0.9844662547111511, -0.9989132881164551], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1449576, "componentType": 5126, "count": 1978, "max": [0.5916170477867126, 1.0000001192092896], "min": [0.46980375051498413, 0.9999996423721313], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2650752, "componentType": 5125, "count": 6144, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4396200, "componentType": 5126, "count": 3569, "max": [0.6091691851615906, -0.9756014943122864, 0.25444087386131287], "min": [-0.6091691851615906, -1.1899222135543823, -0.21380367875099182], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4439028, "componentType": 5126, "count": 3569, "max": [0.714080810546875, 0.4751462936401367, 0.709542453289032], "min": [-0.7140820622444153, -1.0, -0.9748450517654419], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1465400, "componentType": 5126, "count": 3569, "max": [1.0000001192092896, 1.000000238418579], "min": [0.0, 0.5000000596046448], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2675328, "componentType": 5125, "count": 12288, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4481856, "componentType": 5126, "count": 5774, "max": [0.6100005507469177, -0.9438672661781311, 0.25444087386131287], "min": [-0.6100005507469177, -1.189935326576233, -0.21380367875099182], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4551144, "componentType": 5126, "count": 5774, "max": [0.7771432995796204, 0.999884307384491, 0.7834824323654175], "min": [-0.7771387100219727, -1.0, -0.974853515625], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1493952, "componentType": 5126, "count": 5774, "max": [0.9972140789031982, 1.000000238418579], "min": [0.01000000536441803, 0.010000006295740604], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2724480, "componentType": 5125, "count": 20064, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4620432, "componentType": 5126, "count": 340, "max": [0.5020901560783386, -1.0657554864883423, -0.18034455180168152], "min": [-0.5020901560783386, -1.08445405960083, -0.18462006747722626], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4624512, "componentType": 5126, "count": 340, "max": [0.07980761677026749, -0.15200185775756836, -0.9562137722969055], "min": [-0.07980920374393463, -0.2926690876483917, -0.9883803129196167], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1540144, "componentType": 5126, "count": 340, "max": [0.5582667589187622, 1.0000001192092896], "min": [0.5062916874885559, 0.9999997019767761], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2804736, "componentType": 5125, "count": 768, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4628592, "componentType": 5126, "count": 14321, "max": [1.87885582447052, 1.3883119821548462, 0.6606437563896179], "min": [-1.87885582447052, -0.9866906404495239, -0.5439174771308899], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4800444, "componentType": 5126, "count": 14321, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1542864, "componentType": 5126, "count": 14321, "max": [0.7500001788139343, 1.0000001192092896], "min": [0.01000000536441803, 0.01000000536441803], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2807808, "componentType": 5125, "count": 47424, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4972296, "componentType": 5126, "count": 440, "max": [1.8499711751937866, 1.3772664070129395, 0.6213191747665405], "min": [-1.8499711751937866, -0.8970513343811035, -0.5132400989532471], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4977576, "componentType": 5126, "count": 440, "max": [4.4793701817980036e-05, 1.0, 3.546848120095092e-06], "min": [-4.479371273191646e-05, 0.9914149045944214, -0.1307535320520401], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1657432, "componentType": 5126, "count": 440, "max": [0.4350035488605499, 0.2500000298023224], "min": [0.06499651819467545, 0.05714458227157593], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2997504, "componentType": 5125, "count": 1344, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4982856, "componentType": 5126, "count": 144, "max": [1.1447844505310059, 0.13731500506401062, 1.8022710084915161], "min": [0.4714103937149048, -1.255969762802124, -0.819887638092041], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4984584, "componentType": 5126, "count": 144, "max": [1.0, 1.0, 1.0], "min": [-1.0, -0.7840892672538757, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1660952, "componentType": 5126, "count": 144, "max": [0.7500000596046448, 1.0], "min": [0.5, 0.75], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3002880, "componentType": 5125, "count": 492, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4986312, "componentType": 5126, "count": 144, "max": [-0.464679092168808, 0.982433557510376, 1.6433048248291016], "min": [-1.2487692832946777, 0.42050912976264954, -0.8046900033950806], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4988040, "componentType": 5126, "count": 144, "max": [1.0, 1.0, 1.0], "min": [-1.0, -0.3819802701473236, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1662104, "componentType": 5126, "count": 144, "max": [0.7500000596046448, 1.0], "min": [0.5, 0.75], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3004848, "componentType": 5125, "count": 492, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4989768, "componentType": 5126, "count": 50, "max": [-0.238398477435112, -0.8493789434432983, 0.2301330417394638], "min": [-0.30768275260925293, -0.8493791818618774, -0.23001639544963837], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4990368, "componentType": 5126, "count": 50, "max": [3.144302809232613e-06, -1.0, -0.0], "min": [-3.5373186619835906e-06, -1.0, -1.947762939380482e-06], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1663256, "componentType": 5126, "count": 50, "max": [0.9084738492965698, 0.4140784442424774], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3006816, "componentType": 5125, "count": 132, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4990968, "componentType": 5126, "count": 100, "max": [-0.23780572414398193, -0.8461683988571167, 0.23072578012943268], "min": [-0.3082754909992218, -0.8493791818618774, -0.2306179255247116], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4992168, "componentType": 5126, "count": 100, "max": [0.6991091370582581, -0.707221269607544, 0.6964635252952576], "min": [-0.6991053819656372, -1.0, -0.6964687705039978], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1663656, "componentType": 5126, "count": 100, "max": [0.9294602870941162, 0.4294602870941162], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3007344, "componentType": 5125, "count": 216, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4993368, "componentType": 5126, "count": 170, "max": [-0.2377851903438568, -0.8438032865524292, 0.2307463139295578], "min": [-0.3082960247993469, -0.8493791818618774, -0.23063838481903076], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4995408, "componentType": 5126, "count": 170, "max": [0.9999436736106873, 0.8096444606781006, 0.9999435544013977], "min": [-0.9999436140060425, -0.4910487234592438, -0.9999436736106873], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1664456, "componentType": 5126, "count": 170, "max": [0.9294635057449341, 0.4294636845588684], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3008208, "componentType": 5125, "count": 456, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4997448, "componentType": 5126, "count": 44, "max": [0.2540666460990906, -0.5786599516868591, -0.7662971615791321], "min": [-0.2541162371635437, -0.5786603689193726, -0.805307149887085], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4997976, "componentType": 5126, "count": 44, "max": [1.0685829693102278e-05, -1.0, 2.208911382695078e-06], "min": [-1.0685744200600311e-05, -1.0, -3.960262802138459e-06], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1665816, "componentType": 5126, "count": 44, "max": [0.929459273815155, 0.42945924401283264], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3010032, "componentType": 5125, "count": 132, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4998504, "componentType": 5126, "count": 90, "max": [0.25684869289398193, -0.5588432550430298, -0.7635079622268677], "min": [-0.2569054365158081, -0.5786603689193726, -0.8080963492393494], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4999584, "componentType": 5126, "count": 90, "max": [0.9881497621536255, -0.13965699076652527, 0.9881502389907837], "min": [-0.9881500601768494, -1.0, -0.9881494641304016], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1666168, "componentType": 5126, "count": 90, "max": [0.929459273815155, 0.42945924401283264], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3010560, "componentType": 5125, "count": 216, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5000664, "componentType": 5126, "count": 164, "max": [0.25684869289398193, -0.5287454128265381, -0.7635079622268677], "min": [-0.2569054365158081, -0.5786603689193726, -0.8080963492393494], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5002632, "componentType": 5126, "count": 164, "max": [0.9979298114776611, 0.9319063425064087, 0.9979299902915955], "min": [-0.997930109500885, -0.06529717892408371, -0.9979298114776611], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1666888, "componentType": 5126, "count": 164, "max": [0.9294635057449341, 0.4294636845588684], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3011424, "componentType": 5125, "count": 456, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5004600, "componentType": 5126, "count": 17314, "max": [0.5295620560646057, 0.8660556674003601, 1.504321575164795], "min": [-0.5295620560646057, -0.5887575745582581, -0.2102961242198944], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5212368, "componentType": 5126, "count": 17314, "max": [1.0, 0.9999890327453613, 0.9800975918769836], "min": [-1.0, -0.9999892711639404, -0.9794436693191528], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1668200, "componentType": 5126, "count": 17314, "max": [6.466011047363281, 6.149102210998535], "min": [-6.074944496154785, -3.2677199840545654], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3013248, "componentType": 5125, "count": 79536, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5420136, "componentType": 5126, "count": 5727, "max": [0.5295620560646057, 0.7840511798858643, 1.504321575164795], "min": [-0.5295620560646057, -0.5887575745582581, -0.2102961242198944], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5488860, "componentType": 5126, "count": 5727, "max": [1.0, 0.8682708740234375, 0.9636866450309753], "min": [-1.0, -0.9998974204063416, -0.9990688562393188], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1806712, "componentType": 5126, "count": 5727, "max": [1.5884556770324707, 6.166193008422852], "min": [-6.101243019104004, -1.9229131937026978], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3331392, "componentType": 5125, "count": 23760, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5557584, "componentType": 5126, "count": 156, "max": [0.3826207220554352, 0.7822555303573608, -0.0014129877090454102], "min": [-0.3826207220554352, 0.5746871829032898, -0.07895141839981079], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5559456, "componentType": 5126, "count": 156, "max": [1.0, 0.948811411857605, 0.9488757848739624], "min": [-0.8900559544563293, -0.9488369822502136, -0.9488758444786072], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1852528, "componentType": 5126, "count": 156, "max": [1.0, 1.0], "min": [0.5, 0.9999998807907104], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3426432, "componentType": 5125, "count": 402, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5561328, "componentType": 5126, "count": 1536, "max": [-385.6997375488281, 41.07154083251953, 250.06832885742188], "min": [-631.5335693359375, -190.1927947998047, -249.84310913085938], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5579760, "componentType": 5126, "count": 1536, "max": [0.9998617172241211, 0.999889612197876, 1.0], "min": [-0.9998617172241211, -0.999889612197876, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1853776, "componentType": 5126, "count": 1536, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 1866064, "componentType": 5126, "count": 1536, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3428040, "componentType": 5125, "count": 6144, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5598192, "componentType": 5126, "count": 1828, "max": [-397.9040222167969, -7.1294450759887695, 248.6080780029297], "min": [-617.3953247070312, -143.1860809326172, -248.3828582763672], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5620128, "componentType": 5126, "count": 1828, "max": [0.9999653100967407, 0.999971866607666, 1.0], "min": [-0.9999653100967407, -0.9999721050262451, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1878352, "componentType": 5126, "count": 1828, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 1892976, "componentType": 5126, "count": 1828, "max": [1.000000238418579, 1.0000001192092896], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3452616, "componentType": 5125, "count": 6720, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5642064, "componentType": 5126, "count": 676, "max": [1090.8892822265625, -70.17217254638672, 267.4451904296875], "min": [876.1765747070312, -203.99050903320312, -315.6539001464844], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5650176, "componentType": 5126, "count": 676, "max": [0.9999653100967407, 0.999971866607666, 1.0], "min": [-0.9999653100967407, -0.9999721050262451, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1907600, "componentType": 5126, "count": 676, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 1913008, "componentType": 5126, "count": 676, "max": [1.0000001192092896, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3479496, "componentType": 5125, "count": 1872, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5658288, "componentType": 5126, "count": 395, "max": [0.09248044341802597, 1.5278010368347168, -0.09825345873832703], "min": [-0.09247905761003494, 1.4064666032791138, -0.28321295976638794], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5663028, "componentType": 5126, "count": 395, "max": [1.0, 1.0, 1.0], "min": [-1.0, -0.5352977514266968, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1918416, "componentType": 5126, "count": 395, "max": [1.000000238418579, 1.0], "min": [0.0, 0.31249988079071045], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3486984, "componentType": 5125, "count": 2016, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5667768, "componentType": 5126, "count": 32812, "max": [2.096349000930786, -0.35526299476623535, 1.5266523361206055], "min": [-2.253756284713745, -2.4604527950286865, -1.2053239345550537], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6061512, "componentType": 5126, "count": 32812, "max": [1.0, 0.9997977614402771, 0.9935302138328552], "min": [-1.0, -0.9999618530273438, -0.9483799338340759], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1921576, "componentType": 5126, "count": 32812, "max": [0.7038602232933044, 0.7500001192092896], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3495048, "componentType": 5125, "count": 157248, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6455256, "componentType": 5126, "count": 8190, "max": [2.560208559036255, -0.1916467845439911, 1.7196215391159058], "min": [-2.717616081237793, -2.1273398399353027, -1.64577054977417], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6553536, "componentType": 5126, "count": 8190, "max": [0.5736501216888428, 0.9573420882225037, 0.8210607767105103], "min": [-0.573650598526001, -0.9999969005584717, -0.7123679518699646], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2184072, "componentType": 5126, "count": 8190, "max": [3.320852279663086, 2.2619094848632812], "min": [-2.320852518081665, -1.5805690288543701], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4124040, "componentType": 5125, "count": 36288, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6651816, "componentType": 5126, "count": 17610, "max": [2.5624141693115234, -0.18094518780708313, 1.7196215391159058], "min": [-2.7198214530944824, -2.1273398399353027, -1.64577054977417], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6863136, "componentType": 5126, "count": 17610, "max": [0.9999969601631165, 0.9996975064277649, 0.9051451683044434], "min": [-0.9999969005584717, -0.9804173707962036, -0.9384332299232483], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2249592, "componentType": 5126, "count": 17610, "max": [0.8750001788139343, 0.7500001192092896], "min": [0.11671125888824463, 0.5197018980979919], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4269192, "componentType": 5125, "count": 80064, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7074456, "componentType": 5126, "count": 10140, "max": [2.5624141693115234, -0.1814027726650238, 1.5916392803192139], "min": [-2.7198214530944824, -2.2190840244293213, -1.5631974935531616], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7196136, "componentType": 5126, "count": 10140, "max": [1.0, 0.94627445936203, 0.9473301768302917], "min": [-1.0, -0.9462677836418152, -0.9473286271095276], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2390472, "componentType": 5126, "count": 10140, "max": [0.6250001192092896, 1.0000001192092896], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4589448, "componentType": 5125, "count": 47232, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7317816, "componentType": 5126, "count": 10164, "max": [2.600090265274048, -0.17400813102722168, 1.7358696460723877], "min": [-2.757497787475586, -2.1091837882995605, -1.6765766143798828], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7439784, "componentType": 5126, "count": 10164, "max": [0.9953600168228149, 0.9920690655708313, 0.8967196941375732], "min": [-0.9953579306602478, -0.9463069438934326, -0.9974063038825989], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2471592, "componentType": 5126, "count": 10164, "max": [0.8750001192092896, 0.5312657952308655], "min": [-0.03168555349111557, -0.02070649154484272], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4778376, "componentType": 5125, "count": 36864, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7561752, "componentType": 5126, "count": 960, "max": [1.9219505786895752, -0.6190919280052185, 1.3687827587127686], "min": [-2.079357624053955, -2.1975576877593994, -0.973396360874176], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7573272, "componentType": 5126, "count": 960, "max": [1.0, 0.3234061598777771, 0.9462661743164062], "min": [-1.0, -0.3234074115753174, -0.9462689757347107], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2552904, "componentType": 5126, "count": 960, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4925832, "componentType": 5125, "count": 4608, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7584792, "componentType": 5126, "count": 2246, "max": [0.2563652992248535, 0.7080384492874146, 0.04126880690455437], "min": [-0.25636526942253113, 0.6224617958068848, -0.5278577208518982], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7611744, "componentType": 5126, "count": 2246, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2560584, "componentType": 5126, "count": 2246, "max": [0.875, 1.0008485317230225], "min": [0.125, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4944264, "componentType": 5125, "count": 4344, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7638696, "componentType": 5126, "count": 8435, "max": [4.506366729736328, 0.08999999612569809, 0.004494382068514824], "min": [0.02097378298640251, -0.08999999612569809, -0.537827730178833], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7739916, "componentType": 5126, "count": 8435, "max": [0.9999996423721313, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2578552, "componentType": 5126, "count": 8435, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4961640, "componentType": 5125, "count": 24480, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7841136, "componentType": 5126, "count": 8435, "max": [4.506366729736328, 0.08999999612569809, 0.004494382068514824], "min": [0.02097378298640251, -0.08999999612569809, -0.537827730178833], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7942356, "componentType": 5126, "count": 8435, "max": [0.9999996423721313, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2646032, "componentType": 5126, "count": 8435, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5059560, "componentType": 5125, "count": 24480, "type": "SCALAR"}], "asset": {"extras": {"author": "SDC PERFORMANCE™️ (https://sketchfab.com/3Duae)", "license": "CC-BY-4.0 (http://creativecommons.org/licenses/by/4.0/)", "source": "https://sketchfab.com/3d-models/dji-fpv-by-sdc-high-performance-drone-d471ea8c6235457b8e131842e2cf3783", "title": "Dji FPV by SDC -  High performance drone"}, "generator": "Sketchfab-12.63.0", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 5157480, "name": "floatBufferViews", "target": 34963}, {"buffer": 0, "byteLength": 2713512, "byteOffset": 5157480, "byteStride": 8, "name": "floatBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 8043576, "byteOffset": 7870992, "byteStride": 12, "name": "floatBufferViews", "target": 34962}], "buffers": [{"byteLength": 15914568, "uri": "scene.bin"}], "images": [{"uri": "textures/CARBONE_baseColor.jpeg"}], "materials": [{"doubleSided": true, "name": "M_D1", "pbrMetallicRoughness": {"baseColorFactor": [0.09056349720795248, 0.10797513054326313, 0.11673372093150068, 1.0], "metallicFactor": 0.3361195726854301, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Plastic_D1", "pbrMetallicRoughness": {"baseColorFactor": [0.0528607, 0.0528607, 0.06301, 1.0], "metallicFactor": 0.6705290967, "roughnessFactor": 0.2874781874}}, {"doubleSided": true, "name": "Plastic_L1", "pbrMetallicRoughness": {"baseColorFactor": [0.198069, 0.198069, 0.219526, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Plastic_D2", "pbrMetallicRoughness": {"baseColorFactor": [0.005597636389621781, 0.00559758818524892, 0.006962543207152679, 1.0], "metallicFactor": 0.5732463261, "roughnessFactor": 0.0442712608}}, {"doubleSided": true, "name": "Metall_Basic", "pbrMetallicRoughness": {"baseColorFactor": [0.08524889999999997, 0.09078579999999994, 0.10247599999999997, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "emissiveFactor": [0.580416799126386, 0.5594340165711467, 1.0], "name": "Material.009", "pbrMetallicRoughness": {"baseColorFactor": [0.29701299999999997, 0.41837599999999986, 0.7999999999999999, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Material", "pbrMetallicRoughness": {"baseColorFactor": [0.39312, 0.39312, 0.39312, 1.0], "metallicFactor": 0.7252506551386584, "roughnessFactor": 0.09899281931549411}}, {"alphaMode": "BLEND", "doubleSided": true, "name": "Material.007", "pbrMetallicRoughness": {"baseColorFactor": [0.3047519995, 0.2556544458, 0.5190816594, 0.3057187069], "roughnessFactor": 0.1962755899}}, {"doubleSided": true, "name": "Grey_L1", "pbrMetallicRoughness": {"baseColorFactor": [0.8, 0.8, 0.8, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Grey_Basic", "pbrMetallicRoughness": {"baseColorFactor": [0.283149, 0.262251, 0.238398, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Material.005", "pbrMetallicRoughness": {"baseColorFactor": [0.4723073204, 0.4723073204, 0.4723073204, 1.0], "metallicFactor": 0.7009299625, "roughnessFactor": 0.2692376679}}, {"doubleSided": true, "name": "Grey_D2", "pbrMetallicRoughness": {"baseColorFactor": [0.028426, 0.0241576, 0.0202886, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Rubber_L1", "pbrMetallicRoughness": {"baseColorFactor": [0.049994881, 0.049994881, 0.049994881, 1.0], "metallicFactor": 0.5124445944, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Rubber_Basic", "pbrMetallicRoughness": {"baseColorFactor": [0.0908417, 0.102242, 0.107023, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Rubber_D1", "pbrMetallicRoughness": {"baseColorFactor": [0.043735, 0.0497066, 0.0528607, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Plastic_L2", "pbrMetallicRoughness": {"baseColorFactor": [0.341914, 0.341914, 0.366253, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Plastic_Base", "pbrMetallicRoughness": {"baseColorFactor": [0.111932, 0.111932, 0.127438, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "emissiveFactor": [1.0, 0.0, 0.0], "name": "Material.004", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 0.0, 0.0, 1.0]}}, {"alphaMode": "BLEND", "doubleSided": true, "name": "Material.013", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 0.1719548973], "roughnessFactor": 0.1780350704}}, {"doubleSided": true, "name": "M_L1", "pbrMetallicRoughness": {"baseColorFactor": [0.12256760431052603, 0.12256760431052603, 0.12256760431052603, 1.0], "metallicFactor": 0.652288577178678, "roughnessFactor": 0.15371437778547933}}, {"doubleSided": true, "name": "Grey_D1", "pbrMetallicRoughness": {"baseColorFactor": [0.135633, 0.122139, 0.107023, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "CARBONE", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.6340480577, "roughnessFactor": 0.1476342046}}, {"doubleSided": true, "name": "Material.006", "pbrMetallicRoughness": {"baseColorFactor": [0.178865, 0.178865, 0.178865, 1.0], "metallicFactor": 0.5610859797, "roughnessFactor": 0.2084359363}}, {"doubleSided": true, "name": "Material.014", "pbrMetallicRoughness": {"baseColorFactor": [0.8, 0.8, 0.8, 1.0], "metallicFactor": 0.9076558500386092, "roughnessFactor": 0.09899281931549411}}, {"doubleSided": true, "name": "Metall_D2", "pbrMetallicRoughness": {"baseColorFactor": [0.0482593453, 0.0482593453, 0.0482593453, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Mettal_L1", "pbrMetallicRoughness": {"baseColorFactor": [0.799103, 0.814847, 0.846874, 1.0], "metallicFactor": 0.881657, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Metall_D1", "pbrMetallicRoughness": {"baseColorFactor": [0.08194962337652517, 0.09928060427695293, 0.13788420204419904, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Green_L1", "pbrMetallicRoughness": {"baseColorFactor": [0.0368894, 0.107023, 0.0221739, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "emissiveFactor": [0.549912162401044, 1.0, 0.268551888959096], "name": "Green_L2E", "pbrMetallicRoughness": {"baseColorFactor": [0.0908417, 0.215861, 0.06301, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "M_Basic", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.6340480577, "roughnessFactor": 0.2205962826}}, {"doubleSided": true, "name": "M_D2", "pbrMetallicRoughness": {"baseColorFactor": [0.00303527, 0.00303527, 0.00303527, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Red_L1", "pbrMetallicRoughness": {"baseColorFactor": [0.783538, 0.0865004, 0.109462, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Red_Basic", "pbrMetallicRoughness": {"baseColorFactor": [0.0, 0.237857, 0.637597, 1.0], "metallicFactor": 0.5671661529, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Red_D1", "pbrMetallicRoughness": {"baseColorFactor": [0.40724, 0.00749898, 0.0144438, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Material.016", "pbrMetallicRoughness": {"baseColorFactor": [0.0110112305, 0.0110112305, 0.0110112305, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Material.008", "pbrMetallicRoughness": {"baseColorFactor": [0.157437, 0.157437, 0.157437, 1.0], "metallicFactor": 0.7921325599, "roughnessFactor": 0.2813980142}}, {"doubleSided": true, "emissiveFactor": [1.0, 0.0, 0.0], "name": "Yellow_L1", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 0.0, 0.0, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "emissiveFactor": [0.0575966, 0.444978, 1.0], "name": "Material.011"}, {"doubleSided": true, "name": "Material.012", "pbrMetallicRoughness": {"baseColorFactor": [0.88293, 0.88293, 0.88293, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "emissiveFactor": [0.0, 0.12038344707002975, 1.0], "name": "material", "pbrMetallicRoughness": {"baseColorFactor": [0.0, 0.3863840971, 1.0, 1.0]}}, {"doubleSided": true, "name": "Material.001", "pbrMetallicRoughness": {"baseColorFactor": [0.043265861743718964, 0.043265861743718964, 0.043265861743718964, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}}, {"doubleSided": true, "emissiveFactor": [0.028192818140217363, 0.26374906622959715, 0.6963194645530137], "name": "Material.002", "pbrMetallicRoughness": {"baseColorFactor": [0.09855021860221647, 0.18570196268400835, 0.42197790782417816, 1.0], "metallicFactor": 0.6097273650353562, "roughnessFactor": 0.29355836054210827}}, {"doubleSided": true, "name": "Rubber_Tire.002", "pbrMetallicRoughness": {"baseColorFactor": [0.0263830804, 0.0263830804, 0.0263830804, 1.0], "metallicFactor": 0.5124445944, "roughnessFactor": 0.5}}, {"doubleSided": true, "emissiveFactor": [0.04193137004585174, 0.7225556468922821, 1.0], "name": "Material.010"}, {"doubleSided": true, "emissiveFactor": [0.30872784505345513, 0.38133800105351157, 0.4987199530935721], "name": "Material.015", "pbrMetallicRoughness": {"baseColorFactor": [0.1974868437, 0.418842568, 0.5757194014, 1.0], "metallicFactor": 0.47929, "roughnessFactor": 0.357988}}], "meshes": [{"name": "Object_0", "primitives": [{"attributes": {"NORMAL": 1, "POSITION": 0, "TEXCOORD_0": 2}, "indices": 3, "material": 0, "mode": 4}]}, {"name": "Object_1", "primitives": [{"attributes": {"NORMAL": 5, "POSITION": 4, "TEXCOORD_0": 6}, "indices": 7, "material": 1, "mode": 4}]}, {"name": "Object_2", "primitives": [{"attributes": {"NORMAL": 9, "POSITION": 8, "TEXCOORD_0": 10}, "indices": 11, "material": 2, "mode": 4}]}, {"name": "Object_3", "primitives": [{"attributes": {"NORMAL": 13, "POSITION": 12, "TEXCOORD_0": 14}, "indices": 15, "material": 3, "mode": 4}]}, {"name": "Object_4", "primitives": [{"attributes": {"NORMAL": 17, "POSITION": 16, "TEXCOORD_0": 18}, "indices": 19, "material": 4, "mode": 4}]}, {"name": "Object_5", "primitives": [{"attributes": {"NORMAL": 21, "POSITION": 20, "TEXCOORD_0": 22}, "indices": 23, "material": 5, "mode": 4}]}, {"name": "Object_6", "primitives": [{"attributes": {"NORMAL": 25, "POSITION": 24, "TEXCOORD_0": 26}, "indices": 27, "material": 6, "mode": 4}]}, {"name": "Object_7", "primitives": [{"attributes": {"NORMAL": 29, "POSITION": 28, "TEXCOORD_0": 30}, "indices": 31, "material": 7, "mode": 4}]}, {"name": "Object_8", "primitives": [{"attributes": {"NORMAL": 33, "POSITION": 32, "TEXCOORD_0": 34}, "indices": 35, "material": 8, "mode": 4}]}, {"name": "Object_9", "primitives": [{"attributes": {"NORMAL": 37, "POSITION": 36, "TEXCOORD_0": 38}, "indices": 39, "material": 9, "mode": 4}]}, {"name": "Object_10", "primitives": [{"attributes": {"NORMAL": 41, "POSITION": 40, "TEXCOORD_0": 42}, "indices": 43, "material": 10, "mode": 4}]}, {"name": "Object_11", "primitives": [{"attributes": {"NORMAL": 45, "POSITION": 44, "TEXCOORD_0": 46}, "indices": 47, "material": 11, "mode": 4}]}, {"name": "Object_12", "primitives": [{"attributes": {"NORMAL": 49, "POSITION": 48, "TEXCOORD_0": 50}, "indices": 51, "material": 1, "mode": 4}]}, {"name": "Object_13", "primitives": [{"attributes": {"NORMAL": 53, "POSITION": 52, "TEXCOORD_0": 54}, "indices": 55, "material": 12, "mode": 4}]}, {"name": "Object_14", "primitives": [{"attributes": {"NORMAL": 57, "POSITION": 56, "TEXCOORD_0": 58}, "indices": 59, "material": 13, "mode": 4}]}, {"name": "Object_15", "primitives": [{"attributes": {"NORMAL": 61, "POSITION": 60, "TEXCOORD_0": 62}, "indices": 63, "material": 14, "mode": 4}]}, {"name": "Object_16", "primitives": [{"attributes": {"NORMAL": 65, "POSITION": 64, "TEXCOORD_0": 66}, "indices": 67, "material": 1, "mode": 4}]}, {"name": "Object_17", "primitives": [{"attributes": {"NORMAL": 69, "POSITION": 68, "TEXCOORD_0": 70}, "indices": 71, "material": 13, "mode": 4}]}, {"name": "Object_18", "primitives": [{"attributes": {"NORMAL": 73, "POSITION": 72, "TEXCOORD_0": 74}, "indices": 75, "material": 3, "mode": 4}]}, {"name": "Object_19", "primitives": [{"attributes": {"NORMAL": 77, "POSITION": 76, "TEXCOORD_0": 78}, "indices": 79, "material": 11, "mode": 4}]}, {"name": "Object_20", "primitives": [{"attributes": {"NORMAL": 81, "POSITION": 80, "TEXCOORD_0": 82}, "indices": 83, "material": 1, "mode": 4}]}, {"name": "Object_21", "primitives": [{"attributes": {"NORMAL": 85, "POSITION": 84, "TEXCOORD_0": 86}, "indices": 87, "material": 13, "mode": 4}]}, {"name": "Object_22", "primitives": [{"attributes": {"NORMAL": 89, "POSITION": 88, "TEXCOORD_0": 90}, "indices": 91, "material": 15, "mode": 4}]}, {"name": "Object_23", "primitives": [{"attributes": {"NORMAL": 93, "POSITION": 92, "TEXCOORD_0": 94}, "indices": 95, "material": 2, "mode": 4}]}, {"name": "Object_24", "primitives": [{"attributes": {"NORMAL": 97, "POSITION": 96, "TEXCOORD_0": 98}, "indices": 99, "material": 16, "mode": 4}]}, {"name": "Object_25", "primitives": [{"attributes": {"NORMAL": 101, "POSITION": 100, "TEXCOORD_0": 102}, "indices": 103, "material": 3, "mode": 4}]}, {"name": "Object_26", "primitives": [{"attributes": {"NORMAL": 105, "POSITION": 104, "TEXCOORD_0": 106}, "indices": 107, "material": 17, "mode": 4}]}, {"name": "Object_27", "primitives": [{"attributes": {"NORMAL": 109, "POSITION": 108, "TEXCOORD_0": 110}, "indices": 111, "material": 18, "mode": 4}]}, {"name": "Object_28", "primitives": [{"attributes": {"NORMAL": 113, "POSITION": 112, "TEXCOORD_0": 114}, "indices": 115, "material": 19, "mode": 4}]}, {"name": "Object_29", "primitives": [{"attributes": {"NORMAL": 117, "POSITION": 116, "TEXCOORD_0": 118}, "indices": 119, "material": 20, "mode": 4}]}, {"name": "Object_30", "primitives": [{"attributes": {"NORMAL": 121, "POSITION": 120, "TEXCOORD_0": 122}, "indices": 123, "material": 16, "mode": 4}]}, {"name": "Object_31", "primitives": [{"attributes": {"NORMAL": 125, "POSITION": 124, "TEXCOORD_0": 126}, "indices": 127, "material": 8, "mode": 4}]}, {"name": "Object_32", "primitives": [{"attributes": {"NORMAL": 129, "POSITION": 128, "TEXCOORD_0": 130}, "indices": 131, "material": 9, "mode": 4}]}, {"name": "Object_33", "primitives": [{"attributes": {"NORMAL": 133, "POSITION": 132, "TEXCOORD_0": 134}, "indices": 135, "material": 10, "mode": 4}]}, {"name": "Object_34", "primitives": [{"attributes": {"NORMAL": 137, "POSITION": 136, "TEXCOORD_0": 138}, "indices": 139, "material": 11, "mode": 4}]}, {"name": "Object_35", "primitives": [{"attributes": {"NORMAL": 141, "POSITION": 140, "TEXCOORD_0": 142}, "indices": 143, "material": 20, "mode": 4}]}, {"name": "Object_36", "primitives": [{"attributes": {"NORMAL": 145, "POSITION": 144, "TEXCOORD_0": 146}, "indices": 147, "material": 21, "mode": 4}]}, {"name": "Object_37", "primitives": [{"attributes": {"NORMAL": 149, "POSITION": 148, "TEXCOORD_0": 150}, "indices": 151, "material": 22, "mode": 4}]}, {"name": "Object_38", "primitives": [{"attributes": {"NORMAL": 153, "POSITION": 152, "TEXCOORD_0": 154}, "indices": 155, "material": 23, "mode": 4}]}, {"name": "Object_39", "primitives": [{"attributes": {"NORMAL": 157, "POSITION": 156, "TEXCOORD_0": 158}, "indices": 159, "material": 1, "mode": 4}]}, {"name": "Object_40", "primitives": [{"attributes": {"NORMAL": 161, "POSITION": 160, "TEXCOORD_0": 162}, "indices": 163, "material": 2, "mode": 4}]}, {"name": "Object_41", "primitives": [{"attributes": {"NORMAL": 165, "POSITION": 164, "TEXCOORD_0": 166}, "indices": 167, "material": 16, "mode": 4}]}, {"name": "Object_42", "primitives": [{"attributes": {"NORMAL": 169, "POSITION": 168, "TEXCOORD_0": 170}, "indices": 171, "material": 1, "mode": 4}]}, {"name": "Object_43", "primitives": [{"attributes": {"NORMAL": 173, "POSITION": 172, "TEXCOORD_0": 174}, "indices": 175, "material": 12, "mode": 4}]}, {"name": "Object_44", "primitives": [{"attributes": {"NORMAL": 177, "POSITION": 176, "TEXCOORD_0": 178}, "indices": 179, "material": 2, "mode": 4}]}, {"name": "Object_45", "primitives": [{"attributes": {"NORMAL": 181, "POSITION": 180, "TEXCOORD_0": 182}, "indices": 183, "material": 0, "mode": 4}]}, {"name": "Object_46", "primitives": [{"attributes": {"NORMAL": 185, "POSITION": 184, "TEXCOORD_0": 186}, "indices": 187, "material": 1, "mode": 4}]}, {"name": "Object_47", "primitives": [{"attributes": {"NORMAL": 189, "POSITION": 188, "TEXCOORD_0": 190}, "indices": 191, "material": 16, "mode": 4}]}, {"name": "Object_48", "primitives": [{"attributes": {"NORMAL": 193, "POSITION": 192, "TEXCOORD_0": 194}, "indices": 195, "material": 24, "mode": 4}]}, {"name": "Object_49", "primitives": [{"attributes": {"NORMAL": 197, "POSITION": 196, "TEXCOORD_0": 198}, "indices": 199, "material": 25, "mode": 4}]}, {"name": "Object_50", "primitives": [{"attributes": {"NORMAL": 201, "POSITION": 200, "TEXCOORD_0": 202}, "indices": 203, "material": 4, "mode": 4}]}, {"name": "Object_51", "primitives": [{"attributes": {"NORMAL": 205, "POSITION": 204, "TEXCOORD_0": 206}, "indices": 207, "material": 26, "mode": 4}]}, {"name": "Object_52", "primitives": [{"attributes": {"NORMAL": 209, "POSITION": 208, "TEXCOORD_0": 210}, "indices": 211, "material": 1, "mode": 4}]}, {"name": "Object_53", "primitives": [{"attributes": {"NORMAL": 213, "POSITION": 212, "TEXCOORD_0": 214}, "indices": 215, "material": 2, "mode": 4}]}, {"name": "Object_54", "primitives": [{"attributes": {"NORMAL": 217, "POSITION": 216, "TEXCOORD_0": 218}, "indices": 219, "material": 16, "mode": 4}]}, {"name": "Object_55", "primitives": [{"attributes": {"NORMAL": 221, "POSITION": 220, "TEXCOORD_0": 222}, "indices": 223, "material": 24, "mode": 4}]}, {"name": "Object_56", "primitives": [{"attributes": {"NORMAL": 225, "POSITION": 224, "TEXCOORD_0": 226}, "indices": 227, "material": 20, "mode": 4}]}, {"name": "Object_57", "primitives": [{"attributes": {"NORMAL": 229, "POSITION": 228, "TEXCOORD_0": 230}, "indices": 231, "material": 11, "mode": 4}]}, {"name": "Object_58", "primitives": [{"attributes": {"NORMAL": 233, "POSITION": 232, "TEXCOORD_0": 234}, "indices": 235, "material": 9, "mode": 4}]}, {"name": "Object_59", "primitives": [{"attributes": {"NORMAL": 237, "POSITION": 236, "TEXCOORD_0": 238}, "indices": 239, "material": 20, "mode": 4}]}, {"name": "Object_60", "primitives": [{"attributes": {"NORMAL": 241, "POSITION": 240, "TEXCOORD_0": 242}, "indices": 243, "material": 11, "mode": 4}]}, {"name": "Object_61", "primitives": [{"attributes": {"NORMAL": 245, "POSITION": 244, "TEXCOORD_0": 246}, "indices": 247, "material": 27, "mode": 4}]}, {"name": "Object_62", "primitives": [{"attributes": {"NORMAL": 249, "POSITION": 248, "TEXCOORD_0": 250}, "indices": 251, "material": 28, "mode": 4}]}, {"name": "Object_63", "primitives": [{"attributes": {"NORMAL": 253, "POSITION": 252, "TEXCOORD_0": 254}, "indices": 255, "material": 13, "mode": 4}]}, {"name": "Object_64", "primitives": [{"attributes": {"NORMAL": 257, "POSITION": 256, "TEXCOORD_0": 258}, "indices": 259, "material": 14, "mode": 4}]}, {"name": "Object_65", "primitives": [{"attributes": {"NORMAL": 261, "POSITION": 260, "TEXCOORD_0": 262}, "indices": 263, "material": 15, "mode": 4}]}, {"name": "Object_66", "primitives": [{"attributes": {"NORMAL": 265, "POSITION": 264, "TEXCOORD_0": 266}, "indices": 267, "material": 29, "mode": 4}]}, {"name": "Object_67", "primitives": [{"attributes": {"NORMAL": 269, "POSITION": 268, "TEXCOORD_0": 270}, "indices": 271, "material": 0, "mode": 4}]}, {"name": "Object_68", "primitives": [{"attributes": {"NORMAL": 273, "POSITION": 272, "TEXCOORD_0": 274}, "indices": 275, "material": 30, "mode": 4}]}, {"name": "Object_69", "primitives": [{"attributes": {"NORMAL": 277, "POSITION": 276, "TEXCOORD_0": 278}, "indices": 279, "material": 1, "mode": 4}]}, {"name": "Object_70", "primitives": [{"attributes": {"NORMAL": 281, "POSITION": 280, "TEXCOORD_0": 282}, "indices": 283, "material": 2, "mode": 4}]}, {"name": "Object_71", "primitives": [{"attributes": {"NORMAL": 285, "POSITION": 284, "TEXCOORD_0": 286}, "indices": 287, "material": 16, "mode": 4}]}, {"name": "Object_72", "primitives": [{"attributes": {"NORMAL": 289, "POSITION": 288, "TEXCOORD_0": 290}, "indices": 291, "material": 31, "mode": 4}]}, {"name": "Object_73", "primitives": [{"attributes": {"NORMAL": 293, "POSITION": 292, "TEXCOORD_0": 294}, "indices": 295, "material": 32, "mode": 4}]}, {"name": "Object_74", "primitives": [{"attributes": {"NORMAL": 297, "POSITION": 296, "TEXCOORD_0": 298}, "indices": 299, "material": 33, "mode": 4}]}, {"name": "Object_75", "primitives": [{"attributes": {"NORMAL": 301, "POSITION": 300, "TEXCOORD_0": 302}, "indices": 303, "material": 8, "mode": 4}]}, {"name": "Object_76", "primitives": [{"attributes": {"NORMAL": 305, "POSITION": 304, "TEXCOORD_0": 306}, "indices": 307, "material": 34, "mode": 4}]}, {"name": "Object_77", "primitives": [{"attributes": {"NORMAL": 309, "POSITION": 308, "TEXCOORD_0": 310}, "indices": 311, "material": 19, "mode": 4}]}, {"name": "Object_78", "primitives": [{"attributes": {"NORMAL": 313, "POSITION": 312, "TEXCOORD_0": 314}, "indices": 315, "material": 29, "mode": 4}]}, {"name": "Object_79", "primitives": [{"attributes": {"NORMAL": 317, "POSITION": 316, "TEXCOORD_0": 318}, "indices": 319, "material": 2, "mode": 4}]}, {"name": "Object_80", "primitives": [{"attributes": {"NORMAL": 321, "POSITION": 320, "TEXCOORD_0": 322}, "indices": 323, "material": 16, "mode": 4}]}, {"name": "Object_81", "primitives": [{"attributes": {"NORMAL": 325, "POSITION": 324, "TEXCOORD_0": 326}, "indices": 327, "material": 19, "mode": 4}]}, {"name": "Object_82", "primitives": [{"attributes": {"NORMAL": 329, "POSITION": 328, "TEXCOORD_0": 330}, "indices": 331, "material": 4, "mode": 4}]}, {"name": "Object_83", "primitives": [{"attributes": {"NORMAL": 333, "POSITION": 332, "TEXCOORD_0": 334}, "indices": 335, "material": 26, "mode": 4}]}, {"name": "Object_84", "primitives": [{"attributes": {"NORMAL": 337, "POSITION": 336, "TEXCOORD_0": 338}, "indices": 339, "material": 1, "mode": 4}]}, {"name": "Object_85", "primitives": [{"attributes": {"NORMAL": 341, "POSITION": 340, "TEXCOORD_0": 342}, "indices": 343, "material": 0, "mode": 4}]}, {"name": "Object_86", "primitives": [{"attributes": {"NORMAL": 345, "POSITION": 344, "TEXCOORD_0": 346}, "indices": 347, "material": 30, "mode": 4}]}, {"name": "Object_87", "primitives": [{"attributes": {"NORMAL": 349, "POSITION": 348, "TEXCOORD_0": 350}, "indices": 351, "material": 1, "mode": 4}]}, {"name": "Object_88", "primitives": [{"attributes": {"NORMAL": 353, "POSITION": 352, "TEXCOORD_0": 354}, "indices": 355, "material": 16, "mode": 4}]}, {"name": "Object_89", "primitives": [{"attributes": {"NORMAL": 357, "POSITION": 356, "TEXCOORD_0": 358}, "indices": 359, "material": 35, "mode": 4}]}, {"name": "Object_90", "primitives": [{"attributes": {"NORMAL": 361, "POSITION": 360, "TEXCOORD_0": 362}, "indices": 363, "material": 17, "mode": 4}]}, {"name": "Object_91", "primitives": [{"attributes": {"NORMAL": 365, "POSITION": 364, "TEXCOORD_0": 366}, "indices": 367, "material": 11, "mode": 4}]}, {"name": "Object_92", "primitives": [{"attributes": {"NORMAL": 369, "POSITION": 368, "TEXCOORD_0": 370}, "indices": 371, "material": 1, "mode": 4}]}, {"name": "Object_93", "primitives": [{"attributes": {"NORMAL": 373, "POSITION": 372, "TEXCOORD_0": 374}, "indices": 375, "material": 13, "mode": 4}]}, {"name": "Object_94", "primitives": [{"attributes": {"NORMAL": 377, "POSITION": 376, "TEXCOORD_0": 378}, "indices": 379, "material": 2, "mode": 4}]}, {"name": "Object_95", "primitives": [{"attributes": {"NORMAL": 381, "POSITION": 380, "TEXCOORD_0": 382}, "indices": 383, "material": 16, "mode": 4}]}, {"name": "Object_96", "primitives": [{"attributes": {"NORMAL": 385, "POSITION": 384, "TEXCOORD_0": 386}, "indices": 387, "material": 3, "mode": 4}]}, {"name": "Object_97", "primitives": [{"attributes": {"NORMAL": 389, "POSITION": 388, "TEXCOORD_0": 390}, "indices": 391, "material": 36, "mode": 4}]}, {"name": "Object_98", "primitives": [{"attributes": {"NORMAL": 393, "POSITION": 392, "TEXCOORD_0": 394}, "indices": 395, "material": 37, "mode": 4}]}, {"name": "Object_99", "primitives": [{"attributes": {"NORMAL": 397, "POSITION": 396, "TEXCOORD_0": 398}, "indices": 399, "material": 38, "mode": 4}]}, {"name": "Object_100", "primitives": [{"attributes": {"NORMAL": 401, "POSITION": 400, "TEXCOORD_0": 402}, "indices": 403, "material": 39, "mode": 4}]}, {"name": "Object_101", "primitives": [{"attributes": {"NORMAL": 405, "POSITION": 404, "TEXCOORD_0": 406}, "indices": 407, "material": 39, "mode": 4}]}, {"name": "Object_102", "primitives": [{"attributes": {"NORMAL": 409, "POSITION": 408, "TEXCOORD_0": 410}, "indices": 411, "material": 4, "mode": 4}]}, {"name": "Object_103", "primitives": [{"attributes": {"NORMAL": 413, "POSITION": 412, "TEXCOORD_0": 414}, "indices": 415, "material": 26, "mode": 4}]}, {"name": "Object_104", "primitives": [{"attributes": {"NORMAL": 417, "POSITION": 416, "TEXCOORD_0": 418}, "indices": 419, "material": 24, "mode": 4}]}, {"name": "Object_105", "primitives": [{"attributes": {"NORMAL": 421, "POSITION": 420, "TEXCOORD_0": 422}, "indices": 423, "material": 4, "mode": 4}]}, {"name": "Object_106", "primitives": [{"attributes": {"NORMAL": 425, "POSITION": 424, "TEXCOORD_0": 426}, "indices": 427, "material": 26, "mode": 4}]}, {"name": "Object_107", "primitives": [{"attributes": {"NORMAL": 429, "POSITION": 428, "TEXCOORD_0": 430}, "indices": 431, "material": 24, "mode": 4}]}, {"name": "Object_108", "primitives": [{"attributes": {"NORMAL": 433, "POSITION": 432, "TEXCOORD_0": 434}, "indices": 435, "material": 21, "mode": 4}]}, {"name": "Object_109", "primitives": [{"attributes": {"NORMAL": 437, "POSITION": 436, "TEXCOORD_0": 438}, "indices": 439, "material": 40, "mode": 4}]}, {"name": "Object_110", "primitives": [{"attributes": {"NORMAL": 441, "POSITION": 440, "TEXCOORD_0": 442}, "indices": 443, "material": 13, "mode": 4}]}, {"name": "Object_111", "primitives": [{"attributes": {"NORMAL": 445, "POSITION": 444, "TEXCOORD_0": 446, "TEXCOORD_1": 447}, "indices": 448, "material": 41, "mode": 4}]}, {"name": "Object_112", "primitives": [{"attributes": {"NORMAL": 450, "POSITION": 449, "TEXCOORD_0": 451, "TEXCOORD_1": 452}, "indices": 453, "material": 40, "mode": 4}]}, {"name": "Object_113", "primitives": [{"attributes": {"NORMAL": 455, "POSITION": 454, "TEXCOORD_0": 456, "TEXCOORD_1": 457}, "indices": 458, "material": 42, "mode": 4}]}, {"name": "Object_114", "primitives": [{"attributes": {"NORMAL": 460, "POSITION": 459, "TEXCOORD_0": 461}, "indices": 462, "material": 7, "mode": 4}]}, {"name": "Object_115", "primitives": [{"attributes": {"NORMAL": 464, "POSITION": 463, "TEXCOORD_0": 465}, "indices": 466, "material": 19, "mode": 4}]}, {"name": "Object_116", "primitives": [{"attributes": {"NORMAL": 468, "POSITION": 467, "TEXCOORD_0": 469}, "indices": 470, "material": 21, "mode": 4}]}, {"name": "Object_117", "primitives": [{"attributes": {"NORMAL": 472, "POSITION": 471, "TEXCOORD_0": 473}, "indices": 474, "material": 0, "mode": 4}]}, {"name": "Object_118", "primitives": [{"attributes": {"NORMAL": 476, "POSITION": 475, "TEXCOORD_0": 477}, "indices": 478, "material": 30, "mode": 4}]}, {"name": "Object_119", "primitives": [{"attributes": {"NORMAL": 480, "POSITION": 479, "TEXCOORD_0": 481}, "indices": 482, "material": 32, "mode": 4}]}, {"name": "Object_120", "primitives": [{"attributes": {"NORMAL": 484, "POSITION": 483, "TEXCOORD_0": 485}, "indices": 486, "material": 43, "mode": 4}]}, {"name": "Object_121", "primitives": [{"attributes": {"NORMAL": 488, "POSITION": 487, "TEXCOORD_0": 489}, "indices": 490, "material": 44, "mode": 4}]}, {"name": "Object_122", "primitives": [{"attributes": {"NORMAL": 492, "POSITION": 491, "TEXCOORD_0": 493}, "indices": 494, "material": 41, "mode": 4}]}, {"name": "Object_123", "primitives": [{"attributes": {"NORMAL": 496, "POSITION": 495, "TEXCOORD_0": 497}, "indices": 498, "material": 41, "mode": 4}]}], "nodes": [{"children": [1], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 2.220446049250313e-16, -1.0, 0.0, 0.0, 1.0, 2.220446049250313e-16, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Sketchfab_model"}, {"children": [2], "name": "root"}, {"children": [3, 170, 172], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 2.220446049250313e-16, 1.0, 0.0, 0.0, -1.0, 2.220446049250313e-16, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "GLTF_SceneRootNode"}, {"children": [4, 11, 13, 15, 21, 25, 29, 39, 43, 48, 51, 53, 55, 59, 63, 67, 69, 73, 76, 79, 82, 86, 89, 93, 100, 105, 107, 112, 116, 118, 123, 125, 134, 137, 139, 141, 145, 149, 152, 154, 157, 159, 161, 168], "matrix": [3.530942916870117, 0.0, -0.0, 0.0, -0.0, 3.139837775996422, -1.6152326837397435, 0.0, 0.0, 1.6152326837397435, 3.139837775996422, 0.0, 0.0, 1.0165696144104004, -0.13504227995872498, 1.0], "name": "Empty_46"}, {"children": [5, 6, 7, 8, 9, 10], "matrix": [0.28321051597595215, 0.0, -0.0, 0.0, -0.0, -7.161858248951537e-08, -0.2832105159759431, 0.0, 0.0, 0.2832105159759431, -7.161858248951537e-08, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "camera2.002_0"}, {"mesh": 0, "name": "Object_5"}, {"mesh": 1, "name": "Object_6"}, {"mesh": 2, "name": "Object_7"}, {"mesh": 3, "name": "Object_8"}, {"mesh": 4, "name": "Object_9"}, {"mesh": 5, "name": "Object_10"}, {"children": [12], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Circle_1"}, {"mesh": 6, "name": "Object_12"}, {"children": [14], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, -0.00919022411108017, -0.0017081722617149353, 1.0], "name": "Cube_2"}, {"mesh": 7, "name": "Object_14"}, {"children": [16, 17, 18, 19, 20], "matrix": [0.2823615236673997, 0.021399454418367676, 0.004713043764321333, 0.0, -0.02147682221674057, 0.2581883848533993, 0.11439267246186377, 0.0, 0.004346899860673436, -0.11440717091202338, 0.2590372235494584, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.178_3"}, {"mesh": 8, "name": "Object_16"}, {"mesh": 9, "name": "Object_17"}, {"mesh": 10, "name": "Object_18"}, {"mesh": 11, "name": "Object_19"}, {"mesh": 12, "name": "Object_20"}, {"children": [22, 23, 24], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.24832027978745883, 0.13618089075066003, 0.0, 0.0, -0.13618089075066003, 0.24832027978745883, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.179_4"}, {"mesh": 13, "name": "Object_22"}, {"mesh": 14, "name": "Object_23"}, {"mesh": 15, "name": "Object_24"}, {"children": [26, 27, 28], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.2554514708477468, 0.12228140660414331, 0.0, 0.0, -0.12228140660414331, 0.2554514708477468, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.180_5"}, {"mesh": 16, "name": "Object_26"}, {"mesh": 17, "name": "Object_27"}, {"mesh": 18, "name": "Object_28"}, {"children": [30, 31, 32, 33, 34, 35, 36, 37, 38], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.23016127727713856, 0.16502721836570233, 0.0, 0.0, -0.16502721836570233, 0.23016127727713856, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.181_6"}, {"mesh": 19, "name": "Object_30"}, {"mesh": 20, "name": "Object_31"}, {"mesh": 21, "name": "Object_32"}, {"mesh": 22, "name": "Object_33"}, {"mesh": 23, "name": "Object_34"}, {"mesh": 24, "name": "Object_35"}, {"mesh": 25, "name": "Object_36"}, {"mesh": 26, "name": "Object_37"}, {"mesh": 27, "name": "Object_38"}, {"children": [40, 41, 42], "matrix": [0.28321051597595215, 0.0, -0.0, 0.0, -0.0, 0.28321043744697616, -0.00010353326322794495, 0.0, 0.0, 0.00010353326322794495, 0.28321043744697616, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.182_7"}, {"mesh": 28, "name": "Object_40"}, {"mesh": 29, "name": "Object_41"}, {"mesh": 30, "name": "Object_42"}, {"children": [44, 45, 46, 47], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.183_8"}, {"mesh": 31, "name": "Object_44"}, {"mesh": 32, "name": "Object_45"}, {"mesh": 33, "name": "Object_46"}, {"mesh": 34, "name": "Object_47"}, {"children": [49, 50], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.184_9"}, {"mesh": 35, "name": "Object_49"}, {"mesh": 36, "name": "Object_50"}, {"children": [52], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.185_10"}, {"mesh": 37, "name": "Object_52"}, {"children": [54], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.12411276472926472, 0.254566692852702, 0.0, 0.0, -0.254566692852702, 0.12411276472926472, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.186_11"}, {"mesh": 38, "name": "Object_54"}, {"children": [56, 57, 58], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.187_12"}, {"mesh": 39, "name": "Object_56"}, {"mesh": 40, "name": "Object_57"}, {"mesh": 41, "name": "Object_58"}, {"children": [60, 61, 62], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.188_13"}, {"mesh": 42, "name": "Object_60"}, {"mesh": 43, "name": "Object_61"}, {"mesh": 44, "name": "Object_62"}, {"children": [64, 65, 66], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.189_14"}, {"mesh": 45, "name": "Object_64"}, {"mesh": 46, "name": "Object_65"}, {"mesh": 47, "name": "Object_66"}, {"children": [68], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.2264530184612744, 0.1700800599398494, 0.0, 0.0, -0.1700800599398494, 0.2264530184612744, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.194_15"}, {"mesh": 48, "name": "Object_68"}, {"children": [70, 71, 72], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.195_16"}, {"mesh": 49, "name": "Object_70"}, {"mesh": 50, "name": "Object_71"}, {"mesh": 51, "name": "Object_72"}, {"children": [74, 75], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.196_17"}, {"mesh": 52, "name": "Object_74"}, {"mesh": 53, "name": "Object_75"}, {"children": [77, 78], "matrix": [0.3159084618091583, 0.0, -0.0, 0.0, -0.0, -7.161858248951537e-08, -0.2832105159759431, 0.0, 0.0, 0.2832105159759431, -7.161858248951537e-08, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.197_18"}, {"mesh": 54, "name": "Object_77"}, {"mesh": 55, "name": "Object_78"}, {"children": [80, 81], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.198_19"}, {"mesh": 56, "name": "Object_80"}, {"mesh": 57, "name": "Object_81"}, {"children": [83, 84, 85], "matrix": [0.28321051597595215, 0.0, -0.0, 0.0, -0.0, 0.28321043744697616, -0.00010353326322794495, 0.0, 0.0, 0.00010353326322794495, 0.28321043744697616, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.199_20"}, {"mesh": 58, "name": "Object_83"}, {"mesh": 59, "name": "Object_84"}, {"mesh": 60, "name": "Object_85"}, {"children": [87, 88], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.200_21"}, {"mesh": 61, "name": "Object_87"}, {"mesh": 62, "name": "Object_88"}, {"children": [90, 91, 92], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.12411276472926472, 0.254566692852702, 0.0, 0.0, -0.254566692852702, 0.12411276472926472, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.201_22"}, {"mesh": 63, "name": "Object_90"}, {"mesh": 64, "name": "Object_91"}, {"mesh": 65, "name": "Object_92"}, {"children": [94, 95, 96, 97, 98, 99], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.2264530184612744, 0.1700800599398494, 0.0, 0.0, -0.1700800599398494, 0.2264530184612744, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.202_23"}, {"mesh": 66, "name": "Object_94"}, {"mesh": 67, "name": "Object_95"}, {"mesh": 68, "name": "Object_96"}, {"mesh": 69, "name": "Object_97"}, {"mesh": 70, "name": "Object_98"}, {"mesh": 71, "name": "Object_99"}, {"children": [101, 102, 103, 104], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.203_24"}, {"mesh": 72, "name": "Object_101"}, {"mesh": 73, "name": "Object_102"}, {"mesh": 74, "name": "Object_103"}, {"mesh": 75, "name": "Object_104"}, {"children": [106], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.204_25"}, {"mesh": 76, "name": "Object_106"}, {"children": [108, 109, 110, 111], "matrix": [9.09698617235979e-08, -0.2698874591425427, 9.861588992193583e-05, 0.0, 0.26988750696175917, 9.102865123050963e-08, 1.6084834106029139e-07, 0.0, -1.6088157409096402e-07, 9.861588986769623e-05, 0.26988745914251017, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cylinder.155_26"}, {"mesh": 77, "name": "Object_108"}, {"mesh": 78, "name": "Object_109"}, {"mesh": 79, "name": "Object_110"}, {"mesh": 80, "name": "Object_111"}, {"children": [113, 114, 115], "matrix": [9.545442937205059e-08, -0.2832104374796293, 0.00010344385943457501, 0.0, 0.2832105159758636, 9.552843890660743e-08, 2.0255217938736795e-07, 0.0, -2.0258701542009148e-07, 0.00010344385936627366, 0.283210437479573, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cylinder.156_27"}, {"mesh": 81, "name": "Object_113"}, {"mesh": 82, "name": "Object_114"}, {"mesh": 83, "name": "Object_115"}, {"children": [117], "matrix": [9.09698617235979e-08, -0.2698874591425427, 9.861588992193583e-05, 0.0, 0.26988750696175917, 9.102865123050963e-08, 1.6084834106029139e-07, 0.0, -1.6088157409096402e-07, 9.861588986769623e-05, 0.26988745914251017, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cylinder.157_28"}, {"mesh": 84, "name": "Object_117"}, {"children": [119, 120, 121, 122], "matrix": [9.09698617235979e-08, -0.2698874591425427, 9.861588992193583e-05, 0.0, 0.26988750696175917, 9.102865123050963e-08, 1.6084834106029139e-07, 0.0, -1.6088157409096402e-07, 9.861588986769623e-05, 0.26988745914251017, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cylinder.158_29"}, {"mesh": 85, "name": "Object_119"}, {"mesh": 86, "name": "Object_120"}, {"mesh": 87, "name": "Object_121"}, {"mesh": 88, "name": "Object_122"}, {"children": [124], "matrix": [0.28321051597595215, 0.0, -0.0, 0.0, -0.0, -7.161858248951537e-08, -0.2832105159759431, 0.0, 0.0, 0.2832105159759431, -7.161858248951537e-08, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cylinder.160_30"}, {"mesh": 89, "name": "Object_124"}, {"children": [126, 127, 128, 129, 130, 131, 132, 133], "matrix": [0.28321051597595215, 0.0, -0.0, 0.0, -0.0, 0.20303657584628065, -0.19744453709328746, 0.0, 0.0, 0.19744453709328746, 0.20303657584628065, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cylinder.164_31"}, {"mesh": 90, "name": "Object_126"}, {"mesh": 91, "name": "Object_127"}, {"mesh": 92, "name": "Object_128"}, {"mesh": 93, "name": "Object_129"}, {"mesh": 94, "name": "Object_130"}, {"mesh": 95, "name": "Object_131"}, {"mesh": 96, "name": "Object_132"}, {"mesh": 97, "name": "Object_133"}, {"children": [135, 136], "matrix": [0.28321051597595215, 0.0, -0.0, 0.0, -0.0, 0.06768515960153468, -0.27500345206636684, 0.0, 0.0, 0.27500345206636684, 0.06768515960153468, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cylinder.165_32"}, {"mesh": 98, "name": "Object_135"}, {"mesh": 99, "name": "Object_136"}, {"children": [138], "matrix": [-0.06110102050873355, -0.2743910633073048, 0.03441520055503117, 0.0, 0.12838289031849281, -0.059362292630749686, -0.24536126028054253, 0.0, 0.24493406281206828, -0.037334420446458495, 0.1371919905959014, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cylinder.174_33"}, {"mesh": 100, "name": "Object_138"}, {"children": [140], "matrix": [0.08290719374490545, -0.2596114440658559, 0.07704863201089768, 0.0, -0.0579075497603314, -0.09571062979336965, -0.26018141844492854, 0.0, 0.2645398052462621, 0.06041165356148652, -0.08110067763959616, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cylinder.175_34"}, {"mesh": 101, "name": "Object_140"}, {"children": [142, 143, 144], "matrix": [6.752264880560688e-08, -0.2832105159759441, -1.6098653985406608e-14, 0.0, 1.4901794997967954e-07, 1.7764323947391455e-14, 0.3125132918357494, 0.0, -0.2832105159759119, -6.752264880560688e-08, 1.3504529761121375e-07, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cylinder.176_35"}, {"mesh": 102, "name": "Object_142"}, {"mesh": 103, "name": "Object_143"}, {"mesh": 104, "name": "Object_144"}, {"children": [146, 147, 148], "matrix": [-0.28321051597586333, -6.150468033017682e-08, 2.1558456683591304e-07, 0.0, 6.150468033017682e-08, 0.24057836265805127, 0.14943308796960741, 0.0, -2.1558456683591304e-07, 0.14943308796960741, -0.24057836265796248, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cylinder.177_36"}, {"mesh": 105, "name": "Object_146"}, {"mesh": 106, "name": "Object_147"}, {"mesh": 107, "name": "Object_148"}, {"children": [150, 151], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "obt.001_37"}, {"mesh": 108, "name": "Object_150"}, {"mesh": 109, "name": "Object_151"}, {"children": [153], "matrix": [0.26658496260643005, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "obt.010_38"}, {"mesh": 110, "name": "Object_153"}, {"children": [155, 156], "matrix": [6.106130400692625e-11, 0.0, 0.0003621937648858823, 0.0, -0.0, 0.00038301118183881044, 0.0, 0.0, -0.00038301118183880497, -0.0, 6.457085814185691e-11, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "pneu ar-gauche 3.002_41"}, {"mesh": 111, "name": "Object_155"}, {"mesh": 112, "name": "Object_156"}, {"children": [158], "matrix": [6.106130400692625e-11, 0.0, 0.0003621937648858823, 0.0, -0.0, 0.00038301118183881044, 0.0, 0.0, -0.00038301118183880497, -0.0, 6.457085814185691e-11, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "pneu ar-gauche 3.004_42"}, {"mesh": 113, "name": "Object_158"}, {"children": [160], "matrix": [-0.28321051597593605, -6.752264880560496e-08, 6.752264880560496e-08, 0.0, 6.752264880560496e-08, 8.049326992703304e-15, 0.2832105159759441, 0.0, -6.752264880560496e-08, 0.2832105159759441, 8.049326992703304e-15, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Sphere.011_43"}, {"mesh": 114, "name": "Object_160"}, {"children": [162, 163, 164, 165, 166, 167], "matrix": [0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.0, 0.0, 0.0, 0.28321051597595215, 0.0, 0.01732175424695015, 0.4444533586502075, -0.0030976980924606323, 1.0], "name": "SpineB.005_44"}, {"mesh": 115, "name": "Object_162"}, {"mesh": 116, "name": "Object_163"}, {"mesh": 117, "name": "Object_164"}, {"mesh": 118, "name": "Object_165"}, {"mesh": 119, "name": "Object_166"}, {"mesh": 120, "name": "Object_167"}, {"children": [169], "matrix": [0.28321051597595215, 0.0, -0.0, 0.0, -0.0, 0.2814068731810561, -0.03191161552107226, 0.0, 0.0, 0.03191161552107226, 0.2814068731810561, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Spins.002_45"}, {"mesh": 121, "name": "Object_169"}, {"children": [171], "matrix": [-2.799075629866754e-17, -0.10992258018013107, -0.061708492890384395, 0.0, 0.12605914473533633, -2.799075298994509e-17, -3.498844123743136e-18, 0.0, -3.4988445373334425e-18, -0.061708492890384395, 0.10992258018013105, 0.0, 0.49030086398124695, 1.3728975057601929, 0.36969202756881714, 1.0], "name": "Text_47"}, {"mesh": 122, "name": "Object_171"}, {"children": [173], "matrix": [2.1471703838464173e-09, 0.10775811424942686, 0.06541483426308918, 0.0, -0.12605914473533605, -2.147170130034381e-09, 7.674795387736763e-09, 0.0, 7.67479629495665e-09, -0.06541483426308918, 0.10775811424942659, 0.0, -0.4933711290359497, 0.8664383888244629, 0.06426472961902618, 1.0], "name": "Text.001_48"}, {"mesh": 123, "name": "Object_173"}], "samplers": [{"magFilter": 9729, "minFilter": 9987, "wrapS": 10497, "wrapT": 10497}], "scene": 0, "scenes": [{"name": "Sketchfab_Scene", "nodes": [0]}], "textures": [{"sampler": 0, "source": 0}]}