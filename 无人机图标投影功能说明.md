# 无人机图标投影功能说明

## 功能概述
将飞机投影从简单的几何形状升级为使用真实的无人机图标，并实现了近大远小的透视效果，让投影更加逼真和直观。

## 主要特性

### 1. 真实无人机图标
- 使用 `img/drone1.png` 作为默认投影图标
- 支持动态切换不同的无人机图标（drone1.png, drone2.png）
- 保持图标的原始比例和细节

### 2. 近大远小透视效果
- **距离感模拟**: 飞机高度越高，投影越小
- **缩放范围**: 从 0.3倍（远处）到 1.5倍（近处）
- **平滑过渡**: 基于高度的连续缩放变化
- **真实感**: 模拟真实世界的透视效果

### 3. 智能加载机制
- **异步加载**: 图片纹理异步加载，不阻塞主程序
- **加载进度**: 显示图片加载进度
- **备用方案**: 图片加载失败时自动使用圆形投影
- **错误处理**: 完善的错误捕获和处理机制

### 4. 动态视觉效果
- **透明度变化**: 高度越高，投影越淡
- **轻微动画**: 细微的透明度和尺寸变化
- **航向同步**: 投影跟随飞机旋转
- **实时更新**: 60FPS的流畅更新

## 技术实现

### 核心配置
```javascript
const SHADOW_CONFIG = {
    imagePath: './img/drone1.png', // 无人机图标路径
    baseOpacity: 0.6,              // 基础透明度
    baseSize: 80,                  // 基础尺寸
    minScale: 0.3,                 // 最小缩放（远处）
    maxScale: 1.5,                 // 最大缩放（近处）
    maxHeight: 1000,               // 最大飞行高度
    // ... 更多配置
};
```

### 纹理加载
```javascript
const textureLoader = new THREE.TextureLoader();
textureLoader.load(
    SHADOW_CONFIG.imagePath,
    function(texture) {
        // 成功回调
        createShadowWithTexture(texture);
    },
    function(progress) {
        // 进度回调
        console.log('加载进度:', progress);
    },
    function(error) {
        // 错误回调
        createFallbackShadow();
    }
);
```

### 近大远小算法
```javascript
// 计算高度比例
const heightRatio = Math.min(planeHeight / SHADOW_CONFIG.maxHeight, 1);

// 反比例缩放：高度越高，投影越小
const distanceScale = SHADOW_CONFIG.maxScale - 
    (heightRatio * (SHADOW_CONFIG.maxScale - SHADOW_CONFIG.minScale));

// 应用缩放
planeShadow.scale.set(distanceScale, distanceScale, 1);
```

## 新增功能

### 图标切换功能
```javascript
// 切换到无人机1图标
ShadowController.setImage('./img/drone1.png');

// 切换到无人机2图标
ShadowController.setImage('./img/drone2.png');
```

### 增强的控制器
```javascript
const ShadowController = {
    toggle(),                    // 切换显示/隐藏
    setOpacity(opacity),         // 设置透明度
    setColor(color),            // 设置着色
    setImage(imagePath),        // 切换图标
    reset(),                    // 重置到默认状态
    getStatus()                 // 获取状态信息
};
```

## 用户界面

### 新增控制按钮
- **"无人机1"** - 切换到drone1.png图标
- **"无人机2"** - 切换到drone2.png图标
- **"重置投影"** - 恢复默认设置
- **"调试模式"** - 开启性能监控

### 使用方法
1. 页面加载时自动创建无人机图标投影
2. 投影会实时跟随飞机移动和旋转
3. 观察近大远小效果：飞机升高时投影变小
4. 使用控制按钮切换不同图标
5. 开启调试模式查看详细信息

## 视觉效果对比

### 优化前（几何形状）
- 简单的飞机轮廓形状
- 固定的缩放逻辑
- 单一的视觉效果

### 优化后（无人机图标）
- 真实的无人机图标
- 近大远小透视效果
- 动态图标切换
- 更丰富的视觉层次

## 性能优化

### 纹理管理
- 纹理复用，避免重复加载
- 适当的纹理尺寸
- 透明度测试优化

### 渲染优化
- 60FPS更新限制
- 条件渲染
- 批量属性更新

### 内存管理
- 纹理缓存
- 及时释放资源
- 避免内存泄漏

## 调试功能

### 控制台命令
```javascript
// 查看投影状态
ShadowController.getStatus();

// 切换图标
ShadowController.setImage('./img/drone2.png');

// 调整透明度
ShadowController.setOpacity(0.8);

// 开启调试模式
ShadowDebugger.toggleDebug();
```

### 调试信息
- 图标加载状态
- 投影尺寸和透明度
- 性能统计数据
- 错误信息记录

## 扩展建议

### 图标库扩展
- 添加更多无人机图标
- 支持SVG格式图标
- 动态图标动画

### 效果增强
- 阴影模糊效果
- 地面反射
- 环境光影响

### 交互功能
- 鼠标悬停效果
- 点击投影定位
- 投影信息显示

## 总结

通过使用真实的无人机图标和近大远小的透视效果，投影功能变得更加逼真和直观。用户可以：

1. **直观感受**: 通过投影大小直观感受飞机高度
2. **真实体验**: 使用真实图标增强沉浸感
3. **灵活控制**: 支持多种图标和自定义设置
4. **性能优化**: 保持流畅的60FPS更新

这个升级大大提升了3D可视化的用户体验和视觉效果！
