<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="400.667px" height="400.666px" viewBox="0 0 400.667 400.666" enable-background="new 0 0 400.667 400.666"
	 xml:space="preserve">
<filter  filterUnits="objectBoundingBox" id="AI_Shadow_1">
	<feGaussianBlur  stdDeviation="5" result="blur" in="SourceAlpha"></feGaussianBlur>
	<feOffset  dy="0" dx="0" result="offsetBlurredAlpha" in="blur"></feOffset>
	<feMerge>
		<feMergeNode  in="offsetBlurredAlpha"></feMergeNode>
		<feMergeNode  in="SourceGraphic"></feMergeNode>
	</feMerge>
</filter>
	<g filter="url(#AI_Shadow_1)">
		<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="200.333" y1="276.3325" x2="200.333" y2="124.3325">
			<stop  offset="0" style="stop-color:#3D2618"/>
			<stop  offset="0.4999" style="stop-color:#503723"/>
			<stop  offset="0.5001" style="stop-color:#9CCBE5"/>
			<stop  offset="1" style="stop-color:#558EBB"/>
		</linearGradient>
		<path fill="url(#SVGID_1_)" d="M301.333,200.333c0,41.936-33.963,75.938-75.885,76c-0.038,0-50.076,0-50.114,0
			c-41.974,0-76-34.026-76-76s34.026-76,76-76c0.027,0,50.056,0,50.083,0C267.352,124.377,301.333,158.387,301.333,200.333z"/>
	</g>
	<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="99.667" y1="200.457" x2="301.667" y2="200.457"/>
	
		<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="187.979" y1="187.374" x2="212.979" y2="187.374"/>
	
		<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="188.167" y1="213.499" x2="213.167" y2="213.499"/>
	<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="188.026" y1="241.42" x2="213.026" y2="241.42"/>
	
		<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="188.167" y1="159.374" x2="213.167" y2="159.374"/>
	
		<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="180.042" y1="173.405" x2="220.042" y2="173.405"/>
	
		<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="180.042" y1="227.499" x2="220.042" y2="227.499"/>
	
		<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="170.042" y1="255.332" x2="230.042" y2="255.332"/>
	
		<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="170.042" y1="145.333" x2="230.042" y2="145.333"/>
	<g>
		<path fill="#FFFFFF" d="M166.644,169.224h2.25v7.901h-1.493v-6.488h-1.627L166.644,169.224z"/>
		<path fill="#FFFFFF" d="M172.84,169.025c0.577,0,1.073,0.142,1.491,0.424c0.417,0.283,0.739,0.715,0.967,1.297
			c0.227,0.582,0.341,1.392,0.341,2.43c0,1.049-0.115,1.866-0.347,2.449c-0.23,0.583-0.542,1.012-0.932,1.287
			c-0.39,0.273-0.883,0.411-1.477,0.411s-1.094-0.138-1.499-0.414s-0.722-0.695-0.951-1.257s-0.344-1.362-0.344-2.401
			c0-1.45,0.224-2.501,0.671-3.153C171.259,169.383,171.952,169.025,172.84,169.025z M172.875,170.454
			c-0.253,0-0.479,0.082-0.675,0.244c-0.196,0.163-0.347,0.443-0.45,0.841s-0.155,0.979-0.155,1.746
			c0,0.999,0.119,1.689,0.356,2.071c0.238,0.381,0.546,0.572,0.924,0.572c0.365,0,0.654-0.181,0.869-0.542
			c0.261-0.437,0.391-1.167,0.391-2.191c0-1.039-0.117-1.754-0.351-2.148S173.247,170.454,172.875,170.454z"/>
	</g>
	<g>
		<path fill="#FFFFFF" d="M155.584,143.883h-1.466c0.04-0.852,0.307-1.523,0.803-2.011c0.496-0.489,1.133-0.733,1.91-0.733
			c0.48,0,0.903,0.101,1.27,0.304c0.367,0.202,0.661,0.494,0.881,0.875c0.221,0.382,0.331,0.769,0.331,1.163
			c0,0.469-0.133,0.974-0.4,1.515s-0.755,1.18-1.464,1.917l-0.887,0.935h2.816v1.391h-5.473v-0.72l2.444-2.492
			c0.591-0.598,0.984-1.079,1.179-1.442c0.195-0.363,0.292-0.692,0.292-0.985c0-0.304-0.101-0.556-0.303-0.755
			c-0.203-0.198-0.463-0.298-0.782-0.298c-0.322,0-0.591,0.12-0.806,0.36S155.599,143.471,155.584,143.883z"/>
		<path fill="#FFFFFF" d="M162.884,141.138c0.577,0,1.073,0.142,1.491,0.424c0.417,0.283,0.739,0.715,0.967,1.297
			c0.227,0.582,0.341,1.392,0.341,2.43c0,1.049-0.115,1.866-0.347,2.449c-0.23,0.583-0.542,1.012-0.932,1.287
			c-0.39,0.273-0.883,0.411-1.477,0.411s-1.094-0.138-1.499-0.414s-0.722-0.695-0.951-1.257s-0.344-1.362-0.344-2.401
			c0-1.45,0.224-2.501,0.671-3.153C161.303,141.496,161.996,141.138,162.884,141.138z M162.918,142.567
			c-0.253,0-0.479,0.082-0.675,0.244c-0.196,0.163-0.347,0.443-0.45,0.841s-0.155,0.979-0.155,1.746
			c0,0.999,0.119,1.689,0.356,2.071c0.238,0.381,0.546,0.572,0.924,0.572c0.365,0,0.654-0.181,0.869-0.542
			c0.261-0.437,0.391-1.167,0.391-2.191c0-1.039-0.117-1.754-0.351-2.148S163.291,142.567,162.918,142.567z"/>
	</g>
	<g>
		<path fill="#FFFFFF" d="M234.958,143.784h-1.466c0.04-0.852,0.307-1.523,0.803-2.011c0.496-0.489,1.133-0.733,1.91-0.733
			c0.48,0,0.903,0.101,1.27,0.304c0.367,0.202,0.661,0.494,0.881,0.875c0.221,0.382,0.331,0.769,0.331,1.163
			c0,0.469-0.133,0.974-0.4,1.515s-0.755,1.18-1.464,1.917l-0.887,0.935h2.816v1.391h-5.473v-0.72l2.444-2.492
			c0.591-0.598,0.984-1.079,1.179-1.442c0.195-0.363,0.292-0.692,0.292-0.985c0-0.304-0.101-0.556-0.303-0.755
			c-0.203-0.198-0.463-0.298-0.782-0.298c-0.322,0-0.591,0.12-0.806,0.36S234.973,143.372,234.958,143.784z"/>
		<path fill="#FFFFFF" d="M242.258,141.039c0.577,0,1.073,0.142,1.491,0.424c0.417,0.283,0.739,0.715,0.967,1.297
			c0.227,0.582,0.341,1.392,0.341,2.43c0,1.049-0.115,1.866-0.347,2.449c-0.23,0.583-0.542,1.012-0.932,1.287
			c-0.39,0.273-0.883,0.411-1.477,0.411s-1.094-0.138-1.499-0.414s-0.722-0.695-0.951-1.257s-0.344-1.362-0.344-2.401
			c0-1.45,0.224-2.501,0.671-3.153C240.677,141.397,241.37,141.039,242.258,141.039z M242.292,142.468
			c-0.253,0-0.479,0.082-0.675,0.244c-0.196,0.163-0.347,0.443-0.45,0.841s-0.155,0.979-0.155,1.746
			c0,0.999,0.119,1.689,0.356,2.071c0.238,0.381,0.546,0.572,0.924,0.572c0.365,0,0.654-0.181,0.869-0.542
			c0.261-0.437,0.391-1.167,0.391-2.191c0-1.039-0.117-1.754-0.351-2.148S242.665,142.468,242.292,142.468z"/>
	</g>
	<g>
		<path fill="#FFFFFF" d="M224.341,169.224h2.25v7.901h-1.493v-6.488h-1.627L224.341,169.224z"/>
		<path fill="#FFFFFF" d="M230.538,169.025c0.577,0,1.073,0.142,1.491,0.424c0.417,0.283,0.739,0.715,0.967,1.297
			c0.227,0.582,0.341,1.392,0.341,2.43c0,1.049-0.115,1.866-0.347,2.449c-0.23,0.583-0.542,1.012-0.932,1.287
			c-0.39,0.273-0.883,0.411-1.477,0.411s-1.094-0.138-1.499-0.414s-0.722-0.695-0.951-1.257s-0.344-1.362-0.344-2.401
			c0-1.45,0.224-2.501,0.671-3.153C228.957,169.383,229.649,169.025,230.538,169.025z M230.572,170.454
			c-0.253,0-0.479,0.082-0.675,0.244c-0.196,0.163-0.347,0.443-0.45,0.841s-0.155,0.979-0.155,1.746
			c0,0.999,0.119,1.689,0.356,2.071c0.238,0.381,0.546,0.572,0.924,0.572c0.365,0,0.654-0.181,0.869-0.542
			c0.261-0.437,0.391-1.167,0.391-2.191c0-1.039-0.117-1.754-0.351-2.148S230.944,170.454,230.572,170.454z"/>
	</g>
	<g>
		<path fill="#FFFFFF" d="M166.644,223.224h2.25v7.901h-1.493v-6.488h-1.627L166.644,223.224z"/>
		<path fill="#FFFFFF" d="M172.84,223.025c0.577,0,1.073,0.142,1.491,0.424c0.417,0.283,0.739,0.715,0.967,1.297
			c0.227,0.582,0.341,1.392,0.341,2.43c0,1.049-0.115,1.866-0.347,2.449c-0.23,0.583-0.542,1.012-0.932,1.287
			c-0.39,0.273-0.883,0.411-1.477,0.411s-1.094-0.138-1.499-0.414s-0.722-0.695-0.951-1.257s-0.344-1.362-0.344-2.401
			c0-1.45,0.224-2.501,0.671-3.153C171.259,223.383,171.952,223.025,172.84,223.025z M172.875,224.454
			c-0.253,0-0.479,0.082-0.675,0.244c-0.196,0.163-0.347,0.443-0.45,0.841s-0.155,0.979-0.155,1.746
			c0,0.999,0.119,1.689,0.356,2.071c0.238,0.381,0.546,0.572,0.924,0.572c0.365,0,0.654-0.181,0.869-0.542
			c0.261-0.437,0.391-1.167,0.391-2.191c0-1.039-0.117-1.754-0.351-2.148S173.247,224.454,172.875,224.454z"/>
	</g>
	<g>
		<path fill="#FFFFFF" d="M224.341,223.224h2.25v7.901h-1.493v-6.488h-1.627L224.341,223.224z"/>
		<path fill="#FFFFFF" d="M230.538,223.025c0.577,0,1.073,0.142,1.491,0.424c0.417,0.283,0.739,0.715,0.967,1.297
			c0.227,0.582,0.341,1.392,0.341,2.43c0,1.049-0.115,1.866-0.347,2.449c-0.23,0.583-0.542,1.012-0.932,1.287
			c-0.39,0.273-0.883,0.411-1.477,0.411s-1.094-0.138-1.499-0.414s-0.722-0.695-0.951-1.257s-0.344-1.362-0.344-2.401
			c0-1.45,0.224-2.501,0.671-3.153C228.957,223.383,229.649,223.025,230.538,223.025z M230.572,224.454
			c-0.253,0-0.479,0.082-0.675,0.244c-0.196,0.163-0.347,0.443-0.45,0.841s-0.155,0.979-0.155,1.746
			c0,0.999,0.119,1.689,0.356,2.071c0.238,0.381,0.546,0.572,0.924,0.572c0.365,0,0.654-0.181,0.869-0.542
			c0.261-0.437,0.391-1.167,0.391-2.191c0-1.039-0.117-1.754-0.351-2.148S230.944,224.454,230.572,224.454z"/>
	</g>
	<g>
		<path fill="#FFFFFF" d="M155.584,253.883h-1.466c0.04-0.852,0.307-1.523,0.803-2.011c0.496-0.489,1.133-0.733,1.91-0.733
			c0.48,0,0.903,0.101,1.27,0.304c0.367,0.202,0.661,0.494,0.881,0.875c0.221,0.382,0.331,0.769,0.331,1.163
			c0,0.469-0.133,0.974-0.4,1.515s-0.755,1.18-1.464,1.917l-0.887,0.935h2.816v1.391h-5.473v-0.72l2.444-2.492
			c0.591-0.598,0.984-1.079,1.179-1.442c0.195-0.363,0.292-0.692,0.292-0.985c0-0.304-0.101-0.556-0.303-0.755
			c-0.203-0.198-0.463-0.298-0.782-0.298c-0.322,0-0.591,0.12-0.806,0.36S155.599,253.471,155.584,253.883z"/>
		<path fill="#FFFFFF" d="M162.884,251.138c0.577,0,1.073,0.142,1.491,0.424c0.417,0.283,0.739,0.715,0.967,1.297
			c0.227,0.582,0.341,1.392,0.341,2.43c0,1.049-0.115,1.866-0.347,2.449c-0.23,0.583-0.542,1.012-0.932,1.287
			c-0.39,0.273-0.883,0.411-1.477,0.411s-1.094-0.138-1.499-0.414s-0.722-0.695-0.951-1.257s-0.344-1.362-0.344-2.401
			c0-1.45,0.224-2.501,0.671-3.153C161.303,251.496,161.996,251.138,162.884,251.138z M162.918,252.567
			c-0.253,0-0.479,0.082-0.675,0.244c-0.196,0.163-0.347,0.443-0.45,0.841s-0.155,0.979-0.155,1.746
			c0,0.999,0.119,1.689,0.356,2.071c0.238,0.381,0.546,0.572,0.924,0.572c0.365,0,0.654-0.181,0.869-0.542
			c0.261-0.437,0.391-1.167,0.391-2.191c0-1.039-0.117-1.754-0.351-2.148S163.291,252.567,162.918,252.567z"/>
	</g>
	<g>
		<path fill="#FFFFFF" d="M234.958,253.784h-1.466c0.04-0.852,0.307-1.523,0.803-2.011c0.496-0.489,1.133-0.733,1.91-0.733
			c0.48,0,0.903,0.101,1.27,0.304c0.367,0.202,0.661,0.494,0.881,0.875c0.221,0.382,0.331,0.769,0.331,1.163
			c0,0.469-0.133,0.974-0.4,1.515s-0.755,1.18-1.464,1.917l-0.887,0.935h2.816v1.391h-5.473v-0.72l2.444-2.492
			c0.591-0.598,0.984-1.079,1.179-1.442c0.195-0.363,0.292-0.692,0.292-0.985c0-0.304-0.101-0.556-0.303-0.755
			c-0.203-0.198-0.463-0.298-0.782-0.298c-0.322,0-0.591,0.12-0.806,0.36S234.973,253.372,234.958,253.784z"/>
		<path fill="#FFFFFF" d="M242.258,251.039c0.577,0,1.073,0.142,1.491,0.424c0.417,0.283,0.739,0.715,0.967,1.297
			c0.227,0.582,0.341,1.392,0.341,2.43c0,1.049-0.115,1.866-0.347,2.449c-0.23,0.583-0.542,1.012-0.932,1.287
			c-0.39,0.273-0.883,0.411-1.477,0.411s-1.094-0.138-1.499-0.414s-0.722-0.695-0.951-1.257s-0.344-1.362-0.344-2.401
			c0-1.45,0.224-2.501,0.671-3.153C240.677,251.397,241.37,251.039,242.258,251.039z M242.292,252.468
			c-0.253,0-0.479,0.082-0.675,0.244c-0.196,0.163-0.347,0.443-0.45,0.841s-0.155,0.979-0.155,1.746
			c0,0.999,0.119,1.689,0.356,2.071c0.238,0.381,0.546,0.572,0.924,0.572c0.365,0,0.654-0.181,0.869-0.542
			c0.261-0.437,0.391-1.167,0.391-2.191c0-1.039-0.117-1.754-0.351-2.148S242.665,252.468,242.292,252.468z"/>
	</g>

</svg>
