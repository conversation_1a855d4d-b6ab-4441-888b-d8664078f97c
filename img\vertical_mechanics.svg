<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="400.667px" height="400.666px" viewBox="0 0 400.667 400.666" enable-background="new 0 0 400.667 400.666"
	 xml:space="preserve">
<filter  filterUnits="objectBoundingBox" id="AI_Shadow_1">
	<feGaussianBlur  stdDeviation="5" result="blur" in="SourceAlpha"></feGaussianBlur>
	<feOffset  dy="0" dx="0" result="offsetBlurredAlpha" in="blur"></feOffset>
	<feMerge>
		<feMergeNode  in="offsetBlurredAlpha"></feMergeNode>
		<feMergeNode  in="SourceGraphic"></feMergeNode>
	</feMerge>
</filter>
<circle fill="#232323" cx="200.333" cy="200" r="161"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="50.234" y1="200" x2="73.667" y2="200"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="94.197" y1="93.864" x2="110.767" y2="110.434"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="200.333" y1="49.902" x2="200.333" y2="73.334"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="306.468" y1="93.865" x2="289.9" y2="110.434"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="348.583" y1="223.481" x2="325.44" y2="219.814"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="348.583" y1="176.521" x2="325.44" y2="180.185"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="306.467" y1="306.137" x2="289.9" y2="289.566"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="200.332" y1="350.1" x2="200.333" y2="326.666"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="94.196" y1="306.137" x2="110.767" y2="289.566"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="50.697" y1="188.224" x2="66.627" y2="189.477"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="52.082" y1="176.52" x2="67.865" y2="179.019"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="54.381" y1="164.96" x2="69.919" y2="168.689"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="57.581" y1="153.616" x2="72.777" y2="158.554"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="61.66" y1="142.559" x2="76.422" y2="148.674"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="66.594" y1="131.855" x2="80.831" y2="139.11"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="72.353" y1="121.572" x2="85.977" y2="129.922"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="78.9" y1="111.772" x2="91.827" y2="121.165"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="86.197" y1="102.517" x2="98.347" y2="112.895"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="50.697" y1="211.777" x2="66.627" y2="210.523"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="52.083" y1="223.481" x2="67.866" y2="220.98"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="54.382" y1="235.041" x2="69.92" y2="231.309"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="57.582" y1="246.384" x2="72.779" y2="241.444"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="61.661" y1="257.441" x2="76.423" y2="251.324"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="66.595" y1="268.144" x2="80.833" y2="260.887"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="72.354" y1="278.427" x2="85.978" y2="270.075"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="78.902" y1="288.226" x2="91.829" y2="278.831"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="86.199" y1="297.481" x2="98.348" y2="287.101"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="112.11" y1="321.432" x2="121.5" y2="308.502"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="132.192" y1="333.738" x2="139.444" y2="319.499"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="153.952" y1="342.751" x2="158.887" y2="327.553"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="176.853" y1="348.25" x2="179.351" y2="332.466"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="223.814" y1="348.25" x2="221.312" y2="332.467"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="246.716" y1="342.751" x2="241.776" y2="327.555"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="268.476" y1="333.738" x2="261.219" y2="319.501"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="288.558" y1="321.432" x2="279.164" y2="308.506"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="321.764" y1="288.227" x2="308.834" y2="278.836"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="334.071" y1="268.145" x2="319.832" y2="260.892"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="343.084" y1="246.385" x2="327.886" y2="241.449"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="343.085" y1="153.621" x2="327.888" y2="158.561"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="334.072" y1="131.861" x2="319.834" y2="139.117"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="321.765" y1="111.778" x2="308.838" y2="121.173"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="288.559" y1="78.572" x2="279.168" y2="91.502"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="268.477" y1="66.266" x2="261.225" y2="80.505"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="246.717" y1="57.253" x2="241.782" y2="72.451"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="223.815" y1="51.754" x2="221.318" y2="67.538"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="176.855" y1="51.754" x2="179.357" y2="67.537"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="153.953" y1="57.252" x2="158.893" y2="72.449"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="132.193" y1="66.266" x2="139.45" y2="80.502"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="112.111" y1="78.572" x2="121.505" y2="91.498"/>
<g display="none" filter="url(#AI_Shadow_1)">
	<path display="inline" fill="#232323" d="M200.333,33.5c-91.956,0-166.5,74.544-166.5,166.5s74.544,166.5,166.5,166.5
		c91.957,0,166.5-74.544,166.5-166.5S292.29,33.5,200.333,33.5z M200.667,350.099c-82.714,0-149.767-67.053-149.767-149.767
		c0-82.713,67.053-149.767,149.767-149.767s149.767,67.053,149.767,149.767C350.433,283.046,283.38,350.099,200.667,350.099z"/>
	<path display="inline" fill="none" stroke="#353535" stroke-width="1.3" stroke-miterlimit="10" d="M200.333,33.5
		c-91.956,0-166.5,74.544-166.5,166.5s74.544,166.5,166.5,166.5c91.957,0,166.5-74.544,166.5-166.5S292.29,33.5,200.333,33.5z
		 M200.667,350.099c-82.714,0-149.767-67.053-149.767-149.767c0-82.713,67.053-149.767,149.767-149.767
		s149.767,67.053,149.767,149.767C350.433,283.046,283.38,350.099,200.667,350.099z"/>
</g>
<path fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" d="M337.011,178.353c0,0,3,8.314,3,21.98
	c0,13.167-3,21.315-3,21.315"/>
<text transform="matrix(1 0 0 1 83.1968 208.1665)" fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" font-family="sans-serif" font-size="25">0</text>
<path fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" d="M121.417,181.292h-36.25
	c0,0,0.342-4.491,2.306-11.307c1.694-5.881,3.944-10.068,3.944-10.068"/>
<polygon fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" points="92.042,158.554 88.167,160.792 92.813,162.73 "/>
<path fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" d="M121.417,218.708h-36.25
	c0,0,0.342,4.491,2.306,11.307c1.694,5.881,3.944,10.068,3.944,10.068"/>
<polygon fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" points="92.042,241.446 88.167,239.208 92.813,237.27 "/>
<text transform="matrix(1 0 0 1 125.0005 184.6665)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="10">UP</text>
<text transform="matrix(1 0 0 1 125.0005 222.6665)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="10">DOWN</text>
<text transform="matrix(1 0 0 1 193.6592 102.1489)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="1.5" stroke-miterlimit="10" font-family="sans-serif" font-size="25">1</text>
<text transform="matrix(1 0 0 1 192.6592 316.4985)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="1.5" stroke-miterlimit="10" font-family="sans-serif" font-size="25">1</text>
<text transform="matrix(1 0 0 1 112.1099 131.8608)" fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" font-family="sans-serif" font-size="25">.5</text>
<text transform="matrix(1 0 0 1 112.1108 283.5313)" fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" font-family="sans-serif" font-size="25">.5</text>
<text transform="matrix(1 0 0 1 258.8477 135.4829)"><tspan x="0" y="0" fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" font-family="sans-serif" font-size="25" letter-spacing="-3">1</tspan><tspan x="10.355" y="0" fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" font-family="sans-serif" font-size="25">.5</tspan></text>
<text transform="matrix(1 0 0 1 261.2246 281.8164)"><tspan x="0" y="0" fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" font-family="sans-serif" font-size="25" letter-spacing="-3">1</tspan><tspan x="10.355" y="0" fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" font-family="sans-serif" font-size="25">.5</tspan></text>
<text transform="matrix(1 0 0 1 304.8989 208.5229)" fill="#FFFFFF" stroke="#FFFFFF" stroke-miterlimit="10" font-family="sans-serif" font-size="25" letter-spacing="-3">2</text>
<text transform="matrix(1 0 0 1 143.4497 165.6895)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="14">VERTICAL SPEED</text>
<text transform="matrix(1 0 0 1 146.4497 242.3579)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="14">1000FT PER MIN</text>
</svg>
