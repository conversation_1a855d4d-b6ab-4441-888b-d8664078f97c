<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title> 3D地图可视化</title>
    <script src="./js/three.js"></script>
    <script src="./js/loaders/GLTFLoader.js"></script>
    <script src="./js/loaders/OBJLoader.js"></script>
    <script src="./js/turf.min.js"></script>
    <script src="./js/modifiers/CurveModifier.js"></script>
    <script
        src="https://webapi.amap.com/maps?v=2.0&key=66344304d66c1e59c6f3890c93e7d772&plugin=AMap.ControlBar,AMap.ToolBar,AMap.MouseTool"></script>
    <link rel="stylesheet" href="./css/main.css">

    <!--地图仪表展示  -->
    <script src="js/jquery-1.8.3.min.js"></script>
    <!-- Importing the FlightIndicators library -->
    <script src="js/jquery.flightindicators.js"></script>
</head>

<body>
    <div class="contain">
        <!-- Second example -->
        <div class="examples">
            <div>
                <!-- 姿态 -->
                <span id="attitude"></span>
                <!--空速 -->
                <span id="airspeed"></span>
            </div>
            <div>
                <!-- 高度表 -->
                <span id="altimeter"></span>
                <!-- 转弯协调器 -->
                <span id="turn_coordinator"></span>
            </div>
            <div>
                <!-- 航向 -->
                <span id="heading"></span>
                <!-- 垂直速度    vertical-->
                <span id="variometer"></span>
            </div>
        </div>
    </div>
    <div id="menu">
        <button onclick="startPlane()">开始飞机</button>
        <hr>
        <button onclick="stopPlane()">停止飞机</button>
        <hr>
        <button onclick="fenjie()">分解飞机</button>
    </div>

    <div id="container">
    </div>
    <!-- <script>
        console.warn = console.log;
    </script> -->
    <script type="text/javascript">
        // First static example
       /// var first_attitude = $.flightIndicator('#first_attitude', 'attitude', { size: 150, roll: 8, pitch: 3, showBox: true });
        // Dynamic examples
        var attitude = $.flightIndicator('#attitude', 'attitude', {size: 120, roll: 50, pitch: -20,  showBox: true });
        var heading = $.flightIndicator('#heading', 'heading', { size: 120,heading: 150, showBox: true });
        var variometer = $.flightIndicator('#variometer', 'variometer', { size: 120,vario: -5, showBox: true });
        var airspeed = $.flightIndicator('#airspeed', 'airspeed', {size: 120, showBox: false });
        var altimeter = $.flightIndicator('#altimeter', 'altimeter',{size: 120});
        var turn_coordinator = $.flightIndicator('#turn_coordinator', 'turn_coordinator', { size: 120,turn: 0 });
        // Update at 20Hz
        var increment = 0;
        console.log(attitude);
        console.log(airspeed);
        console.log(altimeter);
        altimeter.showBox(true);
        setInterval(function () {
            // Airspeed update
            airspeed.setAirSpeed(80 + 80 * Math.sin(increment / 10));
            // Attitude update
            attitude.setRoll(30 * Math.sin(increment / 10));
            attitude.setPitch(50 * Math.sin(increment / 20));
            // Altimeter update
            altimeter.setAltitude(10 * increment);
            altimeter.setPressure(1000 + 3 * Math.sin(increment / 50));
            increment++;
            // TC update - note that the TC appears opposite the angle of the attitude indicator, as it mirrors the actual wing up/down position
            turn_coordinator.setTurn((30 * Math.sin(increment / 10)) * -1);
            // Heading update
            heading.setHeading(increment);
            // Vario update
            variometer.setVario(2 * Math.sin(increment / 10));
        }, 100);

        console.log(30 * Math.sin(30));
        console.log(Math.sin(180));
    </script>

    <script type="text/javascript">
        console.warn = console.log;
        javascript: (function () {
            var script = document.createElement('script');
            script.onload = function () {
                var stats = new Stats();
                document.body.appendChild(stats.dom);
                requestAnimationFrame(function loop() {
                    stats.update();
                    requestAnimationFrame(loop)
                });
            };
            script.src = './js/stats.min.js';
            document.head.appendChild(script);
        })()
    </script>
    <script type="text/javascript">
        var map = new AMap.Map('container', {
            center: [108.983823, 34.246131],
            zooms: [16, 19],
            rotation: 45,
            wallColor: '#666',   //楼面周围颜色
            roofColor: '#000 ',    //楼的顶部颜色
            buildingAnimation: true,//楼块出现是否带动画
            zoom: 17,
            viewMode: '3D',
            pitch: 50,
        });
        var controlBar = new AMap.ControlBar({
            position: {
                right: '10px',
                top: '10px'
            }
        });
        controlBar.addTo(map);
        var toolBar = new AMap.ToolBar({
            position: {
                right: '40px',
                top: '110px'
            }
        });
        toolBar.addTo(map);
        const ACTION_SELECT = 1, ACTION_NONE = 0;
        const curveHandles = [];
        const mouse = new THREE.Vector2();
        var camera;
        var renderer;
        var scene;
        // 数据转换工具
        var customCoords = map.customCoords;
        var meshes = [];
        // 数据使用转换工具进行转换，这个操作必须要提前执行（在获取镜头参数 函数之前执行），否则将会获得一个错误信息。
        var mesh2, mesh3
        var data = customCoords.lngLatsToCoords([
            [108.982923, 34.246131],
            [108.984823, 34.246131],
            [108.986823, 34.246131],
        ]);
        var object, box, blades, mixer,animations;

        var objPosition = [108.983823, 34.246131]
        var objPosition1 = [108.985823, 34.246131]
        // 创建 GL 图层
        var gllayer = new AMap.GLCustomLayer({
            // 图层的层级
            zIndex: 100,
            // heightFactor: 100, //2倍于默认高度，3D下有效
            // 初始化的操作，创建图层过程中执行一次。
            init: (gl) => {
                // 这里我们的地图模式是 3D，所以创建一个透视相机，相机的参数初始化可以随意设置，因为在 render 函数中，每一帧都需要同步相机参数，因此这里变得不那么重要。
                // 如果你需要 2D 地图（viewMode: '2D'），那么你需要创建一个正交相机
                camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 1000, 1 << 30);
                renderer = new THREE.WebGLRenderer({
                    context: gl,  // 地图的 gl 上下文
                    // alpha: true,
                    // antialias: true,
                    // canvas: gl.canvas,
                });
                // 自动清空画布这里必须设置为 false，否则地图底图将无法显示
                renderer.autoClear = false;
                scene = new THREE.Scene();
                // 环境光照和平行光
                var aLight = new THREE.AmbientLight(0xffffff, 0.6);
                var dLight = new THREE.DirectionalLight(0xffffff, 1);
                dLight.position.set(-1000, -100, 900);
                scene.add(dLight);
                scene.add(aLight);
                initGltf()
            },
            render: () => {
                // 这里必须执行！！重新设置 three 的 gl 上下文状态。
                renderer.resetState();
                // 重新设置图层的渲染中心点，将模型等物体的渲染中心点重置
                // 否则和 LOCA 可视化等多个图层能力使用的时候会出现物体位置偏移的问题
                customCoords.setCenter([116.271363, 39.992414]);
                var { near, far, fov, up, lookAt, position } = customCoords.getCameraParams();
                // 2D 地图下使用的正交相机
                // var { near, far, top, bottom, left, right, position, rotation } = customCoords.getCameraParams();
                // 这里的顺序不能颠倒，否则可能会出现绘制卡顿的效果。

                camera.near = near;
                camera.far = far;
                camera.fov = fov;
                camera.position.set(...position);
                camera.up.set(...up);
                camera.lookAt(...lookAt);
                camera.updateProjectionMatrix();
                // 2D 地图使用的正交相机参数赋值
                // camera.top = top;
                // camera.bottom = bottom;
                // camera.left = left;
                // camera.right = right;
                // camera.position.set(...position);
                // camera.updateProjectionMatrix();
                renderer.render(scene, camera);
                // 这里必须执行！！重新设置 three 的 gl 上下文状态。
                renderer.resetState();
            },
        });
        map.add(gllayer);
        // 初始化模型
        function initGltf() {
            // 添加天空盒
            const skyGeometry = new THREE.SphereGeometry(50000, 32, 32);
            const skyMaterial = new THREE.MeshBasicMaterial({
                color: 0x87CEEB,
                side: THREE.BackSide,
                transparent: true,
                opacity: 0.8
            });
            const sky = new THREE.Mesh(skyGeometry, skyMaterial);
            scene.add(sky);
            
            // 添加云朵粒子效果
            createClouds();
            
            var loader = new THREE.GLTFLoader();
            loader.load('./gltf/dj/scene.gltf', (gltf) => {
                console.log(gltf);
                object = gltf.scene;
                animations = gltf.animations;
                console.log(animations)
                // const mixer = new THREE.AnimationMixer( object );
               // numAnimations = animations.length;
                mixer = new THREE.AnimationMixer(object);
                // const clip = THREE.AnimationClip.findByName(animations, 'hover');
                // // const clip = THREE.AnimationClip.findByName( animations,'exploded_view' );
                // // const clip = THREE.AnimationClip.findByName( animations,'StaticPose' );
                // const action = mixer.clipAction(clip);
                // action.play();
                mixer.clipAction(gltf.animations[0]).play();
                object.scale.set(1000, 1000, 1000)    //模型大小缩放
                setRotation({
                    x: 90, y: 100, z: 0
                });
                scene.add(object);

                // 渲染循环
                const animate = function () {
                    requestAnimationFrame(animate);
                    mixer.update(0.01); // 更新动画
                    renderer.render(scene, camera);
                };
                animate();
                // 模型动画
                startMove();//运动
            });
        }

        // 创建云朵粒子效果
        function createClouds() {
            const cloudGeometry = new THREE.BufferGeometry();
            const cloudCount = 1000;
            const positions = new Float32Array(cloudCount * 3);
            
            for (let i = 0; i < cloudCount * 3; i += 3) {
                positions[i] = (Math.random() - 0.5) * 20000;     // x
                positions[i + 1] = (Math.random() - 0.5) * 20000; // y
                positions[i + 2] = Math.random() * 5000 + 1000;   // z
            }
            
            cloudGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            
            const cloudMaterial = new THREE.PointsMaterial({
                color: 0xffffff,
                size: 50,
                transparent: true,
                opacity: 0.6,
                blending: THREE.AdditiveBlending
            });
            
            const clouds = new THREE.Points(cloudGeometry, cloudMaterial);
            scene.add(clouds);
        }

        // 模型移动函数
        // 设置模型角度
        function setRotation(rotation) {
            var x = Math.PI / 180 * (rotation.x || 0);
            var y = Math.PI / 180 * (rotation.y || 0);
            var z = Math.PI / 180 * (rotation.z || 0);
            object.rotation.set(x, y, z);
        }
        // 设置模型位置
        function setPosition(object, lnglat) {
            // 对模型的经纬度进行转换*//*****************
            var position = customCoords.lngLatsToCoords([
                lnglat
            ])[0];
          ///  console.log(position);
            object.position.setX(position[0])           // 模型坐标
            object.position.setY(position[1])          // 模型坐标
            // console.log(position)
            //   map.setCenter(lnglat, true)
            object.position.setZ(200)            // 模型高度   此处为定义的模型高度
        }
        //定义模型位置编号
        function setPositionn(obj, lnglat) {
            var p = customCoords.lngLatsToCoords([
                lnglat
            ])[0];
            // 对模型的经纬度进行转换*//*****************
            console.log(p);
            obj.position.setX(p[0])           // 模型坐标
            obj.position.setY(p[1])          // 模型坐标
            obj.position.setZ(500)            // 模型高度   此处为定义的模型高度
        }
        // 设置模型角度
        function setAngle(angle) {
            var x = object.rotation.x;
            var z = object.rotation.z;
            var y = Math.PI / 180 * angle;
            object.rotation.set(x, y, z);
        }
        // 动画

        // 改变模型位置和角度
        var centerPoint = turf.point([108.983823, 34.246131]);
        var timer = 0;
        // 添加HUD数据更新函数
        function updateHUD() {
            if (object) {
                // 更新高度显示
                const altitude = Math.round(object.position.z);
                document.getElementById('altitude-display').textContent = altitude + ' m';
                
                // 更新速度显示（基于动画速度计算）
                const speed = Math.round(80 + 80 * Math.sin(increment / 10));
                document.getElementById('speed-display').textContent = speed + ' km/h';
                
                // 更新航向显示
                const heading = Math.round(timer % 360);
                document.getElementById('heading-display').textContent = heading + '°';
                
                // 更新坐标显示
                const currentPos = turf.transformTranslate(centerPoint, 0.3, timer).geometry.coordinates;
                document.getElementById('coordinates-display').textContent = 
                    currentPos[0].toFixed(6) + ', ' + currentPos[1].toFixed(6);
            }
        }

        function startMove() {
            if (timer > 360) {
                timer = 0;
            }
            requestAnimationFrame(function () {
                timer += 0.1;
                var pos = turf.transformTranslate(centerPoint, 0.3, timer).geometry.coordinates;
                var angle = timer;
                
                setPosition(object, pos);
                map.setCenter(pos, true);
                
                // 更新HUD显示
                updateHUD();
                
                map.render();
                startMove();
            });
        }
        function startPlane() {
            mixer = new THREE.AnimationMixer(object);
                const clip = THREE.AnimationClip.findByName(animations, 'hover');
                // const clip = THREE.AnimationClip.findByName( animations,'exploded_view' );
                // const clip = THREE.AnimationClip.findByName( animations,'StaticPose' );
                const action = mixer.clipAction(clip);
                action.play();
        }
        function stopPlane() {
            mixer.stopAllAction ();
        }
        function fenjie(){
            mixer = new THREE.AnimationMixer(object);
                const clip = THREE.AnimationClip.findByName(animations, 'exploded_view');
                // const clip = THREE.AnimationClip.findByName( animations,'exploded_view' );
                // const clip = THREE.AnimationClip.findByName( animations,'StaticPose' );
                const action = mixer.clipAction(clip);
                action.play();
        }

        setInterval(() => {
            mixer.uncacheAction ();//
         //   console.log(timer)
        }, 3000);
    </script>
    <!-- 在body中添加HUD界面 -->
    <div id="hud-overlay">
        <div class="hud-top">
            <div class="flight-status">
                <div class="status-item">
                    <span class="label">高度</span>
                    <span class="value" id="altitude-display">0 m</span>
                </div>
                <div class="status-item">
                    <span class="label">速度</span>
                    <span class="value" id="speed-display">0 km/h</span>
                </div>
                <div class="status-item">
                    <span class="label">航向</span>
                    <span class="value" id="heading-display">0°</span>
                </div>
            </div>
        </div>
        
        <div class="hud-center">
            <div class="crosshair">
                <div class="crosshair-h"></div>
                <div class="crosshair-v"></div>
            </div>
        </div>
        
        <div class="hud-bottom">
            <div class="coordinates">
                <span id="coordinates-display">108.983823, 34.246131</span>
            </div>
        </div>
    </div>
</body>
</html>
