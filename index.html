<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title> 3D地图可视化</title>
    <script src="./js/three.js"></script>
    <script src="./js/loaders/GLTFLoader.js"></script>
    <script src="./js/loaders/OBJLoader.js"></script>
    <script src="./js/turf.min.js"></script>
    <script src="./js/modifiers/CurveModifier.js"></script>
    <script
        src="https://webapi.amap.com/maps?v=2.0&key=66344304d66c1e59c6f3890c93e7d772&plugin=AMap.ControlBar,AMap.ToolBar,AMap.MouseTool"></script>
    <link rel="stylesheet" href="./css/main.css">

    <!--地图仪表展示  -->
    <script src="js/jquery-1.8.3.min.js"></script>

</head>

<body>
  
    <div id="menu">
        <!-- <button onclick="startPlane()">开始飞机</button>
        <hr>
        <button onclick="stopPlane()">停止飞机</button>
        <hr>
        <button onclick="fenjie()">分解飞机</button>
        <hr> -->
        <button onclick="toggleShadow()">切换投影显示</button>
        <hr>
        <button onclick="ShadowController.reset()">重置投影</button>
        <hr>
        <button onclick="ShadowDebugger.toggleDebug()">调试模式</button>
        <hr>
        <div style="color: white; font-size: 12px; margin-bottom: 10px;">
            💡 投影功能说明：<br>
            • 实时跟随飞机位置和航向<br>
            • 高度越高投影越大越淡<br>
            • 支持显示/隐藏切换<br>
            • 可开启调试模式查看详情
        </div>
    </div>

    <div id="container">
    </div>
 


    <script type="text/javascript">
        
        // Update at 20Hz
        var increment = 0;
        console.warn = console.log;
        javascript: (function () {
            var script = document.createElement('script');
            script.onload = function () {
                var stats = new Stats();
                document.body.appendChild(stats.dom);
                requestAnimationFrame(function loop() {
                    stats.update();
                    requestAnimationFrame(loop)
                });
            };
            script.src = './js/stats.min.js';
            document.head.appendChild(script);
        })()
    </script>
    <script type="text/javascript">
        var map = new AMap.Map('container', {
            center: [108.983823, 34.246131],
            zooms: [16, 19],
            rotation: 45,
            wallColor: '#666',   //楼面周围颜色
            roofColor: '#000 ',    //楼的顶部颜色
            buildingAnimation: true,//楼块出现是否带动画
            zoom: 17,
            viewMode: '3D',
            pitch: 50,
        });
        var controlBar = new AMap.ControlBar({
            position: {
                right: '10px',
                top: '10px'
            }
        });
        controlBar.addTo(map);
        var toolBar = new AMap.ToolBar({
            position: {
                right: '40px',
                top: '110px'
            }
        });
        toolBar.addTo(map);
        const ACTION_SELECT = 1, ACTION_NONE = 0;
        const curveHandles = [];
        const mouse = new THREE.Vector2();
        var camera;
        var renderer;
        var scene;
        // 数据转换工具
        var customCoords = map.customCoords;
        var meshes = [];
        // 数据使用转换工具进行转换，这个操作必须要提前执行（在获取镜头参数 函数之前执行），否则将会获得一个错误信息。
        var mesh2, mesh3
        var data = customCoords.lngLatsToCoords([
            [108.982923, 34.246131],
            [108.984823, 34.246131],
            [108.986823, 34.246131],
        ]);
        var object, box, blades, mixer,animations;
        var planeShadow; // 飞机投影对象

        var objPosition = [108.983823, 34.246131]
        var objPosition1 = [108.985823, 34.246131]
        // 创建 GL 图层
        var gllayer = new AMap.GLCustomLayer({
            // 图层的层级
            zIndex: 100,
            // heightFactor: 100, //2倍于默认高度，3D下有效
            // 初始化的操作，创建图层过程中执行一次。
            init: (gl) => {
                // 这里我们的地图模式是 3D，所以创建一个透视相机，相机的参数初始化可以随意设置，因为在 render 函数中，每一帧都需要同步相机参数，因此这里变得不那么重要。
                // 如果你需要 2D 地图（viewMode: '2D'），那么你需要创建一个正交相机
                camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 1000, 1 << 30);
                renderer = new THREE.WebGLRenderer({
                    context: gl,  // 地图的 gl 上下文
                    // alpha: true,
                    // antialias: true,
                    // canvas: gl.canvas,
                });
                // 自动清空画布这里必须设置为 false，否则地图底图将无法显示
                renderer.autoClear = false;
                scene = new THREE.Scene();
                // 环境光照和平行光
                var aLight = new THREE.AmbientLight(0xffffff, 0.6);
                var dLight = new THREE.DirectionalLight(0xffffff, 1);
                dLight.position.set(-1000, -100, 900);
                scene.add(dLight);
                scene.add(aLight);
                initGltf()
            },
            render: () => {
                // 这里必须执行！！重新设置 three 的 gl 上下文状态。
                renderer.resetState();
                // 重新设置图层的渲染中心点，将模型等物体的渲染中心点重置
                // 否则和 LOCA 可视化等多个图层能力使用的时候会出现物体位置偏移的问题
                customCoords.setCenter([116.271363, 39.992414]);
                var { near, far, fov, up, lookAt, position } = customCoords.getCameraParams();
                // 2D 地图下使用的正交相机
                // var { near, far, top, bottom, left, right, position, rotation } = customCoords.getCameraParams();
                // 这里的顺序不能颠倒，否则可能会出现绘制卡顿的效果。

                camera.near = near;
                camera.far = far;
                camera.fov = fov;
                camera.position.set(...position);
                camera.up.set(...up);
                camera.lookAt(...lookAt);
                camera.updateProjectionMatrix();
                // 2D 地图使用的正交相机参数赋值
                // camera.top = top;
                // camera.bottom = bottom;
                // camera.left = left;
                // camera.right = right;
                // camera.position.set(...position);
                // camera.updateProjectionMatrix();
                renderer.render(scene, camera);
                // 这里必须执行！！重新设置 three 的 gl 上下文状态。
                renderer.resetState();
            },
        });
        map.add(gllayer);
        // 初始化模型
        function initGltf() {
            // 添加天空盒
            const skyGeometry = new THREE.SphereGeometry(50000, 32, 32);
            const skyMaterial = new THREE.MeshBasicMaterial({
                color: 0x87CEEB,
                side: THREE.BackSide,
                transparent: true,
                opacity: 0.8
            });
            const sky = new THREE.Mesh(skyGeometry, skyMaterial);
            scene.add(sky);
            
            // 添加云朵粒子效果
            createClouds();

            // 创建飞机投影
            createPlaneShadow();

            var loader = new THREE.GLTFLoader();
            loader.load('./gltf/dj/scene.gltf', (gltf) => {
                console.log(gltf);
                object = gltf.scene;
                animations = gltf.animations;
                console.log(animations)
                // const mixer = new THREE.AnimationMixer( object );
               // numAnimations = animations.length;
                mixer = new THREE.AnimationMixer(object);
                // const clip = THREE.AnimationClip.findByName(animations, 'hover');
                // // const clip = THREE.AnimationClip.findByName( animations,'exploded_view' );
                // // const clip = THREE.AnimationClip.findByName( animations,'StaticPose' );
                // const action = mixer.clipAction(clip);
                // action.play();
                mixer.clipAction(gltf.animations[0]).play();
                object.scale.set(1000, 1000, 1000)    //模型大小缩放
                setRotation({
                    x: 90, y: 100, z: 0
                });
                scene.add(object);

                // 渲染循环
                const animate = function () {
                    requestAnimationFrame(animate);
                    mixer.update(0.01); // 更新动画
                    renderer.render(scene, camera);
                };
                animate();
                // 模型动画
                startMove();//运动
            });
        }

        // 创建云朵粒子效果
        function createClouds() {
            const cloudGeometry = new THREE.BufferGeometry();
            const cloudCount = 1000;
            const positions = new Float32Array(cloudCount * 3);

            for (let i = 0; i < cloudCount * 3; i += 3) {
                positions[i] = (Math.random() - 0.5) * 20000;     // x
                positions[i + 1] = (Math.random() - 0.5) * 20000; // y
                positions[i + 2] = Math.random() * 5000 + 1000;   // z
            }

            cloudGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

            const cloudMaterial = new THREE.PointsMaterial({
                color: 0xffffff,
                size: 50,
                transparent: true,
                opacity: 0.6,
                blending: THREE.AdditiveBlending
            });

            const clouds = new THREE.Points(cloudGeometry, cloudMaterial);
            scene.add(clouds);
        }

        // 投影配置常量
        const SHADOW_CONFIG = {
            color: 0x001122,           // 深蓝黑色
            baseOpacity: 0.4,          // 基础透明度
            groundHeight: 1,           // 地面高度偏移
            maxHeight: 1000,           // 最大飞行高度
            minOpacity: 0.1,           // 最小透明度
            maxOpacity: 0.4,           // 最大透明度
            scaleRange: 0.5,           // 高度缩放范围
            animationSpeed: {
                opacity: 0.001,        // 透明度动画速度
                scale: 0.002           // 缩放动画速度
            },
            animationAmplitude: {
                opacity: 0.05,         // 透明度变化幅度
                scale: 0.02            // 缩放变化幅度
            }
        };

        // 创建飞机投影
        function createPlaneShadow() {
            try {
                // 创建优化的飞机形状投影几何体
                const shadowShape = createPlaneShape();
                const shadowGeometry = new THREE.ShapeGeometry(shadowShape);

                // 创建投影材质
                const shadowMaterial = new THREE.MeshBasicMaterial({
                    color: SHADOW_CONFIG.color,
                    transparent: true,
                    opacity: SHADOW_CONFIG.baseOpacity,
                    side: THREE.DoubleSide,
                    depthWrite: false,     // 优化渲染性能
                    blending: THREE.MultiplyBlending  // 更真实的阴影混合模式
                });

                // 创建投影网格
                planeShadow = new THREE.Mesh(shadowGeometry, shadowMaterial);

                // 设置投影的初始状态
                initializeShadowTransform();

                scene.add(planeShadow);
                console.log('飞机投影创建成功');

            } catch (error) {
                console.error('创建飞机投影失败:', error);
            }
        }

        // 创建飞机形状（提取为独立函数，提高可维护性）
        function createPlaneShape() {
            const shape = new THREE.Shape();

            // 使用数组定义飞机轮廓点，便于修改和维护
            const planePoints = [
                [0, 40],      // 机头
                [-15, 10],    // 左翼前缘
                [-40, 5],     // 左翼尖
                [-15, -5],    // 左翼后缘
                [-8, -35],    // 左尾翼
                [0, -30],     // 机尾
                [8, -35],     // 右尾翼
                [15, -5],     // 右翼后缘
                [40, 5],      // 右翼尖
                [15, 10],     // 右翼前缘
                [0, 40]       // 回到机头
            ];

            // 绘制形状
            shape.moveTo(planePoints[0][0], planePoints[0][1]);
            for (let i = 1; i < planePoints.length; i++) {
                shape.lineTo(planePoints[i][0], planePoints[i][1]);
            }

            return shape;
        }

        // 初始化投影变换
        function initializeShadowTransform() {
            planeShadow.rotation.x = -Math.PI / 2; // 平躺在地面
            planeShadow.position.z = SHADOW_CONFIG.groundHeight;
            planeShadow.castShadow = false;        // 投影本身不产生阴影
            planeShadow.receiveShadow = false;     // 投影不接收阴影
        }

        // 模型移动函数
        // 设置模型角度
        function setRotation(rotation) {
            var x = Math.PI / 180 * (rotation.x || 0);
            var y = Math.PI / 180 * (rotation.y || 0);
            var z = Math.PI / 180 * (rotation.z || 0);
            object.rotation.set(x, y, z);
        }
        // 设置模型位置
        function setPosition(object, lnglat) {
            // 对模型的经纬度进行转换*//*****************
            var position = customCoords.lngLatsToCoords([
                lnglat
            ])[0];
          ///  console.log(position);
            object.position.setX(position[0])           // 模型坐标
            object.position.setY(position[1])          // 模型坐标
            // console.log(position)
            //   map.setCenter(lnglat, true)
            object.position.setZ(200)            // 模型高度   此处为定义的模型高度

            // 更新飞机投影位置
            updatePlaneShadow(position);
        }

        // 性能优化：缓存时间计算
        let lastUpdateTime = 0;
        const UPDATE_INTERVAL = 16; // 约60FPS

        // 更新飞机投影位置（优化版本）
        function updatePlaneShadow(planePosition) {
            const startTime = ShadowDebugger.enableDebug ? performance.now() : 0;

            // 性能优化：限制更新频率
            const currentTime = Date.now();
            if (currentTime - lastUpdateTime < UPDATE_INTERVAL) {
                return;
            }
            lastUpdateTime = currentTime;

            if (!planeShadow || !planePosition || !object || !planeShadow.visible) {
                return;
            }

            try {
                // 更新投影位置
                updateShadowPosition(planePosition);

                // 更新投影旋转
                updateShadowRotation();

                // 更新投影视觉效果
                updateShadowVisualEffects();

                // 记录性能数据
                if (ShadowDebugger.enableDebug) {
                    ShadowDebugger.recordPerformance(startTime);
                }

            } catch (error) {
                console.error('更新投影失败:', error);
            }
        }

        // 更新投影位置
        function updateShadowPosition(planePosition) {
            planeShadow.position.set(
                planePosition[0],
                planePosition[1],
                SHADOW_CONFIG.groundHeight
            );
        }

        // 更新投影旋转
        function updateShadowRotation() {
            // 根据飞机航向角旋转投影
            planeShadow.rotation.z = -object.rotation.y;
        }

        // 更新投影视觉效果
        function updateShadowVisualEffects() {
            const planeHeight = object.position.z;

            // 计算基于高度的透明度和缩放
            const heightRatio = Math.min(planeHeight / SHADOW_CONFIG.maxHeight, 1);

            // 计算透明度
            const baseOpacity = SHADOW_CONFIG.maxOpacity -
                (heightRatio * (SHADOW_CONFIG.maxOpacity - SHADOW_CONFIG.minOpacity));

            // 添加动态效果
            const time = Date.now();
            const opacityVariation = Math.sin(time * SHADOW_CONFIG.animationSpeed.opacity) *
                SHADOW_CONFIG.animationAmplitude.opacity;
            const scaleVariation = 1 + Math.sin(time * SHADOW_CONFIG.animationSpeed.scale) *
                SHADOW_CONFIG.animationAmplitude.scale;

            // 应用透明度
            planeShadow.material.opacity = Math.max(
                SHADOW_CONFIG.minOpacity,
                baseOpacity + opacityVariation
            );

            // 应用缩放
            const heightScale = 1 + (heightRatio * SHADOW_CONFIG.scaleRange);
            const finalScale = heightScale * scaleVariation;
            planeShadow.scale.set(finalScale, finalScale, 1);
        }
        //定义模型位置编号
        function setPositionn(obj, lnglat) {
            var p = customCoords.lngLatsToCoords([
                lnglat
            ])[0];
            // 对模型的经纬度进行转换*//*****************
            console.log(p);
            obj.position.setX(p[0])           // 模型坐标
            obj.position.setY(p[1])          // 模型坐标
            obj.position.setZ(500)            // 模型高度   此处为定义的模型高度
        }
        // 设置模型角度
        function setAngle(angle) {
            var x = object.rotation.x;
            var z = object.rotation.z;
            var y = Math.PI / 180 * angle;
            object.rotation.set(x, y, z);
        }
        // 动画

        // 改变模型位置和角度
        var centerPoint = turf.point([108.983823, 34.246131]);
        var timer = 0;
        // 添加HUD数据更新函数
        function updateHUD() {
            if (object) {
                // 更新高度显示
                const altitude = Math.round(object.position.z);
                document.getElementById('altitude-display').textContent = altitude + ' m';
                
                // 更新速度显示（基于动画速度计算）
                const speed = Math.round(80 + 80 * Math.sin(increment / 10));
                document.getElementById('speed-display').textContent = speed + ' km/h';
                
                // 更新航向显示
                const heading = Math.round(timer % 360);
                document.getElementById('heading-display').textContent = heading + '°';
                
                // 更新坐标显示
                const currentPos = turf.transformTranslate(centerPoint, 0.3, timer).geometry.coordinates;
                document.getElementById('coordinates-display').textContent = 
                    currentPos[0].toFixed(6) + ', ' + currentPos[1].toFixed(6);
            }
        }

        function startMove() {
            if (timer > 360) {
                timer = 0;
            }
            requestAnimationFrame(function () {
                timer += 0.1;
                var pos = turf.transformTranslate(centerPoint, 0.3, timer).geometry.coordinates;
                var angle = timer;
                
                setPosition(object, pos);
                map.setCenter(pos, true);
                
                // 更新HUD显示
                updateHUD();
                
                map.render();
                startMove();
            });
        }
        function startPlane() {
            mixer = new THREE.AnimationMixer(object);
                const clip = THREE.AnimationClip.findByName(animations, 'hover');
                // const clip = THREE.AnimationClip.findByName( animations,'exploded_view' );
                // const clip = THREE.AnimationClip.findByName( animations,'StaticPose' );
                const action = mixer.clipAction(clip);
                action.play();
        }
        function stopPlane() {
            mixer.stopAllAction ();
        }
        function fenjie(){
            mixer = new THREE.AnimationMixer(object);
                const clip = THREE.AnimationClip.findByName(animations, 'exploded_view');
                // const clip = THREE.AnimationClip.findByName( animations,'exploded_view' );
                // const clip = THREE.AnimationClip.findByName( animations,'StaticPose' );
                const action = mixer.clipAction(clip);
                action.play();
        }

        // 投影控制工具函数
        const ShadowController = {
            // 切换投影显示/隐藏
            toggle() {
                if (planeShadow) {
                    planeShadow.visible = !planeShadow.visible;
                    console.log('投影显示状态:', planeShadow.visible ? '显示' : '隐藏');
                    return planeShadow.visible;
                }
                return false;
            },

            // 设置投影透明度
            setOpacity(opacity) {
                if (planeShadow && planeShadow.material) {
                    planeShadow.material.opacity = Math.max(0, Math.min(1, opacity));
                }
            },

            // 设置投影颜色
            setColor(color) {
                if (planeShadow && planeShadow.material) {
                    planeShadow.material.color.setHex(color);
                }
            },

            // 重置投影到默认状态
            reset() {
                if (planeShadow) {
                    planeShadow.material.opacity = SHADOW_CONFIG.baseOpacity;
                    planeShadow.material.color.setHex(SHADOW_CONFIG.color);
                    planeShadow.scale.set(1, 1, 1);
                    planeShadow.visible = true;
                }
            },

            // 获取投影状态信息
            getStatus() {
                if (!planeShadow) return null;

                return {
                    visible: planeShadow.visible,
                    opacity: planeShadow.material.opacity,
                    color: planeShadow.material.color.getHex(),
                    scale: {
                        x: planeShadow.scale.x,
                        y: planeShadow.scale.y,
                        z: planeShadow.scale.z
                    },
                    position: {
                        x: planeShadow.position.x,
                        y: planeShadow.position.y,
                        z: planeShadow.position.z
                    }
                };
            }
        };

        // 兼容性函数（保持原有接口）
        function toggleShadow() {
            return ShadowController.toggle();
        }

        // 调试和监控功能
        const ShadowDebugger = {
            // 启用调试模式
            enableDebug: false,

            // 性能监控
            performanceStats: {
                updateCount: 0,
                lastUpdateTime: 0,
                averageUpdateTime: 0
            },

            // 记录性能数据
            recordPerformance(startTime) {
                if (!this.enableDebug) return;

                const endTime = performance.now();
                const updateTime = endTime - startTime;

                this.performanceStats.updateCount++;
                this.performanceStats.lastUpdateTime = updateTime;
                this.performanceStats.averageUpdateTime =
                    (this.performanceStats.averageUpdateTime * (this.performanceStats.updateCount - 1) + updateTime) /
                    this.performanceStats.updateCount;
            },

            // 输出调试信息
            logStatus() {
                if (!this.enableDebug) return;

                console.group('飞机投影调试信息');
                console.log('投影状态:', ShadowController.getStatus());
                console.log('性能统计:', this.performanceStats);
                console.log('配置参数:', SHADOW_CONFIG);
                console.groupEnd();
            },

            // 切换调试模式
            toggleDebug() {
                this.enableDebug = !this.enableDebug;
                console.log('投影调试模式:', this.enableDebug ? '开启' : '关闭');
                if (this.enableDebug) {
                    // 每5秒输出一次状态信息
                    this.debugInterval = setInterval(() => this.logStatus(), 5000);
                } else {
                    if (this.debugInterval) {
                        clearInterval(this.debugInterval);
                    }
                }
            }
        };
        // 暴露调试功能到全局（便于控制台调试）
        window.ShadowController = ShadowController;
        window.ShadowDebugger = ShadowDebugger;
        setInterval(() => {
            mixer.uncacheAction ();//
         //   console.log(timer)
        }, 3000);
    </script>
    <!-- 在body中添加HUD界面 -->
    <div id="hud-overlay">
        <div class="hud-top">
            <div class="flight-status">
                <div class="status-item">
                    <span class="label">高度</span>
                    <span class="value" id="altitude-display">0 m</span>
                </div>
                <div class="status-item">
                    <span class="label">速度</span>
                    <span class="value" id="speed-display">0 km/h</span>
                </div>
                <div class="status-item">
                    <span class="label">航向</span>
                    <span class="value" id="heading-display">0°</span>
                </div>
            </div>
        </div>
        
        <div class="hud-center">
            <div class="crosshair">
                <div class="crosshair-h"></div>
                <div class="crosshair-v"></div>
            </div>
        </div>
        
        <div class="hud-bottom">
            <div class="coordinates">
                <span id="coordinates-display">108.983823, 34.246131</span>
            </div>
        </div>
    </div>
</body>
</html>
