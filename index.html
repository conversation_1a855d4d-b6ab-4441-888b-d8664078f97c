<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title> 3D地图可视化</title>
    <script src="./js/three.js"></script>
    <script src="./js/loaders/GLTFLoader.js"></script>
    <script src="./js/loaders/OBJLoader.js"></script>
    <script src="./js/turf.min.js"></script>
    <script src="./js/modifiers/CurveModifier.js"></script>
    <script
        src="https://webapi.amap.com/maps?v=2.0&key=66344304d66c1e59c6f3890c93e7d772&plugin=AMap.ControlBar,AMap.ToolBar,AMap.MouseTool"></script>
    <link rel="stylesheet" href="./css/main.css">

    <!--地图仪表展示  -->
    <script src="js/jquery-1.8.3.min.js"></script>

</head>

<body>
  
    <div id="menu">
        <!-- <button onclick="startPlane()">开始飞机</button>
        <hr>
        <button onclick="stopPlane()">停止飞机</button>
        <hr>
        <button onclick="fenjie()">分解飞机</button>
        <hr> -->
        <button onclick="toggleShadow()">切换投影显示</button>
        <hr>
        <button onclick="ShadowController.reset()">重置投影</button>
        <hr>
        <button onclick="ShadowDebugger.toggleDebug()">调试模式</button>
        <hr>
        <button onclick="ShadowController.setImage('./img/drone1.png')">无人机1</button>
        <hr>
        <button onclick="ShadowController.setImage('./img/drone2.png')">无人机2</button>
        <hr>
        <button onclick="setFlightMode('complex')">复杂轨迹</button>
        <hr>
        <button onclick="setFlightMode('simple')">简单轨迹</button>
        <hr>
        <button onclick="setViewAngle('top')">俯视视角</button>
        <hr>
        <button onclick="setViewAngle('side')">侧视视角</button>
        <hr>
        <button onclick="setViewAngle('follow')">跟随视角</button>
        <hr>
        <div style="color: white; font-size: 12px; margin-bottom: 10px;">
            💡 功能说明：<br>
            • 俯视视角：垂直向下查看投影效果<br>
            • 侧视视角：侧面观察飞行轨迹<br>
            • 跟随视角：相机跟随飞机移动<br>
            • 投影垂直于地面显示<br>
            • 支持复杂3D飞行轨迹<br>
            • 实时高度变化效果
        </div>
    </div>

    <div id="container">
    </div>
 


    <script type="text/javascript">
        
        // Update at 20Hz
        var increment = 0;
        console.warn = console.log;
        javascript: (function () {
            var script = document.createElement('script');
            script.onload = function () {
                var stats = new Stats();
                document.body.appendChild(stats.dom);
                requestAnimationFrame(function loop() {
                    stats.update();
                    requestAnimationFrame(loop)
                });
            };
            script.src = './js/stats.min.js';
            document.head.appendChild(script);
        })()
    </script>
    <script type="text/javascript">
        var map = new AMap.Map('container', {
            center: [108.983823, 34.246131],
            zooms: [16, 19],
            rotation: 0,           // 重置旋转角度
            wallColor: '#666',     //楼面周围颜色
            roofColor: '#eeee',    //楼的顶部颜色
            buildingAnimation: true,//楼块出现是否带动画
            zoom: 18,              // 稍微放大以获得更好的俯视效果
            viewMode: '3D',
            pitch: 0,             // 接近垂直俯视（90度为完全垂直）
        });
        var controlBar = new AMap.ControlBar({
            position: {
                right: '10px',
                top: '10px'
            }
        });
        controlBar.addTo(map);
        var toolBar = new AMap.ToolBar({
            position: {
                right: '40px',
                top: '110px'
            }
        });
        toolBar.addTo(map);
        const ACTION_SELECT = 1, ACTION_NONE = 0;
        const curveHandles = [];
        const mouse = new THREE.Vector2();
        var camera;
        var renderer;
        var scene;
        // 数据转换工具
        var customCoords = map.customCoords;
        var meshes = [];
        // 数据使用转换工具进行转换，这个操作必须要提前执行（在获取镜头参数 函数之前执行），否则将会获得一个错误信息。
        var mesh2, mesh3
        var data = customCoords.lngLatsToCoords([
            [108.982923, 34.246131],
            [108.984823, 34.246131],
            [108.986823, 34.246131],
        ]);
        var object, box, blades, mixer,animations;
        var planeShadow; // 飞机投影对象
        var shadowType = 'loading'; // 投影类型：'image', 'fallback', 'loading'
        var flightMode = 'complex'; // 飞行模式：'complex', 'simple'
        var viewAngle = 'top'; // 视角模式：'top', 'side', 'follow'

        var objPosition = [108.983823, 34.246131]
        var objPosition1 = [108.985823, 34.246131]
        // 创建 GL 图层
        var gllayer = new AMap.GLCustomLayer({
            // 图层的层级
            zIndex: 100,
            // heightFactor: 100, //2倍于默认高度，3D下有效
            // 初始化的操作，创建图层过程中执行一次。
            init: (gl) => {
                // 这里我们的地图模式是 3D，所以创建一个透视相机，相机的参数初始化可以随意设置，因为在 render 函数中，每一帧都需要同步相机参数，因此这里变得不那么重要。
                // 如果你需要 2D 地图（viewMode: '2D'），那么你需要创建一个正交相机
                camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 1000, 1 << 30);
                renderer = new THREE.WebGLRenderer({
                    context: gl,  // 地图的 gl 上下文
                    // alpha: true,
                    // antialias: true,
                    // canvas: gl.canvas,
                });
                // 自动清空画布这里必须设置为 false，否则地图底图将无法显示
                renderer.autoClear = false;
                scene = new THREE.Scene();
                // 环境光照和平行光
                var aLight = new THREE.AmbientLight(0xffffff, 0.6);
                var dLight = new THREE.DirectionalLight(0xffffff, 1);
                dLight.position.set(-1000, -100, 900);
                scene.add(dLight);
                scene.add(aLight);
                initGltf()
            },
            render: () => {
                // 这里必须执行！！重新设置 three 的 gl 上下文状态。
                renderer.resetState();
                // 重新设置图层的渲染中心点，将模型等物体的渲染中心点重置
                // 否则和 LOCA 可视化等多个图层能力使用的时候会出现物体位置偏移的问题
                customCoords.setCenter([116.271363, 39.992414]);
                var { near, far, fov, up, lookAt, position } = customCoords.getCameraParams();
                // 2D 地图下使用的正交相机
                // var { near, far, top, bottom, left, right, position, rotation } = customCoords.getCameraParams();
                // 这里的顺序不能颠倒，否则可能会出现绘制卡顿的效果。

                camera.near = near;
                camera.far = far;
                camera.fov = fov;
                camera.position.set(...position);
                camera.up.set(...up);
                camera.lookAt(...lookAt);
                camera.updateProjectionMatrix();
                // 2D 地图使用的正交相机参数赋值
                // camera.top = top;
                // camera.bottom = bottom;
                // camera.left = left;
                // camera.right = right;
                // camera.position.set(...position);
                // camera.updateProjectionMatrix();
                renderer.render(scene, camera);
                // 这里必须执行！！重新设置 three 的 gl 上下文状态。
                renderer.resetState();
            },
        });
        map.add(gllayer);
        // 初始化模型
        function initGltf() {
            
        

            // 创建飞机投影
            createPlaneShadow();

            var loader = new THREE.GLTFLoader();
            loader.load('./gltf/dj/scene.gltf', (gltf) => {
                console.log(gltf);
                object = gltf.scene;
                animations = gltf.animations;
                console.log(animations)
                // const mixer = new THREE.AnimationMixer( object );
               // numAnimations = animations.length;
                mixer = new THREE.AnimationMixer(object);
                // const clip = THREE.AnimationClip.findByName(animations, 'hover');
                // // const clip = THREE.AnimationClip.findByName( animations,'exploded_view' );
                // // const clip = THREE.AnimationClip.findByName( animations,'StaticPose' );
                // const action = mixer.clipAction(clip);
                // action.play();
                mixer.clipAction(gltf.animations[0]).play();
                object.scale.set(1000, 1000, 1000)    //模型大小缩放
                setRotation({
                    x: 90, y: 100, z: 0
                });
                scene.add(object);

                // 渲染循环
                const animate = function () {
                    requestAnimationFrame(animate);
                    mixer.update(0.01); // 更新动画
                    renderer.render(scene, camera);
                };
                animate();
                // 模型动画
                startMove();//运动
            });
        }

        

        // 投影配置常量
        const SHADOW_CONFIG = {
            imagePath: './img/drone1.png', // 无人机图标路径
            baseOpacity: 0.8,          // 基础透明度（提高以在俯视角度下更清晰）
            groundHeight: 3,           // 地面高度偏移（稍微抬高避免z-fighting）
            maxHeight: 800,            // 最大飞行高度（调整以获得更好的缩放效果）
            minOpacity: 0.4,           // 最小透明度（提高最小值）
            maxOpacity: 0.95,          // 最大透明度（提高最大值）
            baseSize: 140,             // 基础尺寸（增大以在俯视角度下更清晰）
            minScale: 0.15,            // 最小缩放比例（远处更小）
            maxScale: 2.5,             // 最大缩放比例（近处更大）
            scaleRange: 2.35,          // 高度缩放范围（增大变化幅度）
            animationSpeed: {
                opacity: 0.0006,       // 透明度动画速度（稍微减慢）
                scale: 0.001           // 缩放动画速度（稍微减慢）
            },
            animationAmplitude: {
                opacity: 0.015,        // 透明度变化幅度（减小以保持稳定）
                scale: 0.006           // 缩放变化幅度（减小以保持稳定）
            }
        };

        // 创建飞机投影
        function createPlaneShadow() {
            try {
                // 加载无人机图标纹理
                const textureLoader = new THREE.TextureLoader();
                console.log('开始加载投影图标:', SHADOW_CONFIG.imagePath);
                textureLoader.load(
                    SHADOW_CONFIG.imagePath,
                    function(texture) {
                        // 纹理加载成功
                        console.log('✅ 无人机投影图标加载成功，尺寸:', texture.image.width + 'x' + texture.image.height);
                        createShadowWithTexture(texture);
                    },
                    function(progress) {
                        // 加载进度
                        if (progress.total > 0) {
                            console.log('📥 投影图标加载进度:', Math.round(progress.loaded / progress.total * 100) + '%');
                        }
                    },
                    function(error) {
                        // 加载失败，使用备用方案
                        console.warn('❌ 投影图标加载失败，使用备用方案:', error);
                        console.log('尝试的路径:', SHADOW_CONFIG.imagePath);
                        createFallbackShadow();
                    }
                );

            } catch (error) {
                console.error('创建飞机投影失败:', error);
                createFallbackShadow();
            }
        }

        // 使用纹理创建投影
        function createShadowWithTexture(texture) {
            // 创建平面几何体
            const shadowGeometry = new THREE.PlaneGeometry(
                SHADOW_CONFIG.baseSize,
                SHADOW_CONFIG.baseSize
            );

            // 创建材质，使用图片纹理
            const shadowMaterial = new THREE.MeshBasicMaterial({
                map: texture,
                transparent: true,
                opacity: SHADOW_CONFIG.baseOpacity,
                side: THREE.DoubleSide,
                depthWrite: false,
                alphaTest: 0.1,  // 透明度测试，去除完全透明的像素
            });

            // 创建投影网格
            planeShadow = new THREE.Mesh(shadowGeometry, shadowMaterial);

            // 设置投影的初始状态
            initializeShadowTransform();

            scene.add(planeShadow);
        }

        // 备用投影方案（当图片加载失败时使用）
        function createFallbackShadow() {
            const shadowGeometry = new THREE.CircleGeometry(SHADOW_CONFIG.baseSize / 2, 32);
            const shadowMaterial = new THREE.MeshBasicMaterial({
                color: 0x000000,
                transparent: true,
                opacity: SHADOW_CONFIG.baseOpacity,
                side: THREE.DoubleSide,
                depthWrite: false,
            });

            planeShadow = new THREE.Mesh(shadowGeometry, shadowMaterial);
            initializeShadowTransform();
            
            scene.add(planeShadow);
            shadowType = 'fallback';
            console.log('⚠️ 使用备用投影方案（圆形阴影）');
        }



        // 初始化投影变换
        function initializeShadowTransform() {
            // 让投影平躺在地面上（垂直于地面）
            planeShadow.rotation.x = 10; // 绕X轴旋转-90度，让投影平躺
            planeShadow.rotation.y = 0;            // 重置Y轴旋转
            planeShadow.rotation.z = 0;            // 重置Z轴旋转
            planeShadow.position.x = SHADOW_CONFIG.groundHeight;
            planeShadow.castShadow = false;        // 投影本身不产生阴影
            planeShadow.receiveShadow = false;     // 投影不接收阴影
            console.log(planeShadow.rotation)
              console.log(planeShadow.position)
            console.log('投影已设置为垂直于地面');
        }

        // 模型移动函数
        // 设置模型角度
        function setRotation(rotation) {
            var x = Math.PI / 180 * (rotation.x || 0);
            var y = Math.PI / 180 * (rotation.y || 0);
            var z = Math.PI / 180 * (rotation.z || 0);
            object.rotation.set(x, y, z);
        }
        // 设置模型位置（增强版，支持动态高度）
        function setPosition(object, lnglat, height) {
            // 对模型的经纬度进行转换
            var position = customCoords.lngLatsToCoords([
                lnglat
            ])[0];

            object.position.setX(position[0]);           // 模型X坐标
            object.position.setY(position[1]);          // 模型Y坐标

            // 使用传入的高度，如果没有传入则使用默认值
            const finalHeight = height !== undefined ? height : 200;
            object.position.setZ(finalHeight);          // 模型高度

            // 更新飞机投影位置
            updatePlaneShadow(position);
        }

        // 性能优化：缓存时间计算
        let lastUpdateTime = 0;
        const UPDATE_INTERVAL = 16; // 约60FPS

        // 更新飞机投影位置（优化版本）
        function updatePlaneShadow(planePosition) {
            const startTime = ShadowDebugger.enableDebug ? performance.now() : 0;

            // 性能优化：限制更新频率
            const currentTime = Date.now();
            if (currentTime - lastUpdateTime < UPDATE_INTERVAL) {
                return;
            }
            lastUpdateTime = currentTime;

            if (!planeShadow || !planePosition || !object || !planeShadow.visible) {
                return;
            }

            try {
                // 更新投影位置
                updateShadowPosition(planePosition);

                // 更新投影旋转
                updateShadowRotation();

                // 更新投影视觉效果
                updateShadowVisualEffects();

                // 记录性能数据
                if (ShadowDebugger.enableDebug) {
                    ShadowDebugger.recordPerformance(startTime);
                }

            } catch (error) {
                console.error('更新投影失败:', error);
            }
        }

        // 更新投影位置
        function updateShadowPosition(planePosition) {
            planeShadow.position.set(
                planePosition[0],
                planePosition[1],
                SHADOW_CONFIG.groundHeight
            );
        }

        // 更新投影旋转
        function updateShadowRotation() {
            // 保持投影平躺在地面上，只根据飞机航向角在Z轴旋转
            planeShadow.rotation.x = -Math.PI / 2; // 始终保持平躺
            planeShadow.rotation.z = -object.rotation.y; // 根据飞机航向旋转
        }

        // 更新投影视觉效果（近大远小效果）
        function updateShadowVisualEffects() {
            const planeHeight = object.position.z;

            // 计算高度比例（0-1之间）
            const heightRatio = Math.min(planeHeight / SHADOW_CONFIG.maxHeight, 1);

            // 近大远小效果：高度越高，投影越小
            // 使用反比例关系，让效果更明显
            const distanceScale = SHADOW_CONFIG.maxScale - (heightRatio * (SHADOW_CONFIG.maxScale - SHADOW_CONFIG.minScale));

            // 透明度效果：高度越高，投影越淡
            const baseOpacity = SHADOW_CONFIG.maxOpacity -
                (heightRatio * (SHADOW_CONFIG.maxOpacity - SHADOW_CONFIG.minOpacity));

            // 添加轻微的动态效果
            const time = Date.now();
            const opacityVariation = Math.sin(time * SHADOW_CONFIG.animationSpeed.opacity) *
                SHADOW_CONFIG.animationAmplitude.opacity;
            const scaleVariation = 1 + Math.sin(time * SHADOW_CONFIG.animationSpeed.scale) *
                SHADOW_CONFIG.animationAmplitude.scale;

            // 应用透明度
            planeShadow.material.opacity = Math.max(
                SHADOW_CONFIG.minOpacity,
                baseOpacity + opacityVariation
            );

            // 应用近大远小的缩放效果
            const finalScale = distanceScale * scaleVariation;
            planeShadow.scale.set(finalScale, finalScale, 1);

            // 调试信息（可选）
            if (ShadowDebugger && ShadowDebugger.enableDebug) {
                if (Math.random() < 0.008) { // 0.8%的概率输出调试信息，避免刷屏
                    console.log(`投影效果 - 高度: ${planeHeight.toFixed(1)}m, 缩放: ${finalScale.toFixed(3)}, 透明度: ${planeShadow.material.opacity.toFixed(3)}, 高度比例: ${heightRatio.toFixed(3)}`);
                }
            }
        }
        //定义模型位置编号
        function setPositionn(obj, lnglat) {
            var p = customCoords.lngLatsToCoords([
                lnglat
            ])[0];
            // 对模型的经纬度进行转换*//*****************
            console.log(p);
            obj.position.setX(p[0])           // 模型坐标
            obj.position.setY(p[1])          // 模型坐标
            obj.position.setZ(500)            // 模型高度   此处为定义的模型高度
        }
        // 设置模型角度
        function setAngle(angle) {
            var x = object.rotation.x;
            var z = object.rotation.z;
            var y = Math.PI / 180 * angle;
            object.rotation.set(x, y, z);
        }
        // 动画

        // 改变模型位置和角度
        var centerPoint = turf.point([108.983823, 34.246131]);
        var timer = 0;
        // 添加HUD数据更新函数
        function updateHUD() {
            if (object) {
                // 更新高度显示（实时动态高度）
                const altitude = Math.round(object.position.z);
                const altitudeElement = document.getElementById('altitude-display');
                if (altitudeElement) {
                    altitudeElement.textContent = altitude + ' m';
                }

                // 更新速度显示（基于飞行轨迹计算）
                const speed = Math.round(85 + 25 * Math.sin(timer * Math.PI / 180));
                const speedElement = document.getElementById('speed-display');
                if (speedElement) {
                    speedElement.textContent = speed + ' km/h';
                }

                // 更新航向显示
                const heading = Math.round(timer % 360);
                const headingElement = document.getElementById('heading-display');
                if (headingElement) {
                    headingElement.textContent = heading + '°';
                }

                // 更新坐标显示
                const currentPos = turf.transformTranslate(centerPoint, 0.4, timer).geometry.coordinates;
                const coordsElement = document.getElementById('coordinates-display');
                if (coordsElement) {
                    coordsElement.textContent =
                        currentPos[0].toFixed(6) + ', ' + currentPos[1].toFixed(6);
                }
            }
        }

        // 飞行模式控制
        function setFlightMode(mode) {
            flightMode = mode;
            console.log('飞行模式已切换为:', mode === 'complex' ? '复杂轨迹' : '简单轨迹');
        }

        // 视角控制
        function setViewAngle(angle) {
            viewAngle = angle;

            switch(angle) {
                case 'top':
                    // 俯视视角：接近垂直向下
                    map.setPitch(85);
                    map.setRotation(0);
                    map.setZoom(18);
                    console.log('已切换到俯视视角');
                    break;

                case 'side':
                    // 侧视视角：侧面观察
                    map.setPitch(45);
                    map.setRotation(45);
                    map.setZoom(17);
                    console.log('已切换到侧视视角');
                    break;

                case 'follow':
                    // 跟随视角：动态跟随
                    map.setPitch(60);
                    map.setRotation(0);
                    map.setZoom(17.5);
                    console.log('已切换到跟随视角');
                    break;
            }
        }

        // 相机跟随控制
        function updateCameraFollow(pos, height) {
            switch(viewAngle) {
                case 'top':
                    // 俯视视角：相机始终在飞机正上方
                    map.setCenter(pos, true);
                    break;

                case 'side':
                    // 侧视视角：相机保持一定距离侧面跟随
                    map.setCenter(pos, true);
                    break;

                case 'follow':
                    // 跟随视角：动态调整视角跟随飞机
                    map.setCenter(pos, true);

                    // 根据飞机高度动态调整俯仰角
                    const dynamicPitch = 60 + (height - 200) * 0.05; // 高度影响俯仰角
                    const clampedPitch = Math.max(30, Math.min(80, dynamicPitch));
                    map.setPitch(clampedPitch);

                    // 根据飞行方向微调旋转角度
                    const dynamicRotation = timer * 0.1; // 缓慢旋转跟随
                    map.setRotation(dynamicRotation % 360);
                    break;
            }
        }

        // 计算飞行高度
        function calculateFlightHeight() {
            if (flightMode === 'complex') {
                // 复杂模式：多层波动
                const baseHeight = 180;
                const heightVariation1 = Math.sin(timer * Math.PI / 180) * 120;      // 主要波动
                const heightVariation2 = Math.sin(timer * 3 * Math.PI / 180) * 40;   // 次要波动
                const heightVariation3 = Math.sin(timer * 0.5 * Math.PI / 180) * 60; // 缓慢大幅波动
                const heightVariation4 = Math.sin(timer * 5 * Math.PI / 180) * 15;   // 快速小波动

                const dynamicHeight = baseHeight + heightVariation1 + heightVariation2 + heightVariation3 + heightVariation4;
                return Math.max(50, Math.min(650, dynamicHeight));
            } else {
                // 简单模式：单一波动
                const baseHeight = 200;
                const heightVariation = Math.sin(timer * Math.PI / 180) * 80;
                return Math.max(120, Math.min(280, baseHeight + heightVariation));
            }
        }

        function startMove() {
            if (timer > 360) {
                timer = 0;
            }
            requestAnimationFrame(function () {
                timer += 0.12; // 调整速度以获得更好的视觉效果

                // 基础圆形轨迹
                const radius = flightMode === 'complex' ? 0.45 : 0.35;
                var pos = turf.transformTranslate(centerPoint, radius, timer).geometry.coordinates;

                // 计算动态高度
                const finalHeight = calculateFlightHeight();

                // 设置飞机位置（包含动态高度）
                setPosition(object, pos, finalHeight);

                // 根据视角模式调整相机跟随
                updateCameraFollow(pos, finalHeight);

                // 更新HUD显示
                updateHUD();

                // 调试信息（降低频率）
                if (Math.random() < 0.005) { // 0.5%概率输出
                    console.log(`飞行状态 - 模式: ${flightMode}, 角度: ${timer.toFixed(1)}°, 高度: ${finalHeight.toFixed(1)}m`);
                }

                map.render();
                startMove();
            });
        }
        function startPlane() {
            mixer = new THREE.AnimationMixer(object);
                const clip = THREE.AnimationClip.findByName(animations, 'hover');
                // const clip = THREE.AnimationClip.findByName( animations,'exploded_view' );
                // const clip = THREE.AnimationClip.findByName( animations,'StaticPose' );
                const action = mixer.clipAction(clip);
                action.play();
        }
        function stopPlane() {
            mixer.stopAllAction ();
        }
        function fenjie(){
            mixer = new THREE.AnimationMixer(object);
                const clip = THREE.AnimationClip.findByName(animations, 'exploded_view');
                // const clip = THREE.AnimationClip.findByName( animations,'exploded_view' );
                // const clip = THREE.AnimationClip.findByName( animations,'StaticPose' );
                const action = mixer.clipAction(clip);
                action.play();
        }

        // 投影控制工具函数
        const ShadowController = {
            // 切换投影显示/隐藏
            toggle() {
                if (planeShadow) {
                    planeShadow.visible = !planeShadow.visible;
                    console.log('投影显示状态:', planeShadow.visible ? '显示' : '隐藏');
                    return planeShadow.visible;
                }
                return false;
            },

            // 设置投影透明度
            setOpacity(opacity) {
                if (planeShadow && planeShadow.material) {
                    planeShadow.material.opacity = Math.max(0, Math.min(1, opacity));
                }
            },

            // 设置投影颜色（对图片纹理进行着色）
            setColor(color) {
                if (planeShadow && planeShadow.material) {
                    planeShadow.material.color.setHex(color);
                }
            },

            // 设置投影图片（动态更换图标）
            setImage(imagePath) {
                if (planeShadow && planeShadow.material) {
                    const textureLoader = new THREE.TextureLoader();
                    textureLoader.load(imagePath, function(texture) {
                        planeShadow.material.map = texture;
                        planeShadow.material.needsUpdate = true;
                        console.log('投影图标已更换为:', imagePath);
                    }, undefined, function(error) {
                        console.error('更换投影图标失败:', error);
                    });
                }
            },

            // 重置投影到默认状态
            reset() {
                if (planeShadow) {
                    planeShadow.material.opacity = SHADOW_CONFIG.baseOpacity;
                    planeShadow.material.color.setHex(0xffffff); // 白色，不影响纹理原色
                    planeShadow.scale.set(1, 1, 1);
                    planeShadow.visible = true;

                    // 重新加载默认图标
                    this.setImage(SHADOW_CONFIG.imagePath);
                    console.log('投影已重置到默认状态');
                }
            },

            // 获取投影状态信息
            getStatus() {
                if (!planeShadow) return { type: shadowType, loaded: false };

                return {
                    type: shadowType,
                    loaded: true,
                    visible: planeShadow.visible,
                    opacity: planeShadow.material.opacity,
                    color: planeShadow.material.color.getHex(),
                    hasTexture: !!planeShadow.material.map,
                    textureSize: planeShadow.material.map ?
                        `${planeShadow.material.map.image.width}x${planeShadow.material.map.image.height}` : 'N/A',
                    scale: {
                        x: planeShadow.scale.x,
                        y: planeShadow.scale.y,
                        z: planeShadow.scale.z
                    },
                    position: {
                        x: planeShadow.position.x,
                        y: planeShadow.position.y,
                        z: planeShadow.position.z
                    }
                };
            }
        };

        // 兼容性函数（保持原有接口）
        function toggleShadow() {
            return ShadowController.toggle();
        }

        // 调试和监控功能
        const ShadowDebugger = {
            // 启用调试模式
            enableDebug: false,

            // 性能监控
            performanceStats: {
                updateCount: 0,
                lastUpdateTime: 0,
                averageUpdateTime: 0
            },

            // 记录性能数据
            recordPerformance(startTime) {
                if (!this.enableDebug) return;

                const endTime = performance.now();
                const updateTime = endTime - startTime;

                this.performanceStats.updateCount++;
                this.performanceStats.lastUpdateTime = updateTime;
                this.performanceStats.averageUpdateTime =
                    (this.performanceStats.averageUpdateTime * (this.performanceStats.updateCount - 1) + updateTime) /
                    this.performanceStats.updateCount;
            },

            // 输出调试信息
            logStatus() {
                if (!this.enableDebug) return;

                console.group('飞机投影调试信息');
                console.log('投影状态:', ShadowController.getStatus());
                console.log('飞行模式:', flightMode);
                console.log('视角模式:', viewAngle);
                console.log('地图状态:', {
                    pitch: map.getPitch(),
                    rotation: map.getRotation(),
                    zoom: map.getZoom()
                });
                console.log('性能统计:', this.performanceStats);
                console.log('配置参数:', SHADOW_CONFIG);
                console.groupEnd();
            },

            // 切换调试模式
            toggleDebug() {
                this.enableDebug = !this.enableDebug;
                console.log('投影调试模式:', this.enableDebug ? '开启' : '关闭');
                if (this.enableDebug) {
                    // 每5秒输出一次状态信息
                    this.debugInterval = setInterval(() => this.logStatus(), 5000);
                } else {
                    if (this.debugInterval) {
                        clearInterval(this.debugInterval);
                    }
                }
            }
        };
        // 暴露调试功能到全局（便于控制台调试）
        window.ShadowController = ShadowController;
        window.ShadowDebugger = ShadowDebugger;
        setInterval(() => {
            mixer.uncacheAction ();//
         //   console.log(timer)
        }, 3000);
    </script>
    <!-- 在body中添加HUD界面 -->
    <div id="hud-overlay">
        <div class="hud-top">
            <div class="flight-status">
                <div class="status-item">
                    <span class="label">高度</span>
                    <span class="value" id="altitude-display">0 m</span>
                </div>
                <div class="status-item">
                    <span class="label">速度</span>
                    <span class="value" id="speed-display">0 km/h</span>
                </div>
                <div class="status-item">
                    <span class="label">航向</span>
                    <span class="value" id="heading-display">0°</span>
                </div>
            </div>
        </div>
        
        <div class="hud-center">
            <div class="crosshair">
                <div class="crosshair-h"></div>
                <div class="crosshair-v"></div>
            </div>
        </div>
        
        <div class="hud-bottom">
            <div class="coordinates">
                <span id="coordinates-display">108.983823, 34.246131</span>
            </div>
        </div>
    </div>
</body>
</html>
