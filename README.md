项目概述
主要功能：3D地图可视化，集成了高德地图和Three.js
核心技术：Three.js、高德地图API、飞行仪表显示
主要组件
1. 地图集成
使用高德地图API v2.0作为底层地图
创建了AMap.GLCustomLayer自定义GL图层
支持3D视角，包括俯仰角、旋转等
2. Three.js加载器
项目包含多种3D模型加载器：

GLTFLoader.js - GLTF模型加载
OBJLoader.js - OBJ模型加载
DRACOLoader.js - Draco压缩模型
FBXLoader.js - FBX模型
KTX2Loader.js - KTX2纹理
MMDLoader.js - MMD模型
3. 飞行仪表系统
<script src="js/jquery.flightindicators.js"></script>
包含完整的飞行仪表：

姿态指示器（attitude）
空速表（airspeed）
高度表（altimeter）
转弯协调器（turn_coordinator）
航向指示器（heading）
垂直速度表（variometer）
4. 3D场景控制
var object, box, blades, mixer, animations;
var objPosition = [108.983823, 34.246131]
支持飞机模型的加载、动画控制和分解功能。

这个项目似乎是一个飞行模拟或航空可视化应用，结合了地理信息系统和3D图形渲染技术。